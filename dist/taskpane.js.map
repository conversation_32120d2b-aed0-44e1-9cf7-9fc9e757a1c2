{"version": 3, "file": "taskpane.js", "mappings": ";;;;;;;;;;;;;;;;UAAA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;UAEA;UACA;;;;;WCzBA;WACA;WACA;WACA;WACA,GAAG;WACH;WACA;WACA,CAAC,I;;;;;WCPD,8CAA8C,yD;;;;;WCA9C;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D,E;;;;;WCNA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA,kC;;;;;WClBA;;WAEA;WACA;WACA;WACA;WACA;WACA;;WAEA;;WAEA;;WAEA;;WAEA;;WAEA;;WAEA;;WAEA,oB;;;;;;;;;;0BCpBA,uKAAAA,CAAA,EAAAC,CAAA,EAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,WAAA,8BAAAC,EAAAN,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAE,CAAA,QAAAC,CAAA,GAAAL,CAAA,IAAAA,CAAA,CAAAM,SAAA,YAAAC,SAAA,GAAAP,CAAA,GAAAO,SAAA,EAAAC,CAAA,GAAAC,MAAA,CAAAC,MAAA,CAAAL,CAAA,CAAAC,SAAA,UAAAK,mBAAA,CAAAH,CAAA,uBAAAV,CAAA,EAAAE,CAAA,EAAAE,CAAA,QAAAE,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAI,CAAA,MAAAC,CAAA,GAAAX,CAAA,QAAAY,CAAA,OAAAC,CAAA,KAAAF,CAAA,KAAAb,CAAA,KAAAgB,CAAA,EAAApB,CAAA,EAAAqB,CAAA,EAAAC,CAAA,EAAAN,CAAA,EAAAM,CAAA,CAAAC,IAAA,CAAAvB,CAAA,MAAAsB,CAAA,WAAAA,EAAArB,CAAA,EAAAC,CAAA,WAAAM,CAAA,GAAAP,CAAA,EAAAQ,CAAA,MAAAG,CAAA,GAAAZ,CAAA,EAAAmB,CAAA,CAAAf,CAAA,GAAAF,CAAA,EAAAmB,CAAA,gBAAAC,EAAApB,CAAA,EAAAE,CAAA,SAAAK,CAAA,GAAAP,CAAA,EAAAU,CAAA,GAAAR,CAAA,EAAAH,CAAA,OAAAiB,CAAA,IAAAF,CAAA,KAAAV,CAAA,IAAAL,CAAA,GAAAgB,CAAA,CAAAO,MAAA,EAAAvB,CAAA,UAAAK,CAAA,EAAAE,CAAA,GAAAS,CAAA,CAAAhB,CAAA,GAAAqB,CAAA,GAAAH,CAAA,CAAAF,CAAA,EAAAQ,CAAA,GAAAjB,CAAA,KAAAN,CAAA,QAAAI,CAAA,GAAAmB,CAAA,KAAArB,CAAA,MAAAQ,CAAA,GAAAJ,CAAA,EAAAC,CAAA,GAAAD,CAAA,YAAAC,CAAA,WAAAD,CAAA,MAAAA,CAAA,MAAAR,CAAA,IAAAQ,CAAA,OAAAc,CAAA,MAAAhB,CAAA,GAAAJ,CAAA,QAAAoB,CAAA,GAAAd,CAAA,QAAAC,CAAA,MAAAU,CAAA,CAAAC,CAAA,GAAAhB,CAAA,EAAAe,CAAA,CAAAf,CAAA,GAAAI,CAAA,OAAAc,CAAA,GAAAG,CAAA,KAAAnB,CAAA,GAAAJ,CAAA,QAAAM,CAAA,MAAAJ,CAAA,IAAAA,CAAA,GAAAqB,CAAA,MAAAjB,CAAA,MAAAN,CAAA,EAAAM,CAAA,MAAAJ,CAAA,EAAAe,CAAA,CAAAf,CAAA,GAAAqB,CAAA,EAAAhB,CAAA,cAAAH,CAAA,IAAAJ,CAAA,aAAAmB,CAAA,QAAAH,CAAA,OAAAd,CAAA,qBAAAE,CAAA,EAAAW,CAAA,EAAAQ,CAAA,QAAAT,CAAA,YAAAU,SAAA,uCAAAR,CAAA,UAAAD,CAAA,IAAAK,CAAA,CAAAL,CAAA,EAAAQ,CAAA,GAAAhB,CAAA,GAAAQ,CAAA,EAAAL,CAAA,GAAAa,CAAA,GAAAxB,CAAA,GAAAQ,CAAA,OAAAT,CAAA,GAAAY,CAAA,MAAAM,CAAA,KAAAV,CAAA,KAAAC,CAAA,GAAAA,CAAA,QAAAA,CAAA,SAAAU,CAAA,CAAAf,CAAA,QAAAkB,CAAA,CAAAb,CAAA,EAAAG,CAAA,KAAAO,CAAA,CAAAf,CAAA,GAAAQ,CAAA,GAAAO,CAAA,CAAAC,CAAA,GAAAR,CAAA,aAAAI,CAAA,MAAAR,CAAA,QAAAC,CAAA,KAAAH,CAAA,YAAAL,CAAA,GAAAO,CAAA,CAAAF,CAAA,WAAAL,CAAA,GAAAA,CAAA,CAAA0B,IAAA,CAAAnB,CAAA,EAAAI,CAAA,UAAAc,SAAA,2CAAAzB,CAAA,CAAA2B,IAAA,SAAA3B,CAAA,EAAAW,CAAA,GAAAX,CAAA,CAAA4B,KAAA,EAAApB,CAAA,SAAAA,CAAA,oBAAAA,CAAA,KAAAR,CAAA,GAAAO,CAAA,CAAAsB,MAAA,KAAA7B,CAAA,CAAA0B,IAAA,CAAAnB,CAAA,GAAAC,CAAA,SAAAG,CAAA,GAAAc,SAAA,uCAAApB,CAAA,gBAAAG,CAAA,OAAAD,CAAA,GAAAR,CAAA,cAAAC,CAAA,IAAAiB,CAAA,GAAAC,CAAA,CAAAf,CAAA,QAAAQ,CAAA,GAAAV,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,EAAAe,CAAA,OAAAE,CAAA,kBAAApB,CAAA,IAAAO,CAAA,GAAAR,CAAA,EAAAS,CAAA,MAAAG,CAAA,GAAAX,CAAA,cAAAe,CAAA,mBAAAa,KAAA,EAAA5B,CAAA,EAAA2B,IAAA,EAAAV,CAAA,SAAAhB,CAAA,EAAAI,CAAA,EAAAE,CAAA,QAAAI,CAAA,QAAAS,CAAA,gBAAAV,UAAA,cAAAoB,kBAAA,cAAAC,2BAAA,KAAA/B,CAAA,GAAAY,MAAA,CAAAoB,cAAA,MAAAxB,CAAA,MAAAL,CAAA,IAAAH,CAAA,CAAAA,CAAA,IAAAG,CAAA,SAAAW,mBAAA,CAAAd,CAAA,OAAAG,CAAA,iCAAAH,CAAA,GAAAW,CAAA,GAAAoB,0BAAA,CAAAtB,SAAA,GAAAC,SAAA,CAAAD,SAAA,GAAAG,MAAA,CAAAC,MAAA,CAAAL,CAAA,YAAAO,EAAAhB,CAAA,WAAAa,MAAA,CAAAqB,cAAA,GAAArB,MAAA,CAAAqB,cAAA,CAAAlC,CAAA,EAAAgC,0BAAA,KAAAhC,CAAA,CAAAmC,SAAA,GAAAH,0BAAA,EAAAjB,mBAAA,CAAAf,CAAA,EAAAM,CAAA,yBAAAN,CAAA,CAAAU,SAAA,GAAAG,MAAA,CAAAC,MAAA,CAAAF,CAAA,GAAAZ,CAAA,WAAA+B,iBAAA,CAAArB,SAAA,GAAAsB,0BAAA,EAAAjB,mBAAA,CAAAH,CAAA,iBAAAoB,0BAAA,GAAAjB,mBAAA,CAAAiB,0BAAA,iBAAAD,iBAAA,GAAAA,iBAAA,CAAAK,WAAA,wBAAArB,mBAAA,CAAAiB,0BAAA,EAAA1B,CAAA,wBAAAS,mBAAA,CAAAH,CAAA,GAAAG,mBAAA,CAAAH,CAAA,EAAAN,CAAA,gBAAAS,mBAAA,CAAAH,CAAA,EAAAR,CAAA,iCAAAW,mBAAA,CAAAH,CAAA,8DAAAyB,YAAA,YAAAA,aAAA,aAAAC,CAAA,EAAA9B,CAAA,EAAA+B,CAAA,EAAAvB,CAAA;AAAA,SAAAD,oBAAAf,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAH,CAAA,QAAAO,CAAA,GAAAK,MAAA,CAAA2B,cAAA,QAAAhC,CAAA,uBAAAR,CAAA,IAAAQ,CAAA,QAAAO,mBAAA,YAAA0B,mBAAAzC,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAH,CAAA,aAAAK,EAAAJ,CAAA,EAAAE,CAAA,IAAAW,mBAAA,CAAAf,CAAA,EAAAE,CAAA,YAAAF,CAAA,gBAAA0C,OAAA,CAAAxC,CAAA,EAAAE,CAAA,EAAAJ,CAAA,SAAAE,CAAA,GAAAM,CAAA,GAAAA,CAAA,CAAAR,CAAA,EAAAE,CAAA,IAAA2B,KAAA,EAAAzB,CAAA,EAAAuC,UAAA,GAAA1C,CAAA,EAAA2C,YAAA,GAAA3C,CAAA,EAAA4C,QAAA,GAAA5C,CAAA,MAAAD,CAAA,CAAAE,CAAA,IAAAE,CAAA,IAAAE,CAAA,aAAAA,CAAA,cAAAA,CAAA,mBAAAS,mBAAA,CAAAf,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAH,CAAA;AAAA,SAAA6C,mBAAA1C,CAAA,EAAAH,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAI,CAAA,EAAAe,CAAA,EAAAZ,CAAA,cAAAD,CAAA,GAAAJ,CAAA,CAAAiB,CAAA,EAAAZ,CAAA,GAAAG,CAAA,GAAAJ,CAAA,CAAAqB,KAAA,WAAAzB,CAAA,gBAAAJ,CAAA,CAAAI,CAAA,KAAAI,CAAA,CAAAoB,IAAA,GAAA3B,CAAA,CAAAW,CAAA,IAAAmC,OAAA,CAAAC,OAAA,CAAApC,CAAA,EAAAqC,IAAA,CAAA/C,CAAA,EAAAI,CAAA;AAAA,SAAA4C,kBAAA9C,CAAA,6BAAAH,CAAA,SAAAD,CAAA,GAAAmD,SAAA,aAAAJ,OAAA,WAAA7C,CAAA,EAAAI,CAAA,QAAAe,CAAA,GAAAjB,CAAA,CAAAgD,KAAA,CAAAnD,CAAA,EAAAD,CAAA,YAAAqD,MAAAjD,CAAA,IAAA0C,kBAAA,CAAAzB,CAAA,EAAAnB,CAAA,EAAAI,CAAA,EAAA+C,KAAA,EAAAC,MAAA,UAAAlD,CAAA,cAAAkD,OAAAlD,CAAA,IAAA0C,kBAAA,CAAAzB,CAAA,EAAAnB,CAAA,EAAAI,CAAA,EAAA+C,KAAA,EAAAC,MAAA,WAAAlD,CAAA,KAAAiD,KAAA;AADAE,MAAM,CAACC,OAAO,CAAC,UAACC,IAAI,EAAK;EACvBC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEF,IAAI,CAAC;EAE1C,IAAIA,IAAI,CAACG,IAAI,KAAKL,MAAM,CAACM,QAAQ,CAACC,KAAK,EAAE;IAAA,IAAAC,qBAAA,EAAAC,sBAAA;IACvC;IACA,IAAMC,WAAW,GAAGC,QAAQ,CAACC,cAAc,CAAC,cAAc,CAAC;IAC3D,IAAMC,OAAO,GAAGF,QAAQ,CAACC,cAAc,CAAC,UAAU,CAAC;IAEnD,IAAIF,WAAW,EAAEA,WAAW,CAACI,KAAK,CAACC,OAAO,GAAG,MAAM;IACnD,IAAIF,OAAO,EAAEA,OAAO,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;;IAE3C;IACA,CAAAP,qBAAA,GAAAG,QAAQ,CAACC,cAAc,CAAC,YAAY,CAAC,cAAAJ,qBAAA,eAArCA,qBAAA,CAAuCQ,gBAAgB,CAAC,OAAO,EAAEC,iBAAiB,CAAC;IACnF,CAAAR,sBAAA,GAAAE,QAAQ,CAACC,cAAc,CAAC,YAAY,CAAC,cAAAH,sBAAA,eAArCA,sBAAA,CAAuCO,gBAAgB,CAAC,OAAO,EAAEE,gBAAgB,CAAC;IAElFC,YAAY,CAAC,kCAAkC,CAAC;EAClD;AACF,CAAC,CAAC;AAEF,SAASF,iBAAiBA,CAAA,EAAG;EAAA,IAAAG,sBAAA;EAC3B,IAAMC,MAAM,IAAAD,sBAAA,GAAIT,QAAQ,CAACC,cAAc,CAAC,QAAQ,CAAC,cAAAQ,sBAAA,uBAAlCA,sBAAA,CAAyD9C,KAAK;EAE7E,IAAI,CAAC+C,MAAM,EAAE;IACXF,YAAY,CAAC,6BAA6B,CAAC;IAC3C;EACF;;EAEA;EACAG,YAAY,CAACC,OAAO,CAAC,cAAc,EAAEF,MAAM,CAAC;EAC5CF,YAAY,CAAC,sBAAsB,CAAC;AACtC;AAAC,SAEcD,gBAAgBA,CAAA;EAAA,OAAAM,iBAAA,CAAA3B,KAAA,OAAAD,SAAA;AAAA;AAAA,SAAA4B,kBAAA;EAAAA,iBAAA,GAAA7B,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAA/B,SAAAyC,SAAA;IAAA,IAAAC,EAAA;IAAA,OAAA5C,YAAA,GAAAC,CAAA,WAAA4C,SAAA;MAAA,kBAAAA,SAAA,CAAAjE,CAAA,GAAAiE,SAAA,CAAA9E,CAAA;QAAA;UAAA8E,SAAA,CAAAjE,CAAA;UAAAiE,SAAA,CAAA9E,CAAA;UAAA,OAEU0D,KAAK,CAACqB,GAAG;YAAA,IAAAC,IAAA,GAAAlC,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAAC,SAAA8C,QAAOC,OAAO;cAAA,IAAAC,MAAA,EAAAC,UAAA,EAAAC,EAAA,EAAAC,WAAA,EAAAC,IAAA;cAAA,OAAAtD,YAAA,GAAAC,CAAA,WAAAsD,QAAA;gBAAA,kBAAAA,QAAA,CAAAxF,CAAA;kBAAA;oBACtBmF,MAAM,GAAGD,OAAO,CAACO,QAAQ,CAACC,UAAU;oBAEpCN,UAAU,GAAG,CACjB,wBAAwB,EACxB,kBAAkB,EAClB,uBAAuB,EACvB,iBAAiB,EACjB,gBAAgB,CACjB;oBAED,KAAAC,EAAA,MAAAC,WAAA,GAAmBF,UAAU,EAAAC,EAAA,GAAAC,WAAA,CAAAlE,MAAA,EAAAiE,EAAA,IAAE;sBAApBE,IAAI,GAAAD,WAAA,CAAAD,EAAA;sBACbF,MAAM,CAACQ,GAAG,CAACJ,IAAI,CAAC;oBAClB;oBAACC,QAAA,CAAAxF,CAAA;oBAAA,OAEKkF,OAAO,CAACU,IAAI,CAAC,CAAC;kBAAA;oBACpBtB,YAAY,YAAAuB,MAAA,CAAYT,UAAU,CAAChE,MAAM,0BAAuB,CAAC;kBAAC;oBAAA,OAAAoE,QAAA,CAAAvE,CAAA;gBAAA;cAAA,GAAAgE,OAAA;YAAA,CACnE;YAAA,iBAAAa,EAAA;cAAA,OAAAd,IAAA,CAAAhC,KAAA,OAAAD,SAAA;YAAA;UAAA,IAAC;QAAA;UAAA+B,SAAA,CAAA9E,CAAA;UAAA;QAAA;UAAA8E,SAAA,CAAAjE,CAAA;UAAAgE,EAAA,GAAAC,SAAA,CAAA9D,CAAA;UAEFsD,YAAY,WAAAuB,MAAA,CAAAhB,EAAA,CAAkB,CAAC;QAAC;UAAA,OAAAC,SAAA,CAAA7D,CAAA;MAAA;IAAA,GAAA2D,QAAA;EAAA,CAEnC;EAAA,OAAAD,iBAAA,CAAA3B,KAAA,OAAAD,SAAA;AAAA;AAED,SAASuB,YAAYA,CAACyB,OAAe,EAAE;EACrC,IAAMC,QAAQ,GAAGlC,QAAQ,CAACC,cAAc,CAAC,QAAQ,CAAC;EAClD,IAAIiC,QAAQ,EAAE;IACZA,QAAQ,CAACC,WAAW,GAAGF,OAAO;EAChC;AACF,C;;;;;;;;;AC9DA;AACA,yCAAyC,kHAAiC;AAC1E;AACA,+pDAA+pD;AAC/pD;AACA,+DAAe,IAAI,E", "sources": ["webpack://office-addin-taskpane-sso/webpack/bootstrap", "webpack://office-addin-taskpane-sso/webpack/runtime/global", "webpack://office-addin-taskpane-sso/webpack/runtime/hasOwnProperty shorthand", "webpack://office-addin-taskpane-sso/webpack/runtime/make namespace object", "webpack://office-addin-taskpane-sso/webpack/runtime/publicPath", "webpack://office-addin-taskpane-sso/webpack/runtime/jsonp chunk loading", "webpack://office-addin-taskpane-sso/./src/taskpane/taskpane.ts", "webpack://office-addin-taskpane-sso/./src/taskpane/taskpane.html"], "sourcesContent": ["// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "// define __esModule on exports\n__webpack_require__.r = function(exports) {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "var scriptUrl;\nif (__webpack_require__.g.importScripts) scriptUrl = __webpack_require__.g.location + \"\";\nvar document = __webpack_require__.g.document;\nif (!scriptUrl && document) {\n\tif (document.currentScript && document.currentScript.tagName.toUpperCase() === 'SCRIPT')\n\t\tscriptUrl = document.currentScript.src;\n\tif (!scriptUrl) {\n\t\tvar scripts = document.getElementsByTagName(\"script\");\n\t\tif(scripts.length) {\n\t\t\tvar i = scripts.length - 1;\n\t\t\twhile (i > -1 && (!scriptUrl || !/^http(s?):/.test(scriptUrl))) scriptUrl = scripts[i--].src;\n\t\t}\n\t}\n}\n// When supporting browsers where an automatic publicPath is not supported you must specify an output.publicPath manually via configuration\n// or pass an empty string (\"\") and set the __webpack_public_path__ variable from your code to use your own logic.\nif (!scriptUrl) throw new Error(\"Automatic publicPath is not supported in this browser\");\nscriptUrl = scriptUrl.replace(/^blob:/, \"\").replace(/#.*$/, \"\").replace(/\\?.*$/, \"\").replace(/\\/[^\\/]+$/, \"/\");\n__webpack_require__.p = scriptUrl;", "__webpack_require__.b = document.baseURI || self.location.href;\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t\"taskpane\": 0\n};\n\n// no chunk on demand loading\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n// no on chunks loaded\n\n// no jsonp function", "Office.onReady((info) => {\n  console.log(\"Office.onReady called\", info);\n  \n  if (info.host === Office.HostType.Excel) {\n    // Hide sideload message, show app\n    const sideloadMsg = document.getElementById(\"sideload-msg\");\n    const appBody = document.getElementById(\"app-body\");\n    \n    if (sideloadMsg) sideloadMsg.style.display = \"none\";\n    if (appBody) appBody.style.display = \"flex\";\n    \n    // Setup button handlers\n    document.getElementById(\"saveConfig\")?.addEventListener(\"click\", saveConfiguration);\n    document.getElementById(\"initSheets\")?.addEventListener(\"click\", initializeSheets);\n    \n    updateStatus(\"RubiRecruit loaded successfully!\");\n  }\n});\n\nfunction saveConfiguration() {\n  const apiKey = (document.getElementById(\"apiKey\") as HTMLInputElement)?.value;\n  \n  if (!apiKey) {\n    updateStatus(\"Please enter Claude API key\");\n    return;\n  }\n  \n  // Store in local storage for now\n  localStorage.setItem(\"claude<PERSON>pi<PERSON><PERSON>\", apiKey);\n  updateStatus(\"Configuration saved!\");\n}\n\nasync function initializeSheets() {\n  try {\n    await Excel.run(async (context) => {\n      const sheets = context.workbook.worksheets;\n      \n      const sheetNames = [\n        \"Position Configuration\",\n        \"Document Mapping\",\n        \"Candidate Evaluations\",\n        \"Company Metrics\",\n        \"Processing Log\"\n      ];\n      \n      for (const name of sheetNames) {\n        sheets.add(name);\n      }\n      \n      await context.sync();\n      updateStatus(`Created ${sheetNames.length} sheets successfully!`);\n    });\n  } catch (error) {\n    updateStatus(`Error: ${error}`);\n  }\n}\n\nfunction updateStatus(message: string) {\n  const statusEl = document.getElementById(\"status\");\n  if (statusEl) {\n    statusEl.textContent = message;\n  }\n}", "// Imports\nvar ___HTML_LOADER_IMPORT_0___ = new URL(\"./taskpane.css\", import.meta.url);\n// Module\nvar code = \"<!DOCTYPE html>\\n<html>\\n<head>\\n    <meta charset=\\\"UTF-8\\\" />\\n    <meta http-equiv=\\\"X-UA-Compatible\\\" content=\\\"IE=Edge\\\" />\\n    <meta name=\\\"viewport\\\" content=\\\"width=device-width, initial-scale=1\\\">\\n    <title>RubiRecruit - Excel</title>\\n\\n    <\" + \"script src=\\\"https://appsforoffice.microsoft.com/lib/1/hosted/office.js\\\" type=\\\"text/javascript\\\"><\" + \"/script>\\n    <link rel=\\\"stylesheet\\\" href=\\\"https://res-1.cdn.office.net/files/fabric-cdn-prod_20230815.002/office-ui-fabric-core/11.1.0/css/fabric.min.css\\\"/>\\n    <link href=\\\"\" + ___HTML_LOADER_IMPORT_0___ + \"\\\" rel=\\\"stylesheet\\\" type=\\\"text/css\\\" />\\n</head>\\n\\n<body class=\\\"ms-font-m ms-Fabric\\\">\\n    <header class=\\\"rubi-header\\\">\\n        <div class=\\\"logo\\\">💎</div>\\n        <h1>RubiRecruit</h1>\\n        <div class=\\\"subtitle\\\">AI-Powered Recruitment</div>\\n    </header>\\n    \\n    <main class=\\\"rubi-main\\\">\\n        <div id=\\\"setup-section\\\" class=\\\"section\\\">\\n            <h2>Step 1: Initial Setup</h2>\\n            <div class=\\\"form-group\\\">\\n                <label>Claude API Key:</label>\\n                <input type=\\\"password\\\" id=\\\"apiKey\\\" placeholder=\\\"sk-ant-...\\\" />\\n            </div>\\n            <div class=\\\"form-group\\\">\\n                <label>SharePoint Site URL:</label>\\n                <input type=\\\"text\\\" id=\\\"siteUrl\\\" placeholder=\\\"https://yourcompany.sharepoint.com/sites/HR\\\" />\\n            </div>\\n            <button id=\\\"setupButton\\\" class=\\\"ms-Button ms-Button--primary\\\">\\n                <span class=\\\"ms-Button-label\\\">Initialize System</span>\\n            </button>\\n        </div>\\n\\n        <div id=\\\"progress-section\\\" class=\\\"section\\\" style=\\\"display:none;\\\">\\n            <h2>Progress</h2>\\n            <div class=\\\"step-list\\\">\\n                <div class=\\\"step\\\" id=\\\"step1\\\">✅ Setup Complete</div>\\n                <div class=\\\"step\\\" id=\\\"step2\\\">⭕ Load Position Description</div>\\n                <div class=\\\"step\\\" id=\\\"step3\\\">⭕ Generate Rubric</div>\\n                <div class=\\\"step\\\" id=\\\"step4\\\">⭕ Process Candidates</div>\\n            </div>\\n            \\n            <button id=\\\"loadPositionBtn\\\" class=\\\"ms-Button\\\">Load Position</button>\\n            <button id=\\\"generateRubricBtn\\\" class=\\\"ms-Button\\\" disabled>Generate Rubric</button>\\n            <button id=\\\"processCandidatesBtn\\\" class=\\\"ms-Button\\\" disabled>Process Candidates</button>\\n        </div>\\n        \\n        <div id=\\\"status-message\\\" class=\\\"status\\\"></div>\\n    </main>\\n</body>\\n</html>\";\n// Exports\nexport default code;"], "names": ["e", "t", "r", "Symbol", "n", "iterator", "o", "toStringTag", "i", "c", "prototype", "Generator", "u", "Object", "create", "_regeneratorDefine2", "f", "p", "y", "G", "v", "a", "d", "bind", "length", "l", "TypeError", "call", "done", "value", "return", "GeneratorFunction", "GeneratorFunctionPrototype", "getPrototypeOf", "setPrototypeOf", "__proto__", "displayName", "_regenerator", "w", "m", "defineProperty", "_regeneratorDefine", "_invoke", "enumerable", "configurable", "writable", "asyncGeneratorStep", "Promise", "resolve", "then", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "Office", "onReady", "info", "console", "log", "host", "HostType", "Excel", "_document$getElementB", "_document$getElementB2", "sideloadMsg", "document", "getElementById", "appBody", "style", "display", "addEventListener", "saveConfiguration", "initializeSheets", "updateStatus", "_document$getElementB3", "<PERSON><PERSON><PERSON><PERSON>", "localStorage", "setItem", "_initializeSheets", "_callee2", "_t", "_context2", "run", "_ref", "_callee", "context", "sheets", "sheetNames", "_i", "_sheetNames", "name", "_context", "workbook", "worksheets", "add", "sync", "concat", "_x", "message", "statusEl", "textContent"], "sourceRoot": ""}