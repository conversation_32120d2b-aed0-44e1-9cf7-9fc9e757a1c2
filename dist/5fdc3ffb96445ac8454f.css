html, body {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    font-family: 'Segoe UI', sans-serif;
}

.rubi-header {
    background: linear-gradient(135deg, #8B1538 0%, #A91B60 100%);
    color: white;
    padding: 20px;
    text-align: center;
}

.logo {
    font-size: 48px;
    margin-bottom: 10px;
}

.rubi-header h1 {
    margin: 0;
    font-size: 24px;
}

.subtitle {
    font-size: 14px;
    opacity: 0.9;
}

.rubi-main {
    padding: 20px;
}

.section {
    background: #f5f5f5;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
}

.form-group input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.ms-Button {
    margin: 5px 0;
    width: 100%;
}

.step-list {
    margin: 15px 0;
}

.step {
    padding: 8px;
    background: white;
    margin: 5px 0;
    border-radius: 4px;
}

.status {
    padding: 10px;
    margin-top: 15px;
    border-radius: 4px;
    text-align: center;
}

.status.info {
    background: #e3f2fd;
    color: #1976d2;
}

.status.success {
    background: #e8f5e9;
    color: #2e7d32;
}

.status.error {
    background: #ffebee;
    color: #c62828;
}