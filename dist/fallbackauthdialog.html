<!-- Copyright (c) Microsoft. All rights reserved. Licensed under the MIT license. See full license in root of repo. -->
<!-- This file shows how to create an empty HTML file to load an AAD login script. -->

<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=Edge" />

    <!-- Office JavaScript API -->
    <script type="text/javascript" src="https://ajax.microsoft.com/ajax/4.0/MicrosoftAjax.js"></script>
    <script type="text/javascript" src="https://alcdn.msauth.net/browser/2.26.0/js/msal-browser.min.js"></script>
    <script type="text/javascript" src="https://appsforoffice.microsoft.com/lib/1.1/hosted/office.debug.js"></script>

<script defer src="polyfill.js"></script><script defer src="fallbackauthdialog.js"></script></head>

<body>

</body>
</html>