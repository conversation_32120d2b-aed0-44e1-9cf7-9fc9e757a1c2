{"version": 3, "file": "commands.js", "mappings": ";;UAAA;UACA;;;;;WCDA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA,E;;;;;WCPA,8CAA8C,yD;;;;;WCA9C;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D,E;;;;;;;;;;;;ACNA;AACA;AACA;AACA;;AAEA;;AAEAA,MAAM,CAACC,OAAO,CAAC,YAAM;EACnB;AAAA,CACD,CAAC;;AAEF;AACA;AACA;AACA;AACO,SAASC,MAAMA,CAACC,KAAiC,EAAE;EACxD,IAAMC,OAA0C,GAAG;IACjDC,IAAI,EAAEL,MAAM,CAACM,YAAY,CAACC,2BAA2B,CAACC,oBAAoB;IAC1EJ,OAAO,EAAE,mBAAmB;IAC5BK,IAAI,EAAE,YAAY;IAClBC,UAAU,EAAE;EACd,CAAC;;EAED;EACAV,MAAM,CAACW,OAAO,CAACC,OAAO,CAACC,IAAI,CAACC,oBAAoB,CAACC,YAAY,CAAC,+BAA+B,EAAEX,OAAO,CAAC;;EAEvG;EACAD,KAAK,CAACa,SAAS,CAAC,CAAC;AACnB;;AAEA;AACAhB,MAAM,CAACiB,OAAO,CAACC,SAAS,CAAC,QAAQ,EAAEhB,MAAM,CAAC,C", "sources": ["webpack://office-addin-taskpane-sso/webpack/bootstrap", "webpack://office-addin-taskpane-sso/webpack/runtime/define property getters", "webpack://office-addin-taskpane-sso/webpack/runtime/hasOwnProperty shorthand", "webpack://office-addin-taskpane-sso/webpack/runtime/make namespace object", "webpack://office-addin-taskpane-sso/./src/commands/commands.ts"], "sourcesContent": ["// The require scope\nvar __webpack_require__ = {};\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "// define __esModule on exports\n__webpack_require__.r = function(exports) {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "/*\n * Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n * See LICENSE in the project root for license information.\n */\n\n/* global Office */\n\nOffice.onReady(() => {\n  // If needed, Office.js is ready to be called.\n});\n\n/**\n * Shows a notification when the add-in command is executed.\n * @param event\n */\nexport function action(event: Office.AddinCommands.Event) {\n  const message: Office.NotificationMessageDetails = {\n    type: Office.MailboxEnums.ItemNotificationMessageType.InformationalMessage,\n    message: \"Performed action.\",\n    icon: \"Icon.80x80\",\n    persistent: true,\n  };\n\n  // Show a notification message.\n  Office.context.mailbox.item.notificationMessages.replaceAsync(\"ActionPerformanceNotification\", message);\n\n  // Be sure to indicate when the add-in command function is complete.\n  event.completed();\n}\n\n// Register the function with Office.\nOffice.actions.associate(\"action\", action);\n"], "names": ["Office", "onReady", "action", "event", "message", "type", "MailboxEnums", "ItemNotificationMessageType", "InformationalMessage", "icon", "persistent", "context", "mailbox", "item", "notificationMessages", "replaceAsync", "completed", "actions", "associate"], "sourceRoot": ""}