<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=Edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>RubiRecruit - Excel</title>

    <script src="https://appsforoffice.microsoft.com/lib/1/hosted/office.js" type="text/javascript"></script>
    <link rel="stylesheet" href="https://res-1.cdn.office.net/files/fabric-cdn-prod_20230815.002/office-ui-fabric-core/11.1.0/css/fabric.min.css"/>
    <link href="5fdc3ffb96445ac8454f.css" rel="stylesheet" type="text/css" />
<script defer src="polyfill.js"></script><script defer src="taskpane.js"></script></head>

<body class="ms-font-m ms-Fabric">
    <header class="rubi-header">
        <div class="logo">💎</div>
        <h1>RubiRecruit</h1>
        <div class="subtitle">AI-Powered Recruitment</div>
    </header>
    
    <main class="rubi-main">
        <div id="setup-section" class="section">
            <h2>Step 1: Initial Setup</h2>
            <div class="form-group">
                <label>Claude API Key:</label>
                <input type="password" id="apiKey" placeholder="sk-ant-..." />
            </div>
            <div class="form-group">
                <label>SharePoint Site URL:</label>
                <input type="text" id="siteUrl" placeholder="https://yourcompany.sharepoint.com/sites/HR" />
            </div>
            <button id="setupButton" class="ms-Button ms-Button--primary">
                <span class="ms-Button-label">Initialize System</span>
            </button>
        </div>

        <div id="progress-section" class="section" style="display:none;">
            <h2>Progress</h2>
            <div class="step-list">
                <div class="step" id="step1">✅ Setup Complete</div>
                <div class="step" id="step2">⭕ Load Position Description</div>
                <div class="step" id="step3">⭕ Generate Rubric</div>
                <div class="step" id="step4">⭕ Process Candidates</div>
            </div>
            
            <button id="loadPositionBtn" class="ms-Button">Load Position</button>
            <button id="generateRubricBtn" class="ms-Button" disabled>Generate Rubric</button>
            <button id="processCandidatesBtn" class="ms-Button" disabled>Process Candidates</button>
        </div>
        
        <div id="status-message" class="status"></div>
    </main>
</body>
</html>