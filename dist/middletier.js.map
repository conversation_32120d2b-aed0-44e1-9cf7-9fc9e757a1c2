{"version": 3, "file": "middletier.js", "mappings": ";;;;;;;;;;;;;;;;;;;;0BACA,uKAAAA,CAAA,EAAAC,CAAA,EAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,WAAA,8BAAAC,EAAAN,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAE,CAAA,QAAAC,CAAA,GAAAL,CAAA,IAAAA,CAAA,CAAAM,SAAA,YAAAC,SAAA,GAAAP,CAAA,GAAAO,SAAA,EAAAC,CAAA,GAAAC,MAAA,CAAAC,MAAA,CAAAL,CAAA,CAAAC,SAAA,UAAAK,mBAAA,CAAAH,CAAA,uBAAAV,CAAA,EAAAE,CAAA,EAAAE,CAAA,QAAAE,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAI,CAAA,MAAAC,CAAA,GAAAX,CAAA,QAAAY,CAAA,OAAAC,CAAA,KAAAF,CAAA,KAAAb,CAAA,KAAAgB,CAAA,EAAApB,CAAA,EAAAqB,CAAA,EAAAC,CAAA,EAAAN,CAAA,EAAAM,CAAA,CAAAC,IAAA,CAAAvB,CAAA,MAAAsB,CAAA,WAAAA,EAAArB,CAAA,EAAAC,CAAA,WAAAM,CAAA,GAAAP,CAAA,EAAAQ,CAAA,MAAAG,CAAA,GAAAZ,CAAA,EAAAmB,CAAA,CAAAf,CAAA,GAAAF,CAAA,EAAAmB,CAAA,gBAAAC,EAAApB,CAAA,EAAAE,CAAA,SAAAK,CAAA,GAAAP,CAAA,EAAAU,CAAA,GAAAR,CAAA,EAAAH,CAAA,OAAAiB,CAAA,IAAAF,CAAA,KAAAV,CAAA,IAAAL,CAAA,GAAAgB,CAAA,CAAAO,MAAA,EAAAvB,CAAA,UAAAK,CAAA,EAAAE,CAAA,GAAAS,CAAA,CAAAhB,CAAA,GAAAqB,CAAA,GAAAH,CAAA,CAAAF,CAAA,EAAAQ,CAAA,GAAAjB,CAAA,KAAAN,CAAA,QAAAI,CAAA,GAAAmB,CAAA,KAAArB,CAAA,MAAAQ,CAAA,GAAAJ,CAAA,EAAAC,CAAA,GAAAD,CAAA,YAAAC,CAAA,WAAAD,CAAA,MAAAA,CAAA,MAAAR,CAAA,IAAAQ,CAAA,OAAAc,CAAA,MAAAhB,CAAA,GAAAJ,CAAA,QAAAoB,CAAA,GAAAd,CAAA,QAAAC,CAAA,MAAAU,CAAA,CAAAC,CAAA,GAAAhB,CAAA,EAAAe,CAAA,CAAAf,CAAA,GAAAI,CAAA,OAAAc,CAAA,GAAAG,CAAA,KAAAnB,CAAA,GAAAJ,CAAA,QAAAM,CAAA,MAAAJ,CAAA,IAAAA,CAAA,GAAAqB,CAAA,MAAAjB,CAAA,MAAAN,CAAA,EAAAM,CAAA,MAAAJ,CAAA,EAAAe,CAAA,CAAAf,CAAA,GAAAqB,CAAA,EAAAhB,CAAA,cAAAH,CAAA,IAAAJ,CAAA,aAAAmB,CAAA,QAAAH,CAAA,OAAAd,CAAA,qBAAAE,CAAA,EAAAW,CAAA,EAAAQ,CAAA,QAAAT,CAAA,YAAAU,SAAA,uCAAAR,CAAA,UAAAD,CAAA,IAAAK,CAAA,CAAAL,CAAA,EAAAQ,CAAA,GAAAhB,CAAA,GAAAQ,CAAA,EAAAL,CAAA,GAAAa,CAAA,GAAAxB,CAAA,GAAAQ,CAAA,OAAAT,CAAA,GAAAY,CAAA,MAAAM,CAAA,KAAAV,CAAA,KAAAC,CAAA,GAAAA,CAAA,QAAAA,CAAA,SAAAU,CAAA,CAAAf,CAAA,QAAAkB,CAAA,CAAAb,CAAA,EAAAG,CAAA,KAAAO,CAAA,CAAAf,CAAA,GAAAQ,CAAA,GAAAO,CAAA,CAAAC,CAAA,GAAAR,CAAA,aAAAI,CAAA,MAAAR,CAAA,QAAAC,CAAA,KAAAH,CAAA,YAAAL,CAAA,GAAAO,CAAA,CAAAF,CAAA,WAAAL,CAAA,GAAAA,CAAA,CAAA0B,IAAA,CAAAnB,CAAA,EAAAI,CAAA,UAAAc,SAAA,2CAAAzB,CAAA,CAAA2B,IAAA,SAAA3B,CAAA,EAAAW,CAAA,GAAAX,CAAA,CAAA4B,KAAA,EAAApB,CAAA,SAAAA,CAAA,oBAAAA,CAAA,KAAAR,CAAA,GAAAO,CAAA,CAAAsB,MAAA,KAAA7B,CAAA,CAAA0B,IAAA,CAAAnB,CAAA,GAAAC,CAAA,SAAAG,CAAA,GAAAc,SAAA,uCAAApB,CAAA,gBAAAG,CAAA,OAAAD,CAAA,GAAAR,CAAA,cAAAC,CAAA,IAAAiB,CAAA,GAAAC,CAAA,CAAAf,CAAA,QAAAQ,CAAA,GAAAV,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,EAAAe,CAAA,OAAAE,CAAA,kBAAApB,CAAA,IAAAO,CAAA,GAAAR,CAAA,EAAAS,CAAA,MAAAG,CAAA,GAAAX,CAAA,cAAAe,CAAA,mBAAAa,KAAA,EAAA5B,CAAA,EAAA2B,IAAA,EAAAV,CAAA,SAAAhB,CAAA,EAAAI,CAAA,EAAAE,CAAA,QAAAI,CAAA,QAAAS,CAAA,gBAAAV,UAAA,cAAAoB,kBAAA,cAAAC,2BAAA,KAAA/B,CAAA,GAAAY,MAAA,CAAAoB,cAAA,MAAAxB,CAAA,MAAAL,CAAA,IAAAH,CAAA,CAAAA,CAAA,IAAAG,CAAA,SAAAW,mBAAA,CAAAd,CAAA,OAAAG,CAAA,iCAAAH,CAAA,GAAAW,CAAA,GAAAoB,0BAAA,CAAAtB,SAAA,GAAAC,SAAA,CAAAD,SAAA,GAAAG,MAAA,CAAAC,MAAA,CAAAL,CAAA,YAAAO,EAAAhB,CAAA,WAAAa,MAAA,CAAAqB,cAAA,GAAArB,MAAA,CAAAqB,cAAA,CAAAlC,CAAA,EAAAgC,0BAAA,KAAAhC,CAAA,CAAAmC,SAAA,GAAAH,0BAAA,EAAAjB,mBAAA,CAAAf,CAAA,EAAAM,CAAA,yBAAAN,CAAA,CAAAU,SAAA,GAAAG,MAAA,CAAAC,MAAA,CAAAF,CAAA,GAAAZ,CAAA,WAAA+B,iBAAA,CAAArB,SAAA,GAAAsB,0BAAA,EAAAjB,mBAAA,CAAAH,CAAA,iBAAAoB,0BAAA,GAAAjB,mBAAA,CAAAiB,0BAAA,iBAAAD,iBAAA,GAAAA,iBAAA,CAAAK,WAAA,wBAAArB,mBAAA,CAAAiB,0BAAA,EAAA1B,CAAA,wBAAAS,mBAAA,CAAAH,CAAA,GAAAG,mBAAA,CAAAH,CAAA,EAAAN,CAAA,gBAAAS,mBAAA,CAAAH,CAAA,EAAAR,CAAA,iCAAAW,mBAAA,CAAAH,CAAA,8DAAAyB,YAAA,YAAAA,aAAA,aAAAC,CAAA,EAAA9B,CAAA,EAAA+B,CAAA,EAAAvB,CAAA;AAAA,SAAAD,oBAAAf,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAH,CAAA,QAAAO,CAAA,GAAAK,MAAA,CAAA2B,cAAA,QAAAhC,CAAA,uBAAAR,CAAA,IAAAQ,CAAA,QAAAO,mBAAA,YAAA0B,mBAAAzC,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAH,CAAA,aAAAK,EAAAJ,CAAA,EAAAE,CAAA,IAAAW,mBAAA,CAAAf,CAAA,EAAAE,CAAA,YAAAF,CAAA,gBAAA0C,OAAA,CAAAxC,CAAA,EAAAE,CAAA,EAAAJ,CAAA,SAAAE,CAAA,GAAAM,CAAA,GAAAA,CAAA,CAAAR,CAAA,EAAAE,CAAA,IAAA2B,KAAA,EAAAzB,CAAA,EAAAuC,UAAA,GAAA1C,CAAA,EAAA2C,YAAA,GAAA3C,CAAA,EAAA4C,QAAA,GAAA5C,CAAA,MAAAD,CAAA,CAAAE,CAAA,IAAAE,CAAA,IAAAE,CAAA,aAAAA,CAAA,cAAAA,CAAA,mBAAAS,mBAAA,CAAAf,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAH,CAAA;AAAA,SAAA6C,mBAAA1C,CAAA,EAAAH,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAI,CAAA,EAAAe,CAAA,EAAAZ,CAAA,cAAAD,CAAA,GAAAJ,CAAA,CAAAiB,CAAA,EAAAZ,CAAA,GAAAG,CAAA,GAAAJ,CAAA,CAAAqB,KAAA,WAAAzB,CAAA,gBAAAJ,CAAA,CAAAI,CAAA,KAAAI,CAAA,CAAAoB,IAAA,GAAA3B,CAAA,CAAAW,CAAA,IAAAmC,OAAA,CAAAC,OAAA,CAAApC,CAAA,EAAAqC,IAAA,CAAA/C,CAAA,EAAAI,CAAA;AAAA,SAAA4C,kBAAA9C,CAAA,6BAAAH,CAAA,SAAAD,CAAA,GAAAmD,SAAA,aAAAJ,OAAA,WAAA7C,CAAA,EAAAI,CAAA,QAAAe,CAAA,GAAAjB,CAAA,CAAAgD,KAAA,CAAAnD,CAAA,EAAAD,CAAA,YAAAqD,MAAAjD,CAAA,IAAA0C,kBAAA,CAAAzB,CAAA,EAAAnB,CAAA,EAAAI,CAAA,EAAA+C,KAAA,EAAAC,MAAA,UAAAlD,CAAA,cAAAkD,OAAAlD,CAAA,IAAA0C,kBAAA,CAAAzB,CAAA,EAAAnB,CAAA,EAAAI,CAAA,EAAA+C,KAAA,EAAAC,MAAA,WAAAlD,CAAA,KAAAiD,KAAA;AADA;AACA;AACA;AACA;AAC+B;AACmB;AACZ;;AAEtC;;AAEA,IAAMK,MAAc,GAAG,qBAAqB;AAC5C,IAAMC,OAAe,GAAG,MAAM;AAEvB,SAAeC,WAAWA,CAAAC,EAAA,EAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,YAAA,CAAAZ,KAAA,OAAAD,SAAA;AAAA;AAgDhC,SAAAa,aAAA;EAAAA,YAAA,GAAAd,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAhDM,SAAA0B,SAA2BC,GAAQ,EAAEC,GAAQ,EAAEC,IAAS;IAAA,IAAAC,YAAA,EAAAC,aAAA;IAAA,OAAAjC,YAAA,GAAAC,CAAA,WAAAiC,SAAA;MAAA,kBAAAA,SAAA,CAAAnE,CAAA;QAAA;UAAA,SAElB;AAAA,EAAD;UACxCuE,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;UACtDP,YAAY,GAAG;YACnB,gBAAgB,EAAE,0DAA0D;YAC5E,IAAI,EAAE,mBAAmB;YACzB,gBAAgB,EAAE,CAAC,iBAAiB,CAAC;YACrC,aAAa,EAAE,kBAAkB;YACjC,WAAW,EAAE,KAAK;YAClB,SAAS,EAAE,MAAM;YACjB,UAAU,EAAE,oBAAoB;YAChC,MAAM,EAAE,sBAAsB;YAC9B,aAAa,EAAE,IAAI;YACnB,gBAAgB,EAAE,qBAAqB;YACvC,mBAAmB,EAAE,OAAO;YAC5B,mBAAmB,EAAE;UACvB,CAAC;UAAA,OAAAE,SAAA,CAAAlD,CAAA,IACM8C,GAAG,CAACU,IAAI,CAACR,YAAY,CAAC;QAAA;UAGzBC,aAAqB,GAAGJ,GAAG,CAACY,GAAG,CAAC,eAAe,CAAC;UAAAP,SAAA,CAAAnE,CAAA;UAAA,OAEhDoD,+DAAc,CAACc,aAAa,CAAC,CAChCrB,IAAI;YAAA,IAAA8B,IAAA,GAAA7B,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAAC,SAAAyC,QAAOC,kBAAkB;cAAA,IAAAC,UAAA,EAAAC,eAAA,EAAAC,sBAAA,EAAAC,SAAA;cAAA,OAAAhD,YAAA,GAAAC,CAAA,WAAAgD,QAAA;gBAAA,kBAAAA,QAAA,CAAAlF,CAAA;kBAAA;oBAAA,MACzB6E,kBAAkB,KAAKA,kBAAkB,CAACM,MAAM,IAAIN,kBAAkB,CAACO,KAAK,CAAC;sBAAAF,QAAA,CAAAlF,CAAA;sBAAA;oBAAA;oBAC/E+D,GAAG,CAACU,IAAI,CAACI,kBAAkB,CAAC;oBAACK,QAAA,CAAAlF,CAAA;oBAAA;kBAAA;oBAEvB8E,UAAkB,GAAGD,kBAAkB,CAACQ,YAAY;oBACpDN,eAAuB,GAAGX,OAAO,CAACC,GAAG,CAACiB,iBAAiB,IAAI,KAAK;oBAChEN,sBAA8B,GAAGZ,OAAO,CAACC,GAAG,CAACkB,mBAAmB,IAAI,EAAE;oBAAAL,QAAA,CAAAlF,CAAA;oBAAA,OAEpDwF,YAAY,CAACV,UAAU,EAAEC,eAAe,EAAEC,sBAAsB,CAAC;kBAAA;oBAAnFC,SAAS,GAAAC,QAAA,CAAAlE,CAAA;oBAEf;oBACA;oBACA;oBACA,IAAIiE,SAAS,CAACQ,IAAI,EAAE;sBAClBzB,IAAI,CAACX,kDAAW,CAAC4B,SAAS,CAACQ,IAAI,EAAE,wBAAwB,GAAGC,IAAI,CAACC,SAAS,CAACV,SAAS,CAAC,CAAC,CAAC;oBACzF,CAAC,MAAM;sBACLlB,GAAG,CAACU,IAAI,CAACQ,SAAS,CAAC;oBACrB;kBAAC;oBAAA,OAAAC,QAAA,CAAAjE,CAAA;gBAAA;cAAA,GAAA2D,OAAA;YAAA,CAEJ;YAAA,iBAAAgB,GAAA;cAAA,OAAAjB,IAAA,CAAA3B,KAAA,OAAAD,SAAA;YAAA;UAAA,IAAC,CACD8C,KAAK,CAAC,UAACC,GAAG,EAAK;YACd/B,GAAG,CAACgC,MAAM,CAAC,GAAG,CAAC,CAACtB,IAAI,CAACqB,GAAG,CAACE,OAAO,CAAC;YACjC;UACF,CAAC,CAAC;QAAA;UAAA,OAAA7B,SAAA,CAAAlD,CAAA;MAAA;IAAA,GAAA4C,QAAA;EAAA,CACL;EAAA,OAAAD,YAAA,CAAAZ,KAAA,OAAAD,SAAA;AAAA;AAEM,SAAeyC,YAAYA,CAAAS,GAAA,EAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,aAAA,CAAApD,KAAA,OAAAD,SAAA;AAAA;AAmDjC,SAAAqD,cAAA;EAAAA,aAAA,GAAAtD,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAnDM,SAAAkE,SAA4BC,WAAmB,EAAEC,MAAc,EAAEC,WAAoB;IAAA,OAAAvE,YAAA,GAAAC,CAAA,WAAAuE,SAAA;MAAA,kBAAAA,SAAA,CAAAzG,CAAA;QAAA;UAAA,OAAAyG,SAAA,CAAAxF,CAAA,IACnF,IAAI0B,OAAO,CAAM,UAACC,OAAO,EAAE8D,MAAM,EAAK;YAC3C,IAAMC,OAA6B,GAAG;cACpCC,IAAI,EAAEtD,MAAM;cACZuD,IAAI,EAAE,GAAG,GAAGtD,OAAO,GAAGgD,MAAM,GAAGC,WAAW;cAC1CM,MAAM,EAAE,KAAK;cACbC,OAAO,EAAE;gBACP,cAAc,EAAE,kBAAkB;gBAClCC,MAAM,EAAE,kBAAkB;gBAC1BC,aAAa,EAAE,SAAS,GAAGX,WAAW;gBACtC,eAAe,EAAE,8CAA8C;gBAC/DY,OAAO,EAAE,IAAI;gBACbC,MAAM,EAAE;cACV;YACF,CAAC;YAEDhE,sCACM,CAACwD,OAAO,EAAE,UAACS,QAAQ,EAAK;cAC1B,IAAIC,IAAI,GAAG,EAAE;cACbD,QAAQ,CAACE,EAAE,CAAC,MAAM,EAAE,UAACpG,CAAC,EAAK;gBACzBmG,IAAI,IAAInG,CAAC;cACX,CAAC,CAAC;cACFkG,QAAQ,CAACE,EAAE,CAAC,KAAK,EAAE,YAAM;gBACvB;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;;gBAEA,IAAIlC,KAAK;gBACT,IAAIgC,QAAQ,CAACG,UAAU,KAAK,GAAG,EAAE;kBAC/B,IAAIC,UAAU,GAAG9B,IAAI,CAAC+B,KAAK,CAACJ,IAAI,CAAC;kBACjCzE,OAAO,CAAC4E,UAAU,CAAC;gBACrB,CAAC,MAAM;kBACLpC,KAAK,GAAG,IAAIsC,KAAK,CAAC,CAAC;kBACnBtC,KAAK,CAACK,IAAI,GAAG2B,QAAQ,CAACG,UAAU;kBAChCnC,KAAK,CAACY,OAAO,GAAGoB,QAAQ,CAACO,aAAa;;kBAEtC;kBACA;kBACAN,IAAI,GAAGA,IAAI,CAACO,IAAI,CAAC,CAAC;kBAClBxC,KAAK,CAACyC,QAAQ,GAAGnC,IAAI,CAAC+B,KAAK,CAACJ,IAAI,CAAC,CAACjC,KAAK,CAACK,IAAI;kBAC5CL,KAAK,CAAC0C,WAAW,GAAGpC,IAAI,CAAC+B,KAAK,CAACJ,IAAI,CAAC,CAACjC,KAAK,CAACY,OAAO;kBAClDpD,OAAO,CAACwC,KAAK,CAAC;gBAChB;cACF,CAAC,CAAC;YACJ,CAAC,CAAC,CACDkC,EAAE,CAAC,OAAO,EAAEZ,MAAM,CAAC;UACxB,CAAC,CAAC;MAAA;IAAA,GAAAL,QAAA;EAAA,CACH;EAAA,OAAAD,aAAA,CAAApD,KAAA,OAAAD,SAAA;AAAA,C;;;;;;;;;;;;;;;;;;;;;;;0BCjHD,uKAAAnD,CAAA,EAAAC,CAAA,EAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,WAAA,8BAAAC,EAAAN,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAE,CAAA,QAAAC,CAAA,GAAAL,CAAA,IAAAA,CAAA,CAAAM,SAAA,YAAAC,SAAA,GAAAP,CAAA,GAAAO,SAAA,EAAAC,CAAA,GAAAC,MAAA,CAAAC,MAAA,CAAAL,CAAA,CAAAC,SAAA,UAAAK,mBAAA,CAAAH,CAAA,uBAAAV,CAAA,EAAAE,CAAA,EAAAE,CAAA,QAAAE,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAI,CAAA,MAAAC,CAAA,GAAAX,CAAA,QAAAY,CAAA,OAAAC,CAAA,KAAAF,CAAA,KAAAb,CAAA,KAAAgB,CAAA,EAAApB,CAAA,EAAAqB,CAAA,EAAAC,CAAA,EAAAN,CAAA,EAAAM,CAAA,CAAAC,IAAA,CAAAvB,CAAA,MAAAsB,CAAA,WAAAA,EAAArB,CAAA,EAAAC,CAAA,WAAAM,CAAA,GAAAP,CAAA,EAAAQ,CAAA,MAAAG,CAAA,GAAAZ,CAAA,EAAAmB,CAAA,CAAAf,CAAA,GAAAF,CAAA,EAAAmB,CAAA,gBAAAC,EAAApB,CAAA,EAAAE,CAAA,SAAAK,CAAA,GAAAP,CAAA,EAAAU,CAAA,GAAAR,CAAA,EAAAH,CAAA,OAAAiB,CAAA,IAAAF,CAAA,KAAAV,CAAA,IAAAL,CAAA,GAAAgB,CAAA,CAAAO,MAAA,EAAAvB,CAAA,UAAAK,CAAA,EAAAE,CAAA,GAAAS,CAAA,CAAAhB,CAAA,GAAAqB,CAAA,GAAAH,CAAA,CAAAF,CAAA,EAAAQ,CAAA,GAAAjB,CAAA,KAAAN,CAAA,QAAAI,CAAA,GAAAmB,CAAA,KAAArB,CAAA,MAAAQ,CAAA,GAAAJ,CAAA,EAAAC,CAAA,GAAAD,CAAA,YAAAC,CAAA,WAAAD,CAAA,MAAAA,CAAA,MAAAR,CAAA,IAAAQ,CAAA,OAAAc,CAAA,MAAAhB,CAAA,GAAAJ,CAAA,QAAAoB,CAAA,GAAAd,CAAA,QAAAC,CAAA,MAAAU,CAAA,CAAAC,CAAA,GAAAhB,CAAA,EAAAe,CAAA,CAAAf,CAAA,GAAAI,CAAA,OAAAc,CAAA,GAAAG,CAAA,KAAAnB,CAAA,GAAAJ,CAAA,QAAAM,CAAA,MAAAJ,CAAA,IAAAA,CAAA,GAAAqB,CAAA,MAAAjB,CAAA,MAAAN,CAAA,EAAAM,CAAA,MAAAJ,CAAA,EAAAe,CAAA,CAAAf,CAAA,GAAAqB,CAAA,EAAAhB,CAAA,cAAAH,CAAA,IAAAJ,CAAA,aAAAmB,CAAA,QAAAH,CAAA,OAAAd,CAAA,qBAAAE,CAAA,EAAAW,CAAA,EAAAQ,CAAA,QAAAT,CAAA,YAAAU,SAAA,uCAAAR,CAAA,UAAAD,CAAA,IAAAK,CAAA,CAAAL,CAAA,EAAAQ,CAAA,GAAAhB,CAAA,GAAAQ,CAAA,EAAAL,CAAA,GAAAa,CAAA,GAAAxB,CAAA,GAAAQ,CAAA,OAAAT,CAAA,GAAAY,CAAA,MAAAM,CAAA,KAAAV,CAAA,KAAAC,CAAA,GAAAA,CAAA,QAAAA,CAAA,SAAAU,CAAA,CAAAf,CAAA,QAAAkB,CAAA,CAAAb,CAAA,EAAAG,CAAA,KAAAO,CAAA,CAAAf,CAAA,GAAAQ,CAAA,GAAAO,CAAA,CAAAC,CAAA,GAAAR,CAAA,aAAAI,CAAA,MAAAR,CAAA,QAAAC,CAAA,KAAAH,CAAA,YAAAL,CAAA,GAAAO,CAAA,CAAAF,CAAA,WAAAL,CAAA,GAAAA,CAAA,CAAA0B,IAAA,CAAAnB,CAAA,EAAAI,CAAA,UAAAc,SAAA,2CAAAzB,CAAA,CAAA2B,IAAA,SAAA3B,CAAA,EAAAW,CAAA,GAAAX,CAAA,CAAA4B,KAAA,EAAApB,CAAA,SAAAA,CAAA,oBAAAA,CAAA,KAAAR,CAAA,GAAAO,CAAA,CAAAsB,MAAA,KAAA7B,CAAA,CAAA0B,IAAA,CAAAnB,CAAA,GAAAC,CAAA,SAAAG,CAAA,GAAAc,SAAA,uCAAApB,CAAA,gBAAAG,CAAA,OAAAD,CAAA,GAAAR,CAAA,cAAAC,CAAA,IAAAiB,CAAA,GAAAC,CAAA,CAAAf,CAAA,QAAAQ,CAAA,GAAAV,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,EAAAe,CAAA,OAAAE,CAAA,kBAAApB,CAAA,IAAAO,CAAA,GAAAR,CAAA,EAAAS,CAAA,MAAAG,CAAA,GAAAX,CAAA,cAAAe,CAAA,mBAAAa,KAAA,EAAA5B,CAAA,EAAA2B,IAAA,EAAAV,CAAA,SAAAhB,CAAA,EAAAI,CAAA,EAAAE,CAAA,QAAAI,CAAA,QAAAS,CAAA,gBAAAV,UAAA,cAAAoB,kBAAA,cAAAC,2BAAA,KAAA/B,CAAA,GAAAY,MAAA,CAAAoB,cAAA,MAAAxB,CAAA,MAAAL,CAAA,IAAAH,CAAA,CAAAA,CAAA,IAAAG,CAAA,SAAAW,mBAAA,CAAAd,CAAA,OAAAG,CAAA,iCAAAH,CAAA,GAAAW,CAAA,GAAAoB,0BAAA,CAAAtB,SAAA,GAAAC,SAAA,CAAAD,SAAA,GAAAG,MAAA,CAAAC,MAAA,CAAAL,CAAA,YAAAO,EAAAhB,CAAA,WAAAa,MAAA,CAAAqB,cAAA,GAAArB,MAAA,CAAAqB,cAAA,CAAAlC,CAAA,EAAAgC,0BAAA,KAAAhC,CAAA,CAAAmC,SAAA,GAAAH,0BAAA,EAAAjB,mBAAA,CAAAf,CAAA,EAAAM,CAAA,yBAAAN,CAAA,CAAAU,SAAA,GAAAG,MAAA,CAAAC,MAAA,CAAAF,CAAA,GAAAZ,CAAA,WAAA+B,iBAAA,CAAArB,SAAA,GAAAsB,0BAAA,EAAAjB,mBAAA,CAAAH,CAAA,iBAAAoB,0BAAA,GAAAjB,mBAAA,CAAAiB,0BAAA,iBAAAD,iBAAA,GAAAA,iBAAA,CAAAK,WAAA,wBAAArB,mBAAA,CAAAiB,0BAAA,EAAA1B,CAAA,wBAAAS,mBAAA,CAAAH,CAAA,GAAAG,mBAAA,CAAAH,CAAA,EAAAN,CAAA,gBAAAS,mBAAA,CAAAH,CAAA,EAAAR,CAAA,iCAAAW,mBAAA,CAAAH,CAAA,8DAAAyB,YAAA,YAAAA,aAAA,aAAAC,CAAA,EAAA9B,CAAA,EAAA+B,CAAA,EAAAvB,CAAA;AAAA,SAAAD,oBAAAf,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAH,CAAA,QAAAO,CAAA,GAAAK,MAAA,CAAA2B,cAAA,QAAAhC,CAAA,uBAAAR,CAAA,IAAAQ,CAAA,QAAAO,mBAAA,YAAA0B,mBAAAzC,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAH,CAAA,aAAAK,EAAAJ,CAAA,EAAAE,CAAA,IAAAW,mBAAA,CAAAf,CAAA,EAAAE,CAAA,YAAAF,CAAA,gBAAA0C,OAAA,CAAAxC,CAAA,EAAAE,CAAA,EAAAJ,CAAA,SAAAE,CAAA,GAAAM,CAAA,GAAAA,CAAA,CAAAR,CAAA,EAAAE,CAAA,IAAA2B,KAAA,EAAAzB,CAAA,EAAAuC,UAAA,GAAA1C,CAAA,EAAA2C,YAAA,GAAA3C,CAAA,EAAA4C,QAAA,GAAA5C,CAAA,MAAAD,CAAA,CAAAE,CAAA,IAAAE,CAAA,IAAAE,CAAA,aAAAA,CAAA,cAAAA,CAAA,mBAAAS,mBAAA,CAAAf,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAH,CAAA;AAAA,SAAAkI,eAAAjI,CAAA,EAAAF,CAAA,WAAAoI,eAAA,CAAAlI,CAAA,KAAAmI,qBAAA,CAAAnI,CAAA,EAAAF,CAAA,KAAAsI,2BAAA,CAAApI,CAAA,EAAAF,CAAA,KAAAuI,gBAAA;AAAA,SAAAA,iBAAA,cAAA7G,SAAA;AAAA,SAAA4G,4BAAApI,CAAA,EAAAmB,CAAA,QAAAnB,CAAA,2BAAAA,CAAA,SAAAsI,iBAAA,CAAAtI,CAAA,EAAAmB,CAAA,OAAApB,CAAA,MAAAwI,QAAA,CAAA9G,IAAA,CAAAzB,CAAA,EAAAwI,KAAA,6BAAAzI,CAAA,IAAAC,CAAA,CAAAyI,WAAA,KAAA1I,CAAA,GAAAC,CAAA,CAAAyI,WAAA,CAAAC,IAAA,aAAA3I,CAAA,cAAAA,CAAA,GAAA4I,KAAA,CAAAC,IAAA,CAAA5I,CAAA,oBAAAD,CAAA,+CAAA8I,IAAA,CAAA9I,CAAA,IAAAuI,iBAAA,CAAAtI,CAAA,EAAAmB,CAAA;AAAA,SAAAmH,kBAAAtI,CAAA,EAAAmB,CAAA,aAAAA,CAAA,IAAAA,CAAA,GAAAnB,CAAA,CAAAsB,MAAA,MAAAH,CAAA,GAAAnB,CAAA,CAAAsB,MAAA,YAAAxB,CAAA,MAAAI,CAAA,GAAAyI,KAAA,CAAAxH,CAAA,GAAArB,CAAA,GAAAqB,CAAA,EAAArB,CAAA,IAAAI,CAAA,CAAAJ,CAAA,IAAAE,CAAA,CAAAF,CAAA,UAAAI,CAAA;AAAA,SAAAiI,sBAAAnI,CAAA,EAAAuB,CAAA,QAAAxB,CAAA,WAAAC,CAAA,gCAAAC,MAAA,IAAAD,CAAA,CAAAC,MAAA,CAAAE,QAAA,KAAAH,CAAA,4BAAAD,CAAA,QAAAD,CAAA,EAAAI,CAAA,EAAAI,CAAA,EAAAI,CAAA,EAAAS,CAAA,OAAAL,CAAA,OAAAV,CAAA,iBAAAE,CAAA,IAAAP,CAAA,GAAAA,CAAA,CAAA0B,IAAA,CAAAzB,CAAA,GAAAkE,IAAA,QAAA3C,CAAA,QAAAZ,MAAA,CAAAZ,CAAA,MAAAA,CAAA,UAAAe,CAAA,uBAAAA,CAAA,IAAAhB,CAAA,GAAAQ,CAAA,CAAAmB,IAAA,CAAA1B,CAAA,GAAA2B,IAAA,MAAAP,CAAA,CAAA2H,IAAA,CAAAhJ,CAAA,CAAA6B,KAAA,GAAAR,CAAA,CAAAG,MAAA,KAAAC,CAAA,GAAAT,CAAA,iBAAAd,CAAA,IAAAI,CAAA,OAAAF,CAAA,GAAAF,CAAA,yBAAAc,CAAA,YAAAf,CAAA,CAAA6B,MAAA,KAAAlB,CAAA,GAAAX,CAAA,CAAA6B,MAAA,IAAAjB,MAAA,CAAAD,CAAA,MAAAA,CAAA,2BAAAN,CAAA,QAAAF,CAAA,aAAAiB,CAAA;AAAA,SAAA+G,gBAAAlI,CAAA,QAAA2I,KAAA,CAAAI,OAAA,CAAA/I,CAAA,UAAAA,CAAA;AAAA,SAAA4C,mBAAA1C,CAAA,EAAAH,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAI,CAAA,EAAAe,CAAA,EAAAZ,CAAA,cAAAD,CAAA,GAAAJ,CAAA,CAAAiB,CAAA,EAAAZ,CAAA,GAAAG,CAAA,GAAAJ,CAAA,CAAAqB,KAAA,WAAAzB,CAAA,gBAAAJ,CAAA,CAAAI,CAAA,KAAAI,CAAA,CAAAoB,IAAA,GAAA3B,CAAA,CAAAW,CAAA,IAAAmC,OAAA,CAAAC,OAAA,CAAApC,CAAA,EAAAqC,IAAA,CAAA/C,CAAA,EAAAI,CAAA;AAAA,SAAA4C,kBAAA9C,CAAA,6BAAAH,CAAA,SAAAD,CAAA,GAAAmD,SAAA,aAAAJ,OAAA,WAAA7C,CAAA,EAAAI,CAAA,QAAAe,CAAA,GAAAjB,CAAA,CAAAgD,KAAA,CAAAnD,CAAA,EAAAD,CAAA,YAAAqD,MAAAjD,CAAA,IAAA0C,kBAAA,CAAAzB,CAAA,EAAAnB,CAAA,EAAAI,CAAA,EAAA+C,KAAA,EAAAC,MAAA,UAAAlD,CAAA,cAAAkD,OAAAlD,CAAA,IAAA0C,kBAAA,CAAAzB,CAAA,EAAAnB,CAAA,EAAAI,CAAA,EAAA+C,KAAA,EAAAC,MAAA,WAAAlD,CAAA,KAAAiD,KAAA;AADA;AACA;AACA;AACA;AACA;;AAE+B;AACI;AACJ;AACO;;AAEtC;;AAEA,IAAMiG,uBAAuB,GAAG,8DAA8D;AAEvF,SAAe9F,cAAcA,CAAAK,EAAA;EAAA,OAAA0F,eAAA,CAAAnG,KAAA,OAAAD,SAAA;AAAA;AAkDnC,SAAAoG,gBAAA;EAAAA,eAAA,GAAArG,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAlDM,SAAAyC,QAA8BV,aAAqB;IAAA,IAAAkB,KAAA,EAAAgE,SAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,SAAA,EAAAC,WAAA,EAAAC,iBAAA,EAAAC,UAAA,EAAAC,SAAA,EAAAC,MAAA,EAAAC,eAAA,EAAAC,WAAA,EAAAC,aAAA,EAAAC,IAAA;IAAA,OAAA/H,YAAA,GAAAC,CAAA,WAAAgD,QAAA;MAAA,kBAAAA,QAAA,CAAAlF,CAAA;QAAA;UAAA,SAEb;AAAA,EAAD;UACxCuE,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;UAAC,OAAAU,QAAA,CAAAjE,CAAA,IACtD;YACLoE,YAAY,EAAE,kBAAkB;YAChC4E,UAAU,EAAE,QAAQ;YACpBC,UAAU,EAAE,IAAI;YAChBC,KAAK,EAAE;UACT,CAAC;QAAA;UAAA,IAGEjG,aAAa;YAAAgB,QAAA,CAAAlF,CAAA;YAAA;UAAA;UACZoF,KAAK,GAAG,IAAIsC,KAAK,CAAC,oCAAoC,CAAC;UAAA,OAAAxC,QAAA,CAAAjE,CAAA,IACpD0B,OAAO,CAAC+D,MAAM,CAACtB,KAAK,CAAC;QAAA;UAEtBgE,SAAiB,GAAGhF,OAAO,CAACC,GAAG,CAAC+F,KAAK,IAAI,WAAW;UAAAf,oBAAA,GACvBnF,aAAa,CAACmG,KAAK,CAAC,GAAG,CAAC,EAAAf,qBAAA,GAAAvB,cAAA,CAAAsB,oBAAA,MAAlD,YAAaE,SAAS,GAAAD,qBAAA;UAEzBE,WAAW,GAAIR,0DAAU,CAACO,SAAS,CAAC,CAAoBgB,GAAG,CAACF,KAAK,CAAC,GAAG,CAAC;UACtEZ,iBAAiB,GAAGD,WAAW,CAACgB,IAAI,CAAC,UAACL,KAAK;YAAA,OAAKA,KAAK,KAAK,gBAAgB;UAAA,EAAC;UAAA,IAC5EV,iBAAiB;YAAAvE,QAAA,CAAAlF,CAAA;YAAA;UAAA;UAAA,MACd,IAAI0H,KAAK,CAAC,wBAAwB,CAAC;QAAA;UAGrCgC,UAAU,GAAG;YACjBe,SAAS,EAAErG,OAAO,CAACC,GAAG,CAACqG,SAAS;YAChCC,aAAa,EAAEvG,OAAO,CAACC,GAAG,CAACuG,aAAa;YACxCC,UAAU,EAAE,6CAA6C;YACzDtB,SAAS,EAAEA,SAAS;YACpBuB,mBAAmB,EAAE,cAAc;YACnCX,KAAK,EAAE,CAACf,SAAS,CAAC,CAAC2B,IAAI,CAAC,GAAG;UAC7B,CAAC;UAEKpB,SAAiB,GAAG,mCAAmC;UACvDC,MAAc,GAAG,QAAQ;UACzBC,eAAuB,GAAG,mBAAmB;UAC7CC,WAAW,GAAGf,sDAAI,CAACW,UAAU,CAAC;UAAAxE,QAAA,CAAAlF,CAAA;UAAA,OAER8I,iDAAK,IAAAkC,MAAA,CAAIrB,SAAS,OAAAqB,MAAA,CAAIpB,MAAM,OAAAoB,MAAA,CAAInB,eAAe,GAAI;YAC7E/C,MAAM,EAAE,MAAM;YACdO,IAAI,EAAEyC,WAAW;YACjB/C,OAAO,EAAE;cACPC,MAAM,EAAE,kBAAkB;cAC1B,cAAc,EAAE;YAClB;UACF,CAAC,CAAC;QAAA;UAPI+C,aAAa,GAAA7E,QAAA,CAAAlE,CAAA;UAAAkE,QAAA,CAAAlF,CAAA;UAAA,OAQA+J,aAAa,CAACC,IAAI,CAAC,CAAC;QAAA;UAAjCA,IAAI,GAAA9E,QAAA,CAAAlE,CAAA;UAAA,OAAAkE,QAAA,CAAAjE,CAAA,IACH+I,IAAI;QAAA;UAAA,OAAA9E,QAAA,CAAAjE,CAAA;MAAA;IAAA,GAAA2D,OAAA;EAAA,CAEd;EAAA,OAAAuE,eAAA,CAAAnG,KAAA,OAAAD,SAAA;AAAA;AAEM,SAASkI,WAAWA,CAACnH,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAQ;EAChD;EACA,IAAII,IAAsC,EAAE;IAC1CG,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;IAC5D,OAAOR,IAAI,CAAC,CAAC;EACf;EAEA;AAAA,iBAA6C;EAC7C;AAAA,+BAeC;AACH;AAEA,SAASuH,cAAcA,CAACE,MAAW,EAAEC,QAAa,EAAE;EAClD,IAAIC,MAAkB,GAAG,IAAI1C,gDAAU,CAAC;IACtC2C,OAAO,EAAE1C;EACX,CAAC,CAAC;EAEFyC,MAAM,CAACE,aAAa,CAACJ,MAAM,CAACK,GAAG,EAAE,UAAUhG,GAAG,EAAEiG,GAAG,EAAE;IACnDL,QAAQ,CAAC,IAAI,EAAEK,GAAG,CAACC,YAAY,CAAC,CAAC,CAAC;EACpC,CAAC,CAAC;AACJ,C;;;;;;;;;;ACrGA,0C;;;;;;;;;;ACAA,mC;;;;;;;;;;ACAA,oC;;;;;;;;;;ACAA,4C;;;;;;;;;;ACAA,iC;;;;;;;;;;ACAA,wC;;;;;;;;;;ACAA,kC;;;;;;;;;;ACAA,yC;;;;;;;;;;ACAA,qC;;;;;;;;;;ACAA,mC;;;;;;;;;;ACAA,uC;;;;;;;;;;ACAA,mD;;;;;;;;;;ACAA,iC;;;;;;UCAA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA;WACA,iCAAiC,WAAW;WAC5C;WACA,E;;;;;WCPA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA,E;;;;;WCPA,wF;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D,E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BCLA,uKAAApM,CAAA,EAAAC,CAAA,EAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,WAAA,8BAAAC,EAAAN,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAE,CAAA,QAAAC,CAAA,GAAAL,CAAA,IAAAA,CAAA,CAAAM,SAAA,YAAAC,SAAA,GAAAP,CAAA,GAAAO,SAAA,EAAAC,CAAA,GAAAC,MAAA,CAAAC,MAAA,CAAAL,CAAA,CAAAC,SAAA,UAAAK,mBAAA,CAAAH,CAAA,uBAAAV,CAAA,EAAAE,CAAA,EAAAE,CAAA,QAAAE,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAI,CAAA,MAAAC,CAAA,GAAAX,CAAA,QAAAY,CAAA,OAAAC,CAAA,KAAAF,CAAA,KAAAb,CAAA,KAAAgB,CAAA,EAAApB,CAAA,EAAAqB,CAAA,EAAAC,CAAA,EAAAN,CAAA,EAAAM,CAAA,CAAAC,IAAA,CAAAvB,CAAA,MAAAsB,CAAA,WAAAA,EAAArB,CAAA,EAAAC,CAAA,WAAAM,CAAA,GAAAP,CAAA,EAAAQ,CAAA,MAAAG,CAAA,GAAAZ,CAAA,EAAAmB,CAAA,CAAAf,CAAA,GAAAF,CAAA,EAAAmB,CAAA,gBAAAC,EAAApB,CAAA,EAAAE,CAAA,SAAAK,CAAA,GAAAP,CAAA,EAAAU,CAAA,GAAAR,CAAA,EAAAH,CAAA,OAAAiB,CAAA,IAAAF,CAAA,KAAAV,CAAA,IAAAL,CAAA,GAAAgB,CAAA,CAAAO,MAAA,EAAAvB,CAAA,UAAAK,CAAA,EAAAE,CAAA,GAAAS,CAAA,CAAAhB,CAAA,GAAAqB,CAAA,GAAAH,CAAA,CAAAF,CAAA,EAAAQ,CAAA,GAAAjB,CAAA,KAAAN,CAAA,QAAAI,CAAA,GAAAmB,CAAA,KAAArB,CAAA,MAAAQ,CAAA,GAAAJ,CAAA,EAAAC,CAAA,GAAAD,CAAA,YAAAC,CAAA,WAAAD,CAAA,MAAAA,CAAA,MAAAR,CAAA,IAAAQ,CAAA,OAAAc,CAAA,MAAAhB,CAAA,GAAAJ,CAAA,QAAAoB,CAAA,GAAAd,CAAA,QAAAC,CAAA,MAAAU,CAAA,CAAAC,CAAA,GAAAhB,CAAA,EAAAe,CAAA,CAAAf,CAAA,GAAAI,CAAA,OAAAc,CAAA,GAAAG,CAAA,KAAAnB,CAAA,GAAAJ,CAAA,QAAAM,CAAA,MAAAJ,CAAA,IAAAA,CAAA,GAAAqB,CAAA,MAAAjB,CAAA,MAAAN,CAAA,EAAAM,CAAA,MAAAJ,CAAA,EAAAe,CAAA,CAAAf,CAAA,GAAAqB,CAAA,EAAAhB,CAAA,cAAAH,CAAA,IAAAJ,CAAA,aAAAmB,CAAA,QAAAH,CAAA,OAAAd,CAAA,qBAAAE,CAAA,EAAAW,CAAA,EAAAQ,CAAA,QAAAT,CAAA,YAAAU,SAAA,uCAAAR,CAAA,UAAAD,CAAA,IAAAK,CAAA,CAAAL,CAAA,EAAAQ,CAAA,GAAAhB,CAAA,GAAAQ,CAAA,EAAAL,CAAA,GAAAa,CAAA,GAAAxB,CAAA,GAAAQ,CAAA,OAAAT,CAAA,GAAAY,CAAA,MAAAM,CAAA,KAAAV,CAAA,KAAAC,CAAA,GAAAA,CAAA,QAAAA,CAAA,SAAAU,CAAA,CAAAf,CAAA,QAAAkB,CAAA,CAAAb,CAAA,EAAAG,CAAA,KAAAO,CAAA,CAAAf,CAAA,GAAAQ,CAAA,GAAAO,CAAA,CAAAC,CAAA,GAAAR,CAAA,aAAAI,CAAA,MAAAR,CAAA,QAAAC,CAAA,KAAAH,CAAA,YAAAL,CAAA,GAAAO,CAAA,CAAAF,CAAA,WAAAL,CAAA,GAAAA,CAAA,CAAA0B,IAAA,CAAAnB,CAAA,EAAAI,CAAA,UAAAc,SAAA,2CAAAzB,CAAA,CAAA2B,IAAA,SAAA3B,CAAA,EAAAW,CAAA,GAAAX,CAAA,CAAA4B,KAAA,EAAApB,CAAA,SAAAA,CAAA,oBAAAA,CAAA,KAAAR,CAAA,GAAAO,CAAA,CAAAsB,MAAA,KAAA7B,CAAA,CAAA0B,IAAA,CAAAnB,CAAA,GAAAC,CAAA,SAAAG,CAAA,GAAAc,SAAA,uCAAApB,CAAA,gBAAAG,CAAA,OAAAD,CAAA,GAAAR,CAAA,cAAAC,CAAA,IAAAiB,CAAA,GAAAC,CAAA,CAAAf,CAAA,QAAAQ,CAAA,GAAAV,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,EAAAe,CAAA,OAAAE,CAAA,kBAAApB,CAAA,IAAAO,CAAA,GAAAR,CAAA,EAAAS,CAAA,MAAAG,CAAA,GAAAX,CAAA,cAAAe,CAAA,mBAAAa,KAAA,EAAA5B,CAAA,EAAA2B,IAAA,EAAAV,CAAA,SAAAhB,CAAA,EAAAI,CAAA,EAAAE,CAAA,QAAAI,CAAA,QAAAS,CAAA,gBAAAV,UAAA,cAAAoB,kBAAA,cAAAC,2BAAA,KAAA/B,CAAA,GAAAY,MAAA,CAAAoB,cAAA,MAAAxB,CAAA,MAAAL,CAAA,IAAAH,CAAA,CAAAA,CAAA,IAAAG,CAAA,SAAAW,mBAAA,CAAAd,CAAA,OAAAG,CAAA,iCAAAH,CAAA,GAAAW,CAAA,GAAAoB,0BAAA,CAAAtB,SAAA,GAAAC,SAAA,CAAAD,SAAA,GAAAG,MAAA,CAAAC,MAAA,CAAAL,CAAA,YAAAO,EAAAhB,CAAA,WAAAa,MAAA,CAAAqB,cAAA,GAAArB,MAAA,CAAAqB,cAAA,CAAAlC,CAAA,EAAAgC,0BAAA,KAAAhC,CAAA,CAAAmC,SAAA,GAAAH,0BAAA,EAAAjB,mBAAA,CAAAf,CAAA,EAAAM,CAAA,yBAAAN,CAAA,CAAAU,SAAA,GAAAG,MAAA,CAAAC,MAAA,CAAAF,CAAA,GAAAZ,CAAA,WAAA+B,iBAAA,CAAArB,SAAA,GAAAsB,0BAAA,EAAAjB,mBAAA,CAAAH,CAAA,iBAAAoB,0BAAA,GAAAjB,mBAAA,CAAAiB,0BAAA,iBAAAD,iBAAA,GAAAA,iBAAA,CAAAK,WAAA,wBAAArB,mBAAA,CAAAiB,0BAAA,EAAA1B,CAAA,wBAAAS,mBAAA,CAAAH,CAAA,GAAAG,mBAAA,CAAAH,CAAA,EAAAN,CAAA,gBAAAS,mBAAA,CAAAH,CAAA,EAAAR,CAAA,iCAAAW,mBAAA,CAAAH,CAAA,8DAAAyB,YAAA,YAAAA,aAAA,aAAAC,CAAA,EAAA9B,CAAA,EAAA+B,CAAA,EAAAvB,CAAA;AAAA,SAAAD,oBAAAf,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAH,CAAA,QAAAO,CAAA,GAAAK,MAAA,CAAA2B,cAAA,QAAAhC,CAAA,uBAAAR,CAAA,IAAAQ,CAAA,QAAAO,mBAAA,YAAA0B,mBAAAzC,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAH,CAAA,aAAAK,EAAAJ,CAAA,EAAAE,CAAA,IAAAW,mBAAA,CAAAf,CAAA,EAAAE,CAAA,YAAAF,CAAA,gBAAA0C,OAAA,CAAAxC,CAAA,EAAAE,CAAA,EAAAJ,CAAA,SAAAE,CAAA,GAAAM,CAAA,GAAAA,CAAA,CAAAR,CAAA,EAAAE,CAAA,IAAA2B,KAAA,EAAAzB,CAAA,EAAAuC,UAAA,GAAA1C,CAAA,EAAA2C,YAAA,GAAA3C,CAAA,EAAA4C,QAAA,GAAA5C,CAAA,MAAAD,CAAA,CAAAE,CAAA,IAAAE,CAAA,IAAAE,CAAA,aAAAA,CAAA,cAAAA,CAAA,mBAAAS,mBAAA,CAAAf,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAH,CAAA;AAAA,SAAA6C,mBAAA1C,CAAA,EAAAH,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAI,CAAA,EAAAe,CAAA,EAAAZ,CAAA,cAAAD,CAAA,GAAAJ,CAAA,CAAAiB,CAAA,EAAAZ,CAAA,GAAAG,CAAA,GAAAJ,CAAA,CAAAqB,KAAA,WAAAzB,CAAA,gBAAAJ,CAAA,CAAAI,CAAA,KAAAI,CAAA,CAAAoB,IAAA,GAAA3B,CAAA,CAAAW,CAAA,IAAAmC,OAAA,CAAAC,OAAA,CAAApC,CAAA,EAAAqC,IAAA,CAAA/C,CAAA,EAAAI,CAAA;AAAA,SAAA4C,kBAAA9C,CAAA,6BAAAH,CAAA,SAAAD,CAAA,GAAAmD,SAAA,aAAAJ,OAAA,WAAA7C,CAAA,EAAAI,CAAA,QAAAe,CAAA,GAAAjB,CAAA,CAAAgD,KAAA,CAAAnD,CAAA,EAAAD,CAAA,YAAAqD,MAAAjD,CAAA,IAAA0C,kBAAA,CAAAzB,CAAA,EAAAnB,CAAA,EAAAI,CAAA,EAAA+C,KAAA,EAAAC,MAAA,UAAAlD,CAAA,cAAAkD,OAAAlD,CAAA,IAAA0C,kBAAA,CAAAzB,CAAA,EAAAnB,CAAA,EAAAI,CAAA,EAAA+C,KAAA,EAAAC,MAAA,WAAAlD,CAAA,KAAAiD,KAAA;AADA;AACA;AACA;AACA;AACA;;AAEA,IAAImB,IAAqC,EAAE;EACzC6H,oDAAwB,CAAC,CAAC;AAC5B;AACsC;AACT;AACiB;AACb;AACH;AACJ;AACF;AACuC;AAChB;AACA;;AAE/C;;AAEA,IAAMO,GAAG,GAAGH,8CAAO,CAAC,CAAC;AACrB,IAAMI,IAAqB,GAAGrI,OAAO,CAACC,GAAG,CAACqI,QAAQ,IAAI,MAAM;AAE5DF,GAAG,CAACG,GAAG,CAAC,MAAM,EAAEF,IAAI,CAAC;AAErBD,GAAG,CAACI,GAAG,CAACR,mCAAM,CAAC,KAAK,CAAC,CAAC;AACtBI,GAAG,CAACI,GAAG,CAACP,mDAAY,CAAC,CAAC,CAAC;AACvBG,GAAG,CAACI,GAAG,CAACP,yDAAkB,CAAC;EAAES,QAAQ,EAAE;AAAM,CAAC,CAAC,CAAC;AAChDN,GAAG,CAACI,GAAG,CAACT,0CAAY,CAAC,CAAC,CAAC;;AAEvB;AACA,IAAI/H,IAAqC,EAAE;EACzCoI,GAAG,CAACI,GAAG,CAACP,wDAAc,CAACxF,sCAAS,CAACzC,OAAO,CAAC4I,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE;IAAEC,IAAI,EAAE;EAAM,CAAC,CAAC,CAAC;EAE1ET,GAAG,CAACI,GAAG,CAAC,UAAU9I,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAE;IAChCD,GAAG,CAAC0H,MAAM,CAAC,eAAe,EAAE,8CAA8C,CAAC;IAC3E1H,GAAG,CAAC0H,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC;IAC3B1H,GAAG,CAAC0H,MAAM,CAAC,QAAQ,EAAE,UAAU,CAAC;IAChCzH,IAAI,CAAC,CAAC;EACR,CAAC,CAAC;AACJ,CAAC,MAAM;AAAA,EAGN;;AAED;AACAwI,GAAG,CAAC9H,GAAG,CAAC,GAAG,EAAE,UAAUZ,GAAG,EAAEC,GAAG,EAAE;EAC/BA,GAAG,CAACU,IAAI,CAAC,4CAA4C,CAAC;AACxD,CAAC,CAAC;;AAEF;AACA+H,GAAG,CAAC9H,GAAG,CAAC,OAAO,EAAE,UAAUZ,GAAQ,EAAEC,GAAQ,EAAE;EAC7CA,GAAG,CAACU,IAAI,CAAC;IAAEsB,MAAM,EAAE,IAAI;IAAEC,OAAO,EAAE,mBAAmB;IAAEkH,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;EAAE,CAAC,CAAC;AAC/F,CAAC,CAAC;AAEFZ,GAAG,CAAC9H,GAAG,CAAC,cAAc,EAAEuG,wDAAW,EAAEzH,wDAAW,CAAC;;AAEjD;AACAgJ,GAAG,CAAC9H,GAAG,CAAC,gBAAgB;EAAA,IAAAC,IAAA,GAAA7B,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAAE,SAAAyC,QAAOd,GAAQ,EAAEC,GAAQ;IAAA,OAAA9B,YAAA,GAAAC,CAAA,WAAAgD,QAAA;MAAA,kBAAAA,QAAA,CAAAlF,CAAA;QAAA;UAAA,OAAAkF,QAAA,CAAAjE,CAAA,IAC1C8C,GAAG,CAACsJ,QAAQ,CAACxG,sCAAS,CAACzC,OAAO,CAAC4I,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,eAAe,CAAC,CAAC;MAAA;IAAA,GAAApI,OAAA;EAAA,CACvE;EAAA,iBAAAnB,EAAA,EAAAC,GAAA;IAAA,OAAAiB,IAAA,CAAA3B,KAAA,OAAAD,SAAA;EAAA;AAAA,IAAC;AAEFyJ,GAAG,CAAC9H,GAAG,CAAC,0BAA0B;EAAA,IAAA4I,KAAA,GAAAxK,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAAE,SAAA0B,SAAOC,GAAQ,EAAEC,GAAQ;IAAA,OAAA9B,YAAA,GAAAC,CAAA,WAAAiC,SAAA;MAAA,kBAAAA,SAAA,CAAAnE,CAAA;QAAA;UAAA,OAAAmE,SAAA,CAAAlD,CAAA,IACpD8C,GAAG,CAACsJ,QAAQ,CAACxG,sCAAS,CAACzC,OAAO,CAAC4I,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,yBAAyB,CAAC,CAAC;MAAA;IAAA,GAAAnJ,QAAA;EAAA,CACjF;EAAA,iBAAAF,GAAA,EAAAsC,GAAA;IAAA,OAAAqH,KAAA,CAAAtK,KAAA,OAAAD,SAAA;EAAA;AAAA,IAAC;;AAEF;AACAyJ,GAAG,CAACI,GAAG,CAAC,UAAU9I,GAAQ,EAAEC,GAAQ,EAAEC,IAAS,EAAE;EAC/CA,IAAI,CAACX,kDAAW,CAAC,GAAG,CAAC,CAAC;AACxB,CAAC,CAAC;;AAEF;AACAmJ,GAAG,CAACI,GAAG,CAAC,UAAU9G,GAAQ,EAAEhC,GAAQ,EAAEC,GAAQ,EAAE;EAC9C;EACAA,GAAG,CAACwJ,MAAM,CAACvH,OAAO,GAAGF,GAAG,CAACE,OAAO;EAChCjC,GAAG,CAACwJ,MAAM,CAACnI,KAAK,GAAGtB,GAAG,CAAC0I,GAAG,CAAC9H,GAAG,CAAC,KAAK,CAAC,KAAK,aAAa,GAAGoB,GAAG,GAAG,CAAC,CAAC;;EAElE;EACA/B,GAAG,CAACgC,MAAM,CAACD,GAAG,CAACC,MAAM,IAAI,GAAG,CAAC;EAC7BhC,GAAG,CAACU,IAAI,CAAC;IAAEW,KAAK,EAAEU,GAAG,CAACE,OAAO;IAAED,MAAM,EAAED,GAAG,CAACC;EAAO,CAAC,CAAC;AACtD,CAAC,CAAC;;AAEF;AACA,IAAI3B,IAAsC,EAAE;EAC1CG,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;EACxD8H,wDAAiB,CAACE,GAAG,CAAC,CAACiB,MAAM,CAAChB,IAAI,EAAE,YAAM;IACxClI,OAAO,CAACC,GAAG,yDAAAwG,MAAA,CAA+CyB,IAAI,yBAAsB,CAAC;IACrFlI,OAAO,CAACC,GAAG,wDAA8C,CAAC;IAC1DD,OAAO,CAACC,GAAG,+BAAqB,CAAC;IACjCD,OAAO,CAACC,GAAG,0BAAAwG,MAAA,CAA0ByB,IAAI,aAAU,CAAC;IACpDlI,OAAO,CAACC,GAAG,0BAAAwG,MAAA,CAA0ByB,IAAI,yBAAsB,CAAC;IAChElI,OAAO,CAACC,GAAG,0BAAAwG,MAAA,CAA0ByB,IAAI,kCAA+B,CAAC;IACzElI,OAAO,CAACC,GAAG,0BAAAwG,MAAA,CAA0ByB,IAAI,6BAA0B,CAAC;EACtE,CAAC,CAAC;AACJ,CAAC,MAAM;AAAA,E", "sources": ["webpack://office-addin-taskpane-sso/./src/middle-tier/msgraph-helper.ts", "webpack://office-addin-taskpane-sso/./src/middle-tier/ssoauth-helper.ts", "webpack://office-addin-taskpane-sso/external commonjs \"cookie-parser\"", "webpack://office-addin-taskpane-sso/external commonjs \"dotenv\"", "webpack://office-addin-taskpane-sso/external commonjs \"express\"", "webpack://office-addin-taskpane-sso/external commonjs \"form-urlencoded\"", "webpack://office-addin-taskpane-sso/external node-commonjs \"http\"", "webpack://office-addin-taskpane-sso/external commonjs \"http-errors\"", "webpack://office-addin-taskpane-sso/external node-commonjs \"https\"", "webpack://office-addin-taskpane-sso/external commonjs \"jsonwebtoken\"", "webpack://office-addin-taskpane-sso/external commonjs \"jwks-rsa\"", "webpack://office-addin-taskpane-sso/external commonjs \"morgan\"", "webpack://office-addin-taskpane-sso/external commonjs \"node-fetch\"", "webpack://office-addin-taskpane-sso/external commonjs \"office-addin-dev-certs\"", "webpack://office-addin-taskpane-sso/external commonjs \"path\"", "webpack://office-addin-taskpane-sso/webpack/bootstrap", "webpack://office-addin-taskpane-sso/webpack/runtime/compat get default export", "webpack://office-addin-taskpane-sso/webpack/runtime/define property getters", "webpack://office-addin-taskpane-sso/webpack/runtime/hasOwnProperty shorthand", "webpack://office-addin-taskpane-sso/webpack/runtime/make namespace object", "webpack://office-addin-taskpane-sso/./src/middle-tier/app.ts"], "sourcesContent": ["// Copyright (c) Microsoft. All rights reserved. Licensed under the MIT license. See full license in the root of the repo.\n/*\n    This file provides the provides functionality to get Microsoft Graph data.\n*/\nimport * as https from \"https\";\nimport { getAccessToken } from \"./ssoauth-helper\";\nimport createError from \"http-errors\";\n\n/* global process */\n\nconst domain: string = \"graph.microsoft.com\";\nconst version: string = \"v1.0\";\n\nexport async function getUserData(req: any, res: any, next: any) {\n  // Development mode: return mock data\n  if (process.env.NODE_ENV === \"development\") {\n    console.log(\"🔧 Development mode: Returning mock user data\");\n    const mockUserData = {\n      \"@odata.context\": \"https://graph.microsoft.com/v1.0/$metadata#users/$entity\",\n      \"id\": \"dev-user-id-12345\",\n      \"businessPhones\": [\"****** 012 3456\"],\n      \"displayName\": \"Development User\",\n      \"givenName\": \"Dev\",\n      \"surname\": \"User\",\n      \"jobTitle\": \"Software Developer\",\n      \"mail\": \"<EMAIL>\",\n      \"mobilePhone\": null,\n      \"officeLocation\": \"Building 1, Floor 2\",\n      \"preferredLanguage\": \"en-US\",\n      \"userPrincipalName\": \"<EMAIL>\"\n    };\n    return res.send(mockUserData);\n  }\n\n  const authorization: string = req.get(\"Authorization\");\n\n  await getAccessToken(authorization)\n    .then(async (graphTokenResponse) => {\n      if (graphTokenResponse && (graphTokenResponse.claims || graphTokenResponse.error)) {\n        res.send(graphTokenResponse);\n      } else {\n        const graphToken: string = graphTokenResponse.access_token;\n        const graphUrlSegment: string = process.env.GRAPH_URL_SEGMENT || \"/me\";\n        const graphQueryParamSegment: string = process.env.QUERY_PARAM_SEGMENT || \"\";\n\n        const graphData = await getGraphData(graphToken, graphUrlSegment, graphQueryParamSegment);\n\n        // If Microsoft Graph returns an error, such as invalid or expired token,\n        // there will be a code property in the returned object set to a HTTP status (e.g. 401).\n        // Relay it to the client. It will caught in the fail callback of `makeGraphApiCall`.\n        if (graphData.code) {\n          next(createError(graphData.code, \"Microsoft Graph error \" + JSON.stringify(graphData)));\n        } else {\n          res.send(graphData);\n        }\n      }\n    })\n    .catch((err) => {\n      res.status(401).send(err.message);\n      return;\n    });\n}\n\nexport async function getGraphData(accessToken: string, apiUrl: string, queryParams?: string): Promise<any> {\n  return new Promise<any>((resolve, reject) => {\n    const options: https.RequestOptions = {\n      host: domain,\n      path: \"/\" + version + apiUrl + queryParams,\n      method: \"GET\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Accept: \"application/json\",\n        Authorization: \"Bearer \" + accessToken,\n        \"Cache-Control\": \"private, no-cache, no-store, must-revalidate\",\n        Expires: \"-1\",\n        Pragma: \"no-cache\",\n      },\n    };\n\n    https\n      .get(options, (response) => {\n        let body = \"\";\n        response.on(\"data\", (d) => {\n          body += d;\n        });\n        response.on(\"end\", () => {\n          // The response from the OData endpoint might be an error, say a\n          // 401 if the endpoint requires an access token and it was invalid\n          // or expired. But a message is not an error in the call of https.get,\n          // so the \"on('error', reject)\" line below isn't triggered.\n          // So, the code distinguishes success (200) messages from error\n          // messages and sends a JSON object to the caller with either the\n          // requested OData or error information.\n\n          let error;\n          if (response.statusCode === 200) {\n            let parsedBody = JSON.parse(body);\n            resolve(parsedBody);\n          } else {\n            error = new Error();\n            error.code = response.statusCode;\n            error.message = response.statusMessage;\n\n            // The error body sometimes includes an empty space\n            // before the first character, remove it or it causes an error.\n            body = body.trim();\n            error.bodyCode = JSON.parse(body).error.code;\n            error.bodyMessage = JSON.parse(body).error.message;\n            resolve(error);\n          }\n        });\n      })\n      .on(\"error\", reject);\n  });\n}\n", "/*\n * Copyright (c) Microsoft. All rights reserved. Licensed under the MIT license. See full license in root of repo. -->\n *\n * This file defines the routes within the authRoute router.\n */\n\nimport fetch from \"node-fetch\";\nimport form from \"form-urlencoded\";\nimport jwt from \"jsonwebtoken\";\nimport { JwksClient } from \"jwks-rsa\";\n\n/* global process, console */\n\nconst DISCOVERY_KEYS_ENDPOINT = \"https://login.microsoftonline.com/common/discovery/v2.0/keys\";\n\nexport async function getAccessToken(authorization: string): Promise<any> {\n  // Development mode bypass\n  if (process.env.NODE_ENV === \"development\") {\n    console.log(\"🔧 Development mode: Bypassing authentication\");\n    return {\n      access_token: \"dev-access-token\",\n      token_type: \"Bearer\",\n      expires_in: 3600,\n      scope: \"User.Read\"\n    };\n  }\n\n  if (!authorization) {\n    let error = new Error(\"No Authorization header was found.\");\n    return Promise.reject(error);\n  } else {\n    const scopeName: string = process.env.SCOPE || \"User.Read\";\n    const [, /* schema */ assertion] = authorization.split(\" \");\n\n    const tokenScopes = (jwt.decode(assertion) as jwt.JwtPayload).scp.split(\" \");\n    const accessAsUserScope = tokenScopes.find((scope) => scope === \"access_as_user\");\n    if (!accessAsUserScope) {\n      throw new Error(\"Missing access_as_user\");\n    }\n\n    const formParams = {\n      client_id: process.env.CLIENT_ID,\n      client_secret: process.env.CLIENT_SECRET,\n      grant_type: \"urn:ietf:params:oauth:grant-type:jwt-bearer\",\n      assertion: assertion,\n      requested_token_use: \"on_behalf_of\",\n      scope: [scopeName].join(\" \"),\n    };\n\n    const stsDomain: string = \"https://login.microsoftonline.com\";\n    const tenant: string = \"common\";\n    const tokenURLSegment: string = \"oauth2/v2.0/token\";\n    const encodedForm = form(formParams);\n\n    const tokenResponse = await fetch(`${stsDomain}/${tenant}/${tokenURLSegment}`, {\n      method: \"POST\",\n      body: encodedForm,\n      headers: {\n        Accept: \"application/json\",\n        \"Content-Type\": \"application/x-www-form-urlencoded\",\n      },\n    });\n    const json = await tokenResponse.json();\n    return json;\n  }\n}\n\nexport function validateJwt(req, res, next): void {\n  // Development mode bypass\n  if (process.env.NODE_ENV === \"development\") {\n    console.log(\"🔧 Development mode: Bypassing JWT validation\");\n    return next();\n  }\n\n  const authHeader = req.headers.authorization;\n  if (authHeader) {\n    const token = authHeader.split(\" \")[1];\n\n    const validationOptions = {\n      audience: process.env.CLIENT_ID,\n    };\n\n    jwt.verify(token, getSigningKeys, validationOptions, (err) => {\n      if (err) {\n        console.log(err);\n        return res.sendStatus(403);\n      }\n\n      next();\n    });\n  }\n}\n\nfunction getSigningKeys(header: any, callback: any) {\n  var client: JwksClient = new JwksClient({\n    jwksUri: DISCOVERY_KEYS_ENDPOINT,\n  });\n\n  client.getSigningKey(header.kid, function (err, key) {\n    callback(null, key.getPublicKey());\n  });\n}\n", "module.exports = require(\"cookie-parser\");", "module.exports = require(\"dotenv\");", "module.exports = require(\"express\");", "module.exports = require(\"form-urlencoded\");", "module.exports = require(\"http\");", "module.exports = require(\"http-errors\");", "module.exports = require(\"https\");", "module.exports = require(\"jsonwebtoken\");", "module.exports = require(\"jwks-rsa\");", "module.exports = require(\"morgan\");", "module.exports = require(\"node-fetch\");", "module.exports = require(\"office-addin-dev-certs\");", "module.exports = require(\"path\");", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "/*\n * Copyright (c) Microsoft. All rights reserved. Licensed under the MIT license. See full license in root of repo. -->\n *\n * This file is the main Node.js server file that defines the express middleware.\n */\n\nif (process.env.NODE_ENV !== \"production\") {\n  require(\"dotenv\").config();\n}\nimport createError from \"http-errors\";\nimport * as path from \"path\";\nimport * as cookieParser from \"cookie-parser\";\nimport * as logger from \"morgan\";\nimport express from \"express\";\nimport https from \"https\";\nimport http from \"http\";\nimport { getHttpsServerOptions } from \"office-addin-dev-certs\";\nimport { getUserData } from \"./msgraph-helper\";\nimport { validateJwt } from \"./ssoauth-helper\";\n\n/* global console, process, require, __dirname */\n\nconst app = express();\nconst port: number | string = process.env.API_PORT || \"3000\";\n\napp.set(\"port\", port);\n\napp.use(logger(\"dev\"));\napp.use(express.json());\napp.use(express.urlencoded({ extended: false }));\napp.use(cookieParser());\n\n/* Turn off caching when developing */\nif (process.env.NODE_ENV !== \"production\") {\n  app.use(express.static(path.join(process.cwd(), \"dist\"), { etag: false }));\n\n  app.use(function (req, res, next) {\n    res.header(\"Cache-Control\", \"private, no-cache, no-store, must-revalidate\");\n    res.header(\"Expires\", \"-1\");\n    res.header(\"Pragma\", \"no-cache\");\n    next();\n  });\n} else {\n  // In production mode, let static files be cached.\n  app.use(express.static(path.join(process.cwd(), \"dist\")));\n}\n\n// Simple root endpoint\napp.get(\"/\", function (req, res) {\n  res.send(\"RubiRecruit Development Server Running! 🚀\");\n});\n\n// Middle-tier API calls\napp.get(\"/ping\", function (req: any, res: any) {\n  res.send({ status: \"OK\", message: \"Server is running\", timestamp: new Date().toISOString() });\n});\n\napp.get(\"/getuserdata\", validateJwt, getUserData);\n\n// Get the client side task pane files requested\napp.get(\"/taskpane.html\", async (req: any, res: any) => {\n  return res.sendFile(path.join(process.cwd(), \"dist\", \"taskpane.html\"));\n});\n\napp.get(\"/fallbackauthdialog.html\", async (req: any, res: any) => {\n  return res.sendFile(path.join(process.cwd(), \"dist\", \"fallbackauthdialog.html\"));\n});\n\n// Catch 404 and forward to error handler\napp.use(function (req: any, res: any, next: any) {\n  next(createError(404));\n});\n\n// error handler\napp.use(function (err: any, req: any, res: any) {\n  // set locals, only providing error in development\n  res.locals.message = err.message;\n  res.locals.error = req.app.get(\"env\") === \"development\" ? err : {};\n\n  // render the error page\n  res.status(err.status || 500);\n  res.send({ error: err.message, status: err.status });\n});\n\n// Development mode: Run on HTTP to avoid certificate issues\nif (process.env.NODE_ENV === \"development\") {\n  console.log(\"🔧 Development mode: Starting HTTP server\");\n  http.createServer(app).listen(port, () => {\n    console.log(`🚀 HTTP Server running on http://localhost:${port} in development mode`);\n    console.log(`📝 No certificates required for development`);\n    console.log(`🌐 Test endpoints:`);\n    console.log(`   - http://localhost:${port}/ (root)`);\n    console.log(`   - http://localhost:${port}/ping (health check)`);\n    console.log(`   - http://localhost:${port}/getuserdata (mock user data)`);\n    console.log(`   - http://localhost:${port}/taskpane.html (main UI)`);\n  });\n} else {\n  // Production mode: Use HTTPS with certificates\n  getHttpsServerOptions().then((options) => {\n    https\n      .createServer(options, app)\n      .listen(port, () => console.log(`Server running on ${port} in ${process.env.NODE_ENV} mode`));\n  });\n}\n"], "names": ["e", "t", "r", "Symbol", "n", "iterator", "o", "toStringTag", "i", "c", "prototype", "Generator", "u", "Object", "create", "_regeneratorDefine2", "f", "p", "y", "G", "v", "a", "d", "bind", "length", "l", "TypeError", "call", "done", "value", "return", "GeneratorFunction", "GeneratorFunctionPrototype", "getPrototypeOf", "setPrototypeOf", "__proto__", "displayName", "_regenerator", "w", "m", "defineProperty", "_regeneratorDefine", "_invoke", "enumerable", "configurable", "writable", "asyncGeneratorStep", "Promise", "resolve", "then", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "https", "getAccessToken", "createError", "domain", "version", "getUserData", "_x", "_x2", "_x3", "_getUserData", "_callee2", "req", "res", "next", "mockUserData", "authorization", "_context2", "process", "env", "NODE_ENV", "console", "log", "send", "get", "_ref", "_callee", "graphTokenResponse", "graphToken", "graphUrlSegment", "graphQueryParamSegment", "graphData", "_context", "claims", "error", "access_token", "GRAPH_URL_SEGMENT", "QUERY_PARAM_SEGMENT", "getGraphData", "code", "JSON", "stringify", "_x7", "catch", "err", "status", "message", "_x4", "_x5", "_x6", "_getGraphData", "_callee3", "accessToken", "apiUrl", "queryParams", "_context3", "reject", "options", "host", "path", "method", "headers", "Accept", "Authorization", "Expires", "Pragma", "response", "body", "on", "statusCode", "parsedBody", "parse", "Error", "statusMessage", "trim", "bodyCode", "bodyMessage", "_slicedToArray", "_arrayWithHoles", "_iterableToArrayLimit", "_unsupportedIterableToArray", "_nonIterableRest", "_arrayLikeToArray", "toString", "slice", "constructor", "name", "Array", "from", "test", "push", "isArray", "fetch", "form", "jwt", "JwksClient", "DISCOVERY_KEYS_ENDPOINT", "_getAccessToken", "scopeName", "_authorization$split", "_authorization$split2", "assertion", "tokenScopes", "accessAsUserScope", "formParams", "stsDomain", "tenant", "tokenURLSegment", "encodedForm", "tokenResponse", "json", "token_type", "expires_in", "scope", "SCOPE", "split", "decode", "scp", "find", "client_id", "CLIENT_ID", "client_secret", "CLIENT_SECRET", "grant_type", "requested_token_use", "join", "concat", "validateJwt", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "token", "validationOptions", "audience", "verify", "getSigningKeys", "sendStatus", "header", "callback", "client", "jwksUri", "getSigningKey", "kid", "key", "getPublicKey", "require", "config", "cookie<PERSON>arser", "logger", "express", "http", "getHttpsServerOptions", "app", "port", "API_PORT", "set", "use", "u<PERSON><PERSON><PERSON>", "extended", "static", "cwd", "etag", "timestamp", "Date", "toISOString", "sendFile", "_ref2", "locals", "createServer", "listen"], "sourceRoot": ""}