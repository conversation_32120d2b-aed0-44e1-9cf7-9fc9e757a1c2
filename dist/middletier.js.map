{"version": 3, "file": "middletier.js", "mappings": ";;;;;;;;;;;;;;;;;;;;0BACA,uKAAAA,CAAA,EAAAC,CAAA,EAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,WAAA,8BAAAC,EAAAN,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAE,CAAA,QAAAC,CAAA,GAAAL,CAAA,IAAAA,CAAA,CAAAM,SAAA,YAAAC,SAAA,GAAAP,CAAA,GAAAO,SAAA,EAAAC,CAAA,GAAAC,MAAA,CAAAC,MAAA,CAAAL,CAAA,CAAAC,SAAA,UAAAK,mBAAA,CAAAH,CAAA,uBAAAV,CAAA,EAAAE,CAAA,EAAAE,CAAA,QAAAE,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAI,CAAA,MAAAC,CAAA,GAAAX,CAAA,QAAAY,CAAA,OAAAC,CAAA,KAAAF,CAAA,KAAAb,CAAA,KAAAgB,CAAA,EAAApB,CAAA,EAAAqB,CAAA,EAAAC,CAAA,EAAAN,CAAA,EAAAM,CAAA,CAAAC,IAAA,CAAAvB,CAAA,MAAAsB,CAAA,WAAAA,EAAArB,CAAA,EAAAC,CAAA,WAAAM,CAAA,GAAAP,CAAA,EAAAQ,CAAA,MAAAG,CAAA,GAAAZ,CAAA,EAAAmB,CAAA,CAAAf,CAAA,GAAAF,CAAA,EAAAmB,CAAA,gBAAAC,EAAApB,CAAA,EAAAE,CAAA,SAAAK,CAAA,GAAAP,CAAA,EAAAU,CAAA,GAAAR,CAAA,EAAAH,CAAA,OAAAiB,CAAA,IAAAF,CAAA,KAAAV,CAAA,IAAAL,CAAA,GAAAgB,CAAA,CAAAO,MAAA,EAAAvB,CAAA,UAAAK,CAAA,EAAAE,CAAA,GAAAS,CAAA,CAAAhB,CAAA,GAAAqB,CAAA,GAAAH,CAAA,CAAAF,CAAA,EAAAQ,CAAA,GAAAjB,CAAA,KAAAN,CAAA,QAAAI,CAAA,GAAAmB,CAAA,KAAArB,CAAA,MAAAQ,CAAA,GAAAJ,CAAA,EAAAC,CAAA,GAAAD,CAAA,YAAAC,CAAA,WAAAD,CAAA,MAAAA,CAAA,MAAAR,CAAA,IAAAQ,CAAA,OAAAc,CAAA,MAAAhB,CAAA,GAAAJ,CAAA,QAAAoB,CAAA,GAAAd,CAAA,QAAAC,CAAA,MAAAU,CAAA,CAAAC,CAAA,GAAAhB,CAAA,EAAAe,CAAA,CAAAf,CAAA,GAAAI,CAAA,OAAAc,CAAA,GAAAG,CAAA,KAAAnB,CAAA,GAAAJ,CAAA,QAAAM,CAAA,MAAAJ,CAAA,IAAAA,CAAA,GAAAqB,CAAA,MAAAjB,CAAA,MAAAN,CAAA,EAAAM,CAAA,MAAAJ,CAAA,EAAAe,CAAA,CAAAf,CAAA,GAAAqB,CAAA,EAAAhB,CAAA,cAAAH,CAAA,IAAAJ,CAAA,aAAAmB,CAAA,QAAAH,CAAA,OAAAd,CAAA,qBAAAE,CAAA,EAAAW,CAAA,EAAAQ,CAAA,QAAAT,CAAA,YAAAU,SAAA,uCAAAR,CAAA,UAAAD,CAAA,IAAAK,CAAA,CAAAL,CAAA,EAAAQ,CAAA,GAAAhB,CAAA,GAAAQ,CAAA,EAAAL,CAAA,GAAAa,CAAA,GAAAxB,CAAA,GAAAQ,CAAA,OAAAT,CAAA,GAAAY,CAAA,MAAAM,CAAA,KAAAV,CAAA,KAAAC,CAAA,GAAAA,CAAA,QAAAA,CAAA,SAAAU,CAAA,CAAAf,CAAA,QAAAkB,CAAA,CAAAb,CAAA,EAAAG,CAAA,KAAAO,CAAA,CAAAf,CAAA,GAAAQ,CAAA,GAAAO,CAAA,CAAAC,CAAA,GAAAR,CAAA,aAAAI,CAAA,MAAAR,CAAA,QAAAC,CAAA,KAAAH,CAAA,YAAAL,CAAA,GAAAO,CAAA,CAAAF,CAAA,WAAAL,CAAA,GAAAA,CAAA,CAAA0B,IAAA,CAAAnB,CAAA,EAAAI,CAAA,UAAAc,SAAA,2CAAAzB,CAAA,CAAA2B,IAAA,SAAA3B,CAAA,EAAAW,CAAA,GAAAX,CAAA,CAAA4B,KAAA,EAAApB,CAAA,SAAAA,CAAA,oBAAAA,CAAA,KAAAR,CAAA,GAAAO,CAAA,CAAAsB,MAAA,KAAA7B,CAAA,CAAA0B,IAAA,CAAAnB,CAAA,GAAAC,CAAA,SAAAG,CAAA,GAAAc,SAAA,uCAAApB,CAAA,gBAAAG,CAAA,OAAAD,CAAA,GAAAR,CAAA,cAAAC,CAAA,IAAAiB,CAAA,GAAAC,CAAA,CAAAf,CAAA,QAAAQ,CAAA,GAAAV,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,EAAAe,CAAA,OAAAE,CAAA,kBAAApB,CAAA,IAAAO,CAAA,GAAAR,CAAA,EAAAS,CAAA,MAAAG,CAAA,GAAAX,CAAA,cAAAe,CAAA,mBAAAa,KAAA,EAAA5B,CAAA,EAAA2B,IAAA,EAAAV,CAAA,SAAAhB,CAAA,EAAAI,CAAA,EAAAE,CAAA,QAAAI,CAAA,QAAAS,CAAA,gBAAAV,UAAA,cAAAoB,kBAAA,cAAAC,2BAAA,KAAA/B,CAAA,GAAAY,MAAA,CAAAoB,cAAA,MAAAxB,CAAA,MAAAL,CAAA,IAAAH,CAAA,CAAAA,CAAA,IAAAG,CAAA,SAAAW,mBAAA,CAAAd,CAAA,OAAAG,CAAA,iCAAAH,CAAA,GAAAW,CAAA,GAAAoB,0BAAA,CAAAtB,SAAA,GAAAC,SAAA,CAAAD,SAAA,GAAAG,MAAA,CAAAC,MAAA,CAAAL,CAAA,YAAAO,EAAAhB,CAAA,WAAAa,MAAA,CAAAqB,cAAA,GAAArB,MAAA,CAAAqB,cAAA,CAAAlC,CAAA,EAAAgC,0BAAA,KAAAhC,CAAA,CAAAmC,SAAA,GAAAH,0BAAA,EAAAjB,mBAAA,CAAAf,CAAA,EAAAM,CAAA,yBAAAN,CAAA,CAAAU,SAAA,GAAAG,MAAA,CAAAC,MAAA,CAAAF,CAAA,GAAAZ,CAAA,WAAA+B,iBAAA,CAAArB,SAAA,GAAAsB,0BAAA,EAAAjB,mBAAA,CAAAH,CAAA,iBAAAoB,0BAAA,GAAAjB,mBAAA,CAAAiB,0BAAA,iBAAAD,iBAAA,GAAAA,iBAAA,CAAAK,WAAA,wBAAArB,mBAAA,CAAAiB,0BAAA,EAAA1B,CAAA,wBAAAS,mBAAA,CAAAH,CAAA,GAAAG,mBAAA,CAAAH,CAAA,EAAAN,CAAA,gBAAAS,mBAAA,CAAAH,CAAA,EAAAR,CAAA,iCAAAW,mBAAA,CAAAH,CAAA,8DAAAyB,YAAA,YAAAA,aAAA,aAAAC,CAAA,EAAA9B,CAAA,EAAA+B,CAAA,EAAAvB,CAAA;AAAA,SAAAD,oBAAAf,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAH,CAAA,QAAAO,CAAA,GAAAK,MAAA,CAAA2B,cAAA,QAAAhC,CAAA,uBAAAR,CAAA,IAAAQ,CAAA,QAAAO,mBAAA,YAAA0B,mBAAAzC,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAH,CAAA,aAAAK,EAAAJ,CAAA,EAAAE,CAAA,IAAAW,mBAAA,CAAAf,CAAA,EAAAE,CAAA,YAAAF,CAAA,gBAAA0C,OAAA,CAAAxC,CAAA,EAAAE,CAAA,EAAAJ,CAAA,SAAAE,CAAA,GAAAM,CAAA,GAAAA,CAAA,CAAAR,CAAA,EAAAE,CAAA,IAAA2B,KAAA,EAAAzB,CAAA,EAAAuC,UAAA,GAAA1C,CAAA,EAAA2C,YAAA,GAAA3C,CAAA,EAAA4C,QAAA,GAAA5C,CAAA,MAAAD,CAAA,CAAAE,CAAA,IAAAE,CAAA,IAAAE,CAAA,aAAAA,CAAA,cAAAA,CAAA,mBAAAS,mBAAA,CAAAf,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAH,CAAA;AAAA,SAAA6C,mBAAA1C,CAAA,EAAAH,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAI,CAAA,EAAAe,CAAA,EAAAZ,CAAA,cAAAD,CAAA,GAAAJ,CAAA,CAAAiB,CAAA,EAAAZ,CAAA,GAAAG,CAAA,GAAAJ,CAAA,CAAAqB,KAAA,WAAAzB,CAAA,gBAAAJ,CAAA,CAAAI,CAAA,KAAAI,CAAA,CAAAoB,IAAA,GAAA3B,CAAA,CAAAW,CAAA,IAAAmC,OAAA,CAAAC,OAAA,CAAApC,CAAA,EAAAqC,IAAA,CAAA/C,CAAA,EAAAI,CAAA;AAAA,SAAA4C,kBAAA9C,CAAA,6BAAAH,CAAA,SAAAD,CAAA,GAAAmD,SAAA,aAAAJ,OAAA,WAAA7C,CAAA,EAAAI,CAAA,QAAAe,CAAA,GAAAjB,CAAA,CAAAgD,KAAA,CAAAnD,CAAA,EAAAD,CAAA,YAAAqD,MAAAjD,CAAA,IAAA0C,kBAAA,CAAAzB,CAAA,EAAAnB,CAAA,EAAAI,CAAA,EAAA+C,KAAA,EAAAC,MAAA,UAAAlD,CAAA,cAAAkD,OAAAlD,CAAA,IAAA0C,kBAAA,CAAAzB,CAAA,EAAAnB,CAAA,EAAAI,CAAA,EAAA+C,KAAA,EAAAC,MAAA,WAAAlD,CAAA,KAAAiD,KAAA;AADA;AACA;AACA;AACA;AAC+B;AACmB;AACP;;AAE3C;;AAEA,IAAMK,MAAc,GAAG,qBAAqB;AAC5C,IAAMC,OAAe,GAAG,MAAM;AAEvB,SAAeC,WAAWA,CAAAC,EAAA,EAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,YAAA,CAAAZ,KAAA,OAAAD,SAAA;AAAA;AA4BhC,SAAAa,aAAA;EAAAA,YAAA,GAAAd,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CA5BM,SAAA0B,SAA2BC,GAAQ,EAAEC,GAAQ,EAAEC,IAAS;IAAA,IAAAC,aAAA;IAAA,OAAAhC,YAAA,GAAAC,CAAA,WAAAgC,SAAA;MAAA,kBAAAA,SAAA,CAAAlE,CAAA;QAAA;UACvDiE,aAAqB,GAAGH,GAAG,CAACK,GAAG,CAAC,eAAe,CAAC;UAAAD,SAAA,CAAAlE,CAAA;UAAA,OAEhDoD,+DAAc,CAACa,aAAa,CAAC,CAChCpB,IAAI;YAAA,IAAAuB,IAAA,GAAAtB,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAAC,SAAAkC,QAAOC,kBAAkB;cAAA,IAAAC,UAAA,EAAAC,eAAA,EAAAC,sBAAA,EAAAC,SAAA;cAAA,OAAAzC,YAAA,GAAAC,CAAA,WAAAyC,QAAA;gBAAA,kBAAAA,QAAA,CAAA3E,CAAA;kBAAA;oBAAA,MACzBsE,kBAAkB,KAAKA,kBAAkB,CAACM,MAAM,IAAIN,kBAAkB,CAACO,KAAK,CAAC;sBAAAF,QAAA,CAAA3E,CAAA;sBAAA;oBAAA;oBAC/E+D,GAAG,CAACe,IAAI,CAACR,kBAAkB,CAAC;oBAACK,QAAA,CAAA3E,CAAA;oBAAA;kBAAA;oBAEvBuE,UAAkB,GAAGD,kBAAkB,CAACS,YAAY;oBACpDP,eAAuB,GAAGQ,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,KAAK;oBAChET,sBAA8B,GAAGO,OAAO,CAACC,GAAG,CAACE,mBAAmB,IAAI,EAAE;oBAAAR,QAAA,CAAA3E,CAAA;oBAAA,OAEpDoF,YAAY,CAACb,UAAU,EAAEC,eAAe,EAAEC,sBAAsB,CAAC;kBAAA;oBAAnFC,SAAS,GAAAC,QAAA,CAAA3D,CAAA;oBAEf;oBACA;oBACA;oBACA,IAAI0D,SAAS,CAACW,IAAI,EAAE;sBAClBrB,IAAI,CAACX,wCAAW,CAACqB,SAAS,CAACW,IAAI,EAAE,wBAAwB,GAAGC,IAAI,CAACC,SAAS,CAACb,SAAS,CAAC,CAAC,CAAC;oBACzF,CAAC,MAAM;sBACLX,GAAG,CAACe,IAAI,CAACJ,SAAS,CAAC;oBACrB;kBAAC;oBAAA,OAAAC,QAAA,CAAA1D,CAAA;gBAAA;cAAA,GAAAoD,OAAA;YAAA,CAEJ;YAAA,iBAAAmB,GAAA;cAAA,OAAApB,IAAA,CAAApB,KAAA,OAAAD,SAAA;YAAA;UAAA,IAAC,CACD0C,KAAK,CAAC,UAACC,GAAG,EAAK;YACd3B,GAAG,CAAC4B,MAAM,CAAC,GAAG,CAAC,CAACb,IAAI,CAACY,GAAG,CAACE,OAAO,CAAC;YACjC;UACF,CAAC,CAAC;QAAA;UAAA,OAAA1B,SAAA,CAAAjD,CAAA;MAAA;IAAA,GAAA4C,QAAA;EAAA,CACL;EAAA,OAAAD,YAAA,CAAAZ,KAAA,OAAAD,SAAA;AAAA;AAEM,SAAeqC,YAAYA,CAAAS,GAAA,EAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,aAAA,CAAAhD,KAAA,OAAAD,SAAA;AAAA;AAmDjC,SAAAiD,cAAA;EAAAA,aAAA,GAAAlD,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAnDM,SAAA8D,SAA4BC,WAAmB,EAAEC,MAAc,EAAEC,WAAoB;IAAA,OAAAnE,YAAA,GAAAC,CAAA,WAAAmE,SAAA;MAAA,kBAAAA,SAAA,CAAArG,CAAA;QAAA;UAAA,OAAAqG,SAAA,CAAApF,CAAA,IACnF,IAAI0B,OAAO,CAAM,UAACC,OAAO,EAAE0D,MAAM,EAAK;YAC3C,IAAMC,OAA6B,GAAG;cACpCC,IAAI,EAAElD,MAAM;cACZmD,IAAI,EAAE,GAAG,GAAGlD,OAAO,GAAG4C,MAAM,GAAGC,WAAW;cAC1CM,MAAM,EAAE,KAAK;cACbC,OAAO,EAAE;gBACP,cAAc,EAAE,kBAAkB;gBAClCC,MAAM,EAAE,kBAAkB;gBAC1BC,aAAa,EAAE,SAAS,GAAGX,WAAW;gBACtC,eAAe,EAAE,8CAA8C;gBAC/DY,OAAO,EAAE,IAAI;gBACbC,MAAM,EAAE;cACV;YACF,CAAC;YAED5D,sCACM,CAACoD,OAAO,EAAE,UAACS,QAAQ,EAAK;cAC1B,IAAIC,IAAI,GAAG,EAAE;cACbD,QAAQ,CAACE,EAAE,CAAC,MAAM,EAAE,UAAChG,CAAC,EAAK;gBACzB+F,IAAI,IAAI/F,CAAC;cACX,CAAC,CAAC;cACF8F,QAAQ,CAACE,EAAE,CAAC,KAAK,EAAE,YAAM;gBACvB;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;;gBAEA,IAAIrC,KAAK;gBACT,IAAImC,QAAQ,CAACG,UAAU,KAAK,GAAG,EAAE;kBAC/B,IAAIC,UAAU,GAAG9B,IAAI,CAAC+B,KAAK,CAACJ,IAAI,CAAC;kBACjCrE,OAAO,CAACwE,UAAU,CAAC;gBACrB,CAAC,MAAM;kBACLvC,KAAK,GAAG,IAAIyC,KAAK,CAAC,CAAC;kBACnBzC,KAAK,CAACQ,IAAI,GAAG2B,QAAQ,CAACG,UAAU;kBAChCtC,KAAK,CAACe,OAAO,GAAGoB,QAAQ,CAACO,aAAa;;kBAEtC;kBACA;kBACAN,IAAI,GAAGA,IAAI,CAACO,IAAI,CAAC,CAAC;kBAClB3C,KAAK,CAAC4C,QAAQ,GAAGnC,IAAI,CAAC+B,KAAK,CAACJ,IAAI,CAAC,CAACpC,KAAK,CAACQ,IAAI;kBAC5CR,KAAK,CAAC6C,WAAW,GAAGpC,IAAI,CAAC+B,KAAK,CAACJ,IAAI,CAAC,CAACpC,KAAK,CAACe,OAAO;kBAClDhD,OAAO,CAACiC,KAAK,CAAC;gBAChB;cACF,CAAC,CAAC;YACJ,CAAC,CAAC,CACDqC,EAAE,CAAC,OAAO,EAAEZ,MAAM,CAAC;UACxB,CAAC,CAAC;MAAA;IAAA,GAAAL,QAAA;EAAA,CACH;EAAA,OAAAD,aAAA,CAAAhD,KAAA,OAAAD,SAAA;AAAA,C;;;;;;;;;;;;;;;;;;;;;;;0BC7FD,uKAAAnD,CAAA,EAAAC,CAAA,EAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,WAAA,8BAAAC,EAAAN,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAE,CAAA,QAAAC,CAAA,GAAAL,CAAA,IAAAA,CAAA,CAAAM,SAAA,YAAAC,SAAA,GAAAP,CAAA,GAAAO,SAAA,EAAAC,CAAA,GAAAC,MAAA,CAAAC,MAAA,CAAAL,CAAA,CAAAC,SAAA,UAAAK,mBAAA,CAAAH,CAAA,uBAAAV,CAAA,EAAAE,CAAA,EAAAE,CAAA,QAAAE,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAI,CAAA,MAAAC,CAAA,GAAAX,CAAA,QAAAY,CAAA,OAAAC,CAAA,KAAAF,CAAA,KAAAb,CAAA,KAAAgB,CAAA,EAAApB,CAAA,EAAAqB,CAAA,EAAAC,CAAA,EAAAN,CAAA,EAAAM,CAAA,CAAAC,IAAA,CAAAvB,CAAA,MAAAsB,CAAA,WAAAA,EAAArB,CAAA,EAAAC,CAAA,WAAAM,CAAA,GAAAP,CAAA,EAAAQ,CAAA,MAAAG,CAAA,GAAAZ,CAAA,EAAAmB,CAAA,CAAAf,CAAA,GAAAF,CAAA,EAAAmB,CAAA,gBAAAC,EAAApB,CAAA,EAAAE,CAAA,SAAAK,CAAA,GAAAP,CAAA,EAAAU,CAAA,GAAAR,CAAA,EAAAH,CAAA,OAAAiB,CAAA,IAAAF,CAAA,KAAAV,CAAA,IAAAL,CAAA,GAAAgB,CAAA,CAAAO,MAAA,EAAAvB,CAAA,UAAAK,CAAA,EAAAE,CAAA,GAAAS,CAAA,CAAAhB,CAAA,GAAAqB,CAAA,GAAAH,CAAA,CAAAF,CAAA,EAAAQ,CAAA,GAAAjB,CAAA,KAAAN,CAAA,QAAAI,CAAA,GAAAmB,CAAA,KAAArB,CAAA,MAAAQ,CAAA,GAAAJ,CAAA,EAAAC,CAAA,GAAAD,CAAA,YAAAC,CAAA,WAAAD,CAAA,MAAAA,CAAA,MAAAR,CAAA,IAAAQ,CAAA,OAAAc,CAAA,MAAAhB,CAAA,GAAAJ,CAAA,QAAAoB,CAAA,GAAAd,CAAA,QAAAC,CAAA,MAAAU,CAAA,CAAAC,CAAA,GAAAhB,CAAA,EAAAe,CAAA,CAAAf,CAAA,GAAAI,CAAA,OAAAc,CAAA,GAAAG,CAAA,KAAAnB,CAAA,GAAAJ,CAAA,QAAAM,CAAA,MAAAJ,CAAA,IAAAA,CAAA,GAAAqB,CAAA,MAAAjB,CAAA,MAAAN,CAAA,EAAAM,CAAA,MAAAJ,CAAA,EAAAe,CAAA,CAAAf,CAAA,GAAAqB,CAAA,EAAAhB,CAAA,cAAAH,CAAA,IAAAJ,CAAA,aAAAmB,CAAA,QAAAH,CAAA,OAAAd,CAAA,qBAAAE,CAAA,EAAAW,CAAA,EAAAQ,CAAA,QAAAT,CAAA,YAAAU,SAAA,uCAAAR,CAAA,UAAAD,CAAA,IAAAK,CAAA,CAAAL,CAAA,EAAAQ,CAAA,GAAAhB,CAAA,GAAAQ,CAAA,EAAAL,CAAA,GAAAa,CAAA,GAAAxB,CAAA,GAAAQ,CAAA,OAAAT,CAAA,GAAAY,CAAA,MAAAM,CAAA,KAAAV,CAAA,KAAAC,CAAA,GAAAA,CAAA,QAAAA,CAAA,SAAAU,CAAA,CAAAf,CAAA,QAAAkB,CAAA,CAAAb,CAAA,EAAAG,CAAA,KAAAO,CAAA,CAAAf,CAAA,GAAAQ,CAAA,GAAAO,CAAA,CAAAC,CAAA,GAAAR,CAAA,aAAAI,CAAA,MAAAR,CAAA,QAAAC,CAAA,KAAAH,CAAA,YAAAL,CAAA,GAAAO,CAAA,CAAAF,CAAA,WAAAL,CAAA,GAAAA,CAAA,CAAA0B,IAAA,CAAAnB,CAAA,EAAAI,CAAA,UAAAc,SAAA,2CAAAzB,CAAA,CAAA2B,IAAA,SAAA3B,CAAA,EAAAW,CAAA,GAAAX,CAAA,CAAA4B,KAAA,EAAApB,CAAA,SAAAA,CAAA,oBAAAA,CAAA,KAAAR,CAAA,GAAAO,CAAA,CAAAsB,MAAA,KAAA7B,CAAA,CAAA0B,IAAA,CAAAnB,CAAA,GAAAC,CAAA,SAAAG,CAAA,GAAAc,SAAA,uCAAApB,CAAA,gBAAAG,CAAA,OAAAD,CAAA,GAAAR,CAAA,cAAAC,CAAA,IAAAiB,CAAA,GAAAC,CAAA,CAAAf,CAAA,QAAAQ,CAAA,GAAAV,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,EAAAe,CAAA,OAAAE,CAAA,kBAAApB,CAAA,IAAAO,CAAA,GAAAR,CAAA,EAAAS,CAAA,MAAAG,CAAA,GAAAX,CAAA,cAAAe,CAAA,mBAAAa,KAAA,EAAA5B,CAAA,EAAA2B,IAAA,EAAAV,CAAA,SAAAhB,CAAA,EAAAI,CAAA,EAAAE,CAAA,QAAAI,CAAA,QAAAS,CAAA,gBAAAV,UAAA,cAAAoB,kBAAA,cAAAC,2BAAA,KAAA/B,CAAA,GAAAY,MAAA,CAAAoB,cAAA,MAAAxB,CAAA,MAAAL,CAAA,IAAAH,CAAA,CAAAA,CAAA,IAAAG,CAAA,SAAAW,mBAAA,CAAAd,CAAA,OAAAG,CAAA,iCAAAH,CAAA,GAAAW,CAAA,GAAAoB,0BAAA,CAAAtB,SAAA,GAAAC,SAAA,CAAAD,SAAA,GAAAG,MAAA,CAAAC,MAAA,CAAAL,CAAA,YAAAO,EAAAhB,CAAA,WAAAa,MAAA,CAAAqB,cAAA,GAAArB,MAAA,CAAAqB,cAAA,CAAAlC,CAAA,EAAAgC,0BAAA,KAAAhC,CAAA,CAAAmC,SAAA,GAAAH,0BAAA,EAAAjB,mBAAA,CAAAf,CAAA,EAAAM,CAAA,yBAAAN,CAAA,CAAAU,SAAA,GAAAG,MAAA,CAAAC,MAAA,CAAAF,CAAA,GAAAZ,CAAA,WAAA+B,iBAAA,CAAArB,SAAA,GAAAsB,0BAAA,EAAAjB,mBAAA,CAAAH,CAAA,iBAAAoB,0BAAA,GAAAjB,mBAAA,CAAAiB,0BAAA,iBAAAD,iBAAA,GAAAA,iBAAA,CAAAK,WAAA,wBAAArB,mBAAA,CAAAiB,0BAAA,EAAA1B,CAAA,wBAAAS,mBAAA,CAAAH,CAAA,GAAAG,mBAAA,CAAAH,CAAA,EAAAN,CAAA,gBAAAS,mBAAA,CAAAH,CAAA,EAAAR,CAAA,iCAAAW,mBAAA,CAAAH,CAAA,8DAAAyB,YAAA,YAAAA,aAAA,aAAAC,CAAA,EAAA9B,CAAA,EAAA+B,CAAA,EAAAvB,CAAA;AAAA,SAAAD,oBAAAf,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAH,CAAA,QAAAO,CAAA,GAAAK,MAAA,CAAA2B,cAAA,QAAAhC,CAAA,uBAAAR,CAAA,IAAAQ,CAAA,QAAAO,mBAAA,YAAA0B,mBAAAzC,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAH,CAAA,aAAAK,EAAAJ,CAAA,EAAAE,CAAA,IAAAW,mBAAA,CAAAf,CAAA,EAAAE,CAAA,YAAAF,CAAA,gBAAA0C,OAAA,CAAAxC,CAAA,EAAAE,CAAA,EAAAJ,CAAA,SAAAE,CAAA,GAAAM,CAAA,GAAAA,CAAA,CAAAR,CAAA,EAAAE,CAAA,IAAA2B,KAAA,EAAAzB,CAAA,EAAAuC,UAAA,GAAA1C,CAAA,EAAA2C,YAAA,GAAA3C,CAAA,EAAA4C,QAAA,GAAA5C,CAAA,MAAAD,CAAA,CAAAE,CAAA,IAAAE,CAAA,IAAAE,CAAA,aAAAA,CAAA,cAAAA,CAAA,mBAAAS,mBAAA,CAAAf,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAH,CAAA;AAAA,SAAA8H,eAAA7H,CAAA,EAAAF,CAAA,WAAAgI,eAAA,CAAA9H,CAAA,KAAA+H,qBAAA,CAAA/H,CAAA,EAAAF,CAAA,KAAAkI,2BAAA,CAAAhI,CAAA,EAAAF,CAAA,KAAAmI,gBAAA;AAAA,SAAAA,iBAAA,cAAAzG,SAAA;AAAA,SAAAwG,4BAAAhI,CAAA,EAAAmB,CAAA,QAAAnB,CAAA,2BAAAA,CAAA,SAAAkI,iBAAA,CAAAlI,CAAA,EAAAmB,CAAA,OAAApB,CAAA,MAAAoI,QAAA,CAAA1G,IAAA,CAAAzB,CAAA,EAAAoI,KAAA,6BAAArI,CAAA,IAAAC,CAAA,CAAAqI,WAAA,KAAAtI,CAAA,GAAAC,CAAA,CAAAqI,WAAA,CAAAC,IAAA,aAAAvI,CAAA,cAAAA,CAAA,GAAAwI,KAAA,CAAAC,IAAA,CAAAxI,CAAA,oBAAAD,CAAA,+CAAA0I,IAAA,CAAA1I,CAAA,IAAAmI,iBAAA,CAAAlI,CAAA,EAAAmB,CAAA;AAAA,SAAA+G,kBAAAlI,CAAA,EAAAmB,CAAA,aAAAA,CAAA,IAAAA,CAAA,GAAAnB,CAAA,CAAAsB,MAAA,MAAAH,CAAA,GAAAnB,CAAA,CAAAsB,MAAA,YAAAxB,CAAA,MAAAI,CAAA,GAAAqI,KAAA,CAAApH,CAAA,GAAArB,CAAA,GAAAqB,CAAA,EAAArB,CAAA,IAAAI,CAAA,CAAAJ,CAAA,IAAAE,CAAA,CAAAF,CAAA,UAAAI,CAAA;AAAA,SAAA6H,sBAAA/H,CAAA,EAAAuB,CAAA,QAAAxB,CAAA,WAAAC,CAAA,gCAAAC,MAAA,IAAAD,CAAA,CAAAC,MAAA,CAAAE,QAAA,KAAAH,CAAA,4BAAAD,CAAA,QAAAD,CAAA,EAAAI,CAAA,EAAAI,CAAA,EAAAI,CAAA,EAAAS,CAAA,OAAAL,CAAA,OAAAV,CAAA,iBAAAE,CAAA,IAAAP,CAAA,GAAAA,CAAA,CAAA0B,IAAA,CAAAzB,CAAA,GAAAkE,IAAA,QAAA3C,CAAA,QAAAZ,MAAA,CAAAZ,CAAA,MAAAA,CAAA,UAAAe,CAAA,uBAAAA,CAAA,IAAAhB,CAAA,GAAAQ,CAAA,CAAAmB,IAAA,CAAA1B,CAAA,GAAA2B,IAAA,MAAAP,CAAA,CAAAuH,IAAA,CAAA5I,CAAA,CAAA6B,KAAA,GAAAR,CAAA,CAAAG,MAAA,KAAAC,CAAA,GAAAT,CAAA,iBAAAd,CAAA,IAAAI,CAAA,OAAAF,CAAA,GAAAF,CAAA,yBAAAc,CAAA,YAAAf,CAAA,CAAA6B,MAAA,KAAAlB,CAAA,GAAAX,CAAA,CAAA6B,MAAA,IAAAjB,MAAA,CAAAD,CAAA,MAAAA,CAAA,2BAAAN,CAAA,QAAAF,CAAA,aAAAiB,CAAA;AAAA,SAAA2G,gBAAA9H,CAAA,QAAAuI,KAAA,CAAAI,OAAA,CAAA3I,CAAA,UAAAA,CAAA;AAAA,SAAA4C,mBAAA1C,CAAA,EAAAH,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAI,CAAA,EAAAe,CAAA,EAAAZ,CAAA,cAAAD,CAAA,GAAAJ,CAAA,CAAAiB,CAAA,EAAAZ,CAAA,GAAAG,CAAA,GAAAJ,CAAA,CAAAqB,KAAA,WAAAzB,CAAA,gBAAAJ,CAAA,CAAAI,CAAA,KAAAI,CAAA,CAAAoB,IAAA,GAAA3B,CAAA,CAAAW,CAAA,IAAAmC,OAAA,CAAAC,OAAA,CAAApC,CAAA,EAAAqC,IAAA,CAAA/C,CAAA,EAAAI,CAAA;AAAA,SAAA4C,kBAAA9C,CAAA,6BAAAH,CAAA,SAAAD,CAAA,GAAAmD,SAAA,aAAAJ,OAAA,WAAA7C,CAAA,EAAAI,CAAA,QAAAe,CAAA,GAAAjB,CAAA,CAAAgD,KAAA,CAAAnD,CAAA,EAAAD,CAAA,YAAAqD,MAAAjD,CAAA,IAAA0C,kBAAA,CAAAzB,CAAA,EAAAnB,CAAA,EAAAI,CAAA,EAAA+C,KAAA,EAAAC,MAAA,UAAAlD,CAAA,cAAAkD,OAAAlD,CAAA,IAAA0C,kBAAA,CAAAzB,CAAA,EAAAnB,CAAA,EAAAI,CAAA,EAAA+C,KAAA,EAAAC,MAAA,WAAAlD,CAAA,KAAAiD,KAAA;AADA;AACA;AACA;AACA;AACA;;AAE+B;AACI;AACJ;AACO;;AAEtC;;AAEA,IAAM6F,uBAAuB,GAAG,8DAA8D;AAEvF,SAAe1F,cAAcA,CAAAK,EAAA;EAAA,OAAAsF,eAAA,CAAA/F,KAAA,OAAAD,SAAA;AAAA;AAuCnC,SAAAgG,gBAAA;EAAAA,eAAA,GAAAjG,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAvCM,SAAAkC,QAA8BJ,aAAqB;IAAA,IAAAY,KAAA,EAAAmE,SAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,SAAA,EAAAC,WAAA,EAAAC,iBAAA,EAAAC,UAAA,EAAAC,SAAA,EAAAC,MAAA,EAAAC,eAAA,EAAAC,WAAA,EAAAC,aAAA,EAAAC,IAAA;IAAA,OAAA3H,YAAA,GAAAC,CAAA,WAAAyC,QAAA;MAAA,kBAAAA,QAAA,CAAA3E,CAAA;QAAA;UAAA,IACnDiE,aAAa;YAAAU,QAAA,CAAA3E,CAAA;YAAA;UAAA;UACZ6E,KAAK,GAAG,IAAIyC,KAAK,CAAC,oCAAoC,CAAC;UAAA,OAAA3C,QAAA,CAAA1D,CAAA,IACpD0B,OAAO,CAAC2D,MAAM,CAACzB,KAAK,CAAC;QAAA;UAEtBmE,SAAiB,GAAGhE,OAAO,CAACC,GAAG,CAAC4E,KAAK,IAAI,WAAW;UAAAZ,oBAAA,GACvBhF,aAAa,CAAC6F,KAAK,CAAC,GAAG,CAAC,EAAAZ,qBAAA,GAAAvB,cAAA,CAAAsB,oBAAA,MAAlD,YAAaE,SAAS,GAAAD,qBAAA;UAEzBE,WAAW,GAAIR,0DAAU,CAACO,SAAS,CAAC,CAAoBa,GAAG,CAACF,KAAK,CAAC,GAAG,CAAC;UACtET,iBAAiB,GAAGD,WAAW,CAACa,IAAI,CAAC,UAACC,KAAK;YAAA,OAAKA,KAAK,KAAK,gBAAgB;UAAA,EAAC;UAAA,IAC5Eb,iBAAiB;YAAA1E,QAAA,CAAA3E,CAAA;YAAA;UAAA;UAAA,MACd,IAAIsH,KAAK,CAAC,wBAAwB,CAAC;QAAA;UAGrCgC,UAAU,GAAG;YACjBa,SAAS,EAAEnF,OAAO,CAACC,GAAG,CAACmF,SAAS;YAChCC,aAAa,EAAErF,OAAO,CAACC,GAAG,CAACqF,aAAa;YACxCC,UAAU,EAAE,6CAA6C;YACzDpB,SAAS,EAAEA,SAAS;YACpBqB,mBAAmB,EAAE,cAAc;YACnCN,KAAK,EAAE,CAAClB,SAAS,CAAC,CAACyB,IAAI,CAAC,GAAG;UAC7B,CAAC;UAEKlB,SAAiB,GAAG,mCAAmC;UACvDC,MAAc,GAAG,QAAQ;UACzBC,eAAuB,GAAG,mBAAmB;UAC7CC,WAAW,GAAGf,sDAAI,CAACW,UAAU,CAAC;UAAA3E,QAAA,CAAA3E,CAAA;UAAA,OAER0I,iDAAK,IAAAgC,MAAA,CAAInB,SAAS,OAAAmB,MAAA,CAAIlB,MAAM,OAAAkB,MAAA,CAAIjB,eAAe,GAAI;YAC7E/C,MAAM,EAAE,MAAM;YACdO,IAAI,EAAEyC,WAAW;YACjB/C,OAAO,EAAE;cACPC,MAAM,EAAE,kBAAkB;cAC1B,cAAc,EAAE;YAClB;UACF,CAAC,CAAC;QAAA;UAPI+C,aAAa,GAAAhF,QAAA,CAAA3D,CAAA;UAAA2D,QAAA,CAAA3E,CAAA;UAAA,OAQA2J,aAAa,CAACC,IAAI,CAAC,CAAC;QAAA;UAAjCA,IAAI,GAAAjF,QAAA,CAAA3D,CAAA;UAAA,OAAA2D,QAAA,CAAA1D,CAAA,IACH2I,IAAI;QAAA;UAAA,OAAAjF,QAAA,CAAA1D,CAAA;MAAA;IAAA,GAAAoD,OAAA;EAAA,CAEd;EAAA,OAAA0E,eAAA,CAAA/F,KAAA,OAAAD,SAAA;AAAA;AAEM,SAAS4H,WAAWA,CAAC7G,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAQ;EAChD,IAAM4G,UAAU,GAAG9G,GAAG,CAAC6C,OAAO,CAAC1C,aAAa;EAC5C,IAAI2G,UAAU,EAAE;IACd,IAAMC,KAAK,GAAGD,UAAU,CAACd,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAEtC,IAAMgB,iBAAiB,GAAG;MACxBC,QAAQ,EAAE/F,OAAO,CAACC,GAAG,CAACmF;IACxB,CAAC;IAEDxB,0DAAU,CAACiC,KAAK,EAAEI,cAAc,EAAEH,iBAAiB,EAAE,UAACpF,GAAG,EAAK;MAC5D,IAAIA,GAAG,EAAE;QACPwF,OAAO,CAACC,GAAG,CAACzF,GAAG,CAAC;QAChB,OAAO3B,GAAG,CAACqH,UAAU,CAAC,GAAG,CAAC;MAC5B;MAEApH,IAAI,CAAC,CAAC;IACR,CAAC,CAAC;EACJ;AACF;AAEA,SAASiH,cAAcA,CAACI,MAAW,EAAEC,QAAa,EAAE;EAClD,IAAIC,MAAkB,GAAG,IAAI1C,gDAAU,CAAC;IACtC2C,OAAO,EAAE1C;EACX,CAAC,CAAC;EAEFyC,MAAM,CAACE,aAAa,CAACJ,MAAM,CAACK,GAAG,EAAE,UAAUhG,GAAG,EAAEiG,GAAG,EAAE;IACnDL,QAAQ,CAAC,IAAI,EAAEK,GAAG,CAACC,YAAY,CAAC,CAAC,CAAC;EACpC,CAAC,CAAC;AACJ,C;;;;;;;;;;ACpFA,0C;;;;;;;;;;ACAA,mC;;;;;;;;;;ACAA,oC;;;;;;;;;;ACAA,4C;;;;;;;;;;ACAA,wC;;;;;;;;;;ACAA,kC;;;;;;;;;;ACAA,yC;;;;;;;;;;ACAA,qC;;;;;;;;;;ACAA,mC;;;;;;;;;;ACAA,uC;;;;;;;;;;ACAA,mD;;;;;;;;;;ACAA,iC;;;;;;UCAA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA;WACA,iCAAiC,WAAW;WAC5C;WACA,E;;;;;WCPA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA,E;;;;;WCPA,wF;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D,E;;;;;;;;;;;;;;;;;;;;;;;;;;;0BCLA,uKAAAhM,CAAA,EAAAC,CAAA,EAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,WAAA,8BAAAC,EAAAN,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAE,CAAA,QAAAC,CAAA,GAAAL,CAAA,IAAAA,CAAA,CAAAM,SAAA,YAAAC,SAAA,GAAAP,CAAA,GAAAO,SAAA,EAAAC,CAAA,GAAAC,MAAA,CAAAC,MAAA,CAAAL,CAAA,CAAAC,SAAA,UAAAK,mBAAA,CAAAH,CAAA,uBAAAV,CAAA,EAAAE,CAAA,EAAAE,CAAA,QAAAE,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAI,CAAA,MAAAC,CAAA,GAAAX,CAAA,QAAAY,CAAA,OAAAC,CAAA,KAAAF,CAAA,KAAAb,CAAA,KAAAgB,CAAA,EAAApB,CAAA,EAAAqB,CAAA,EAAAC,CAAA,EAAAN,CAAA,EAAAM,CAAA,CAAAC,IAAA,CAAAvB,CAAA,MAAAsB,CAAA,WAAAA,EAAArB,CAAA,EAAAC,CAAA,WAAAM,CAAA,GAAAP,CAAA,EAAAQ,CAAA,MAAAG,CAAA,GAAAZ,CAAA,EAAAmB,CAAA,CAAAf,CAAA,GAAAF,CAAA,EAAAmB,CAAA,gBAAAC,EAAApB,CAAA,EAAAE,CAAA,SAAAK,CAAA,GAAAP,CAAA,EAAAU,CAAA,GAAAR,CAAA,EAAAH,CAAA,OAAAiB,CAAA,IAAAF,CAAA,KAAAV,CAAA,IAAAL,CAAA,GAAAgB,CAAA,CAAAO,MAAA,EAAAvB,CAAA,UAAAK,CAAA,EAAAE,CAAA,GAAAS,CAAA,CAAAhB,CAAA,GAAAqB,CAAA,GAAAH,CAAA,CAAAF,CAAA,EAAAQ,CAAA,GAAAjB,CAAA,KAAAN,CAAA,QAAAI,CAAA,GAAAmB,CAAA,KAAArB,CAAA,MAAAQ,CAAA,GAAAJ,CAAA,EAAAC,CAAA,GAAAD,CAAA,YAAAC,CAAA,WAAAD,CAAA,MAAAA,CAAA,MAAAR,CAAA,IAAAQ,CAAA,OAAAc,CAAA,MAAAhB,CAAA,GAAAJ,CAAA,QAAAoB,CAAA,GAAAd,CAAA,QAAAC,CAAA,MAAAU,CAAA,CAAAC,CAAA,GAAAhB,CAAA,EAAAe,CAAA,CAAAf,CAAA,GAAAI,CAAA,OAAAc,CAAA,GAAAG,CAAA,KAAAnB,CAAA,GAAAJ,CAAA,QAAAM,CAAA,MAAAJ,CAAA,IAAAA,CAAA,GAAAqB,CAAA,MAAAjB,CAAA,MAAAN,CAAA,EAAAM,CAAA,MAAAJ,CAAA,EAAAe,CAAA,CAAAf,CAAA,GAAAqB,CAAA,EAAAhB,CAAA,cAAAH,CAAA,IAAAJ,CAAA,aAAAmB,CAAA,QAAAH,CAAA,OAAAd,CAAA,qBAAAE,CAAA,EAAAW,CAAA,EAAAQ,CAAA,QAAAT,CAAA,YAAAU,SAAA,uCAAAR,CAAA,UAAAD,CAAA,IAAAK,CAAA,CAAAL,CAAA,EAAAQ,CAAA,GAAAhB,CAAA,GAAAQ,CAAA,EAAAL,CAAA,GAAAa,CAAA,GAAAxB,CAAA,GAAAQ,CAAA,OAAAT,CAAA,GAAAY,CAAA,MAAAM,CAAA,KAAAV,CAAA,KAAAC,CAAA,GAAAA,CAAA,QAAAA,CAAA,SAAAU,CAAA,CAAAf,CAAA,QAAAkB,CAAA,CAAAb,CAAA,EAAAG,CAAA,KAAAO,CAAA,CAAAf,CAAA,GAAAQ,CAAA,GAAAO,CAAA,CAAAC,CAAA,GAAAR,CAAA,aAAAI,CAAA,MAAAR,CAAA,QAAAC,CAAA,KAAAH,CAAA,YAAAL,CAAA,GAAAO,CAAA,CAAAF,CAAA,WAAAL,CAAA,GAAAA,CAAA,CAAA0B,IAAA,CAAAnB,CAAA,EAAAI,CAAA,UAAAc,SAAA,2CAAAzB,CAAA,CAAA2B,IAAA,SAAA3B,CAAA,EAAAW,CAAA,GAAAX,CAAA,CAAA4B,KAAA,EAAApB,CAAA,SAAAA,CAAA,oBAAAA,CAAA,KAAAR,CAAA,GAAAO,CAAA,CAAAsB,MAAA,KAAA7B,CAAA,CAAA0B,IAAA,CAAAnB,CAAA,GAAAC,CAAA,SAAAG,CAAA,GAAAc,SAAA,uCAAApB,CAAA,gBAAAG,CAAA,OAAAD,CAAA,GAAAR,CAAA,cAAAC,CAAA,IAAAiB,CAAA,GAAAC,CAAA,CAAAf,CAAA,QAAAQ,CAAA,GAAAV,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,EAAAe,CAAA,OAAAE,CAAA,kBAAApB,CAAA,IAAAO,CAAA,GAAAR,CAAA,EAAAS,CAAA,MAAAG,CAAA,GAAAX,CAAA,cAAAe,CAAA,mBAAAa,KAAA,EAAA5B,CAAA,EAAA2B,IAAA,EAAAV,CAAA,SAAAhB,CAAA,EAAAI,CAAA,EAAAE,CAAA,QAAAI,CAAA,QAAAS,CAAA,gBAAAV,UAAA,cAAAoB,kBAAA,cAAAC,2BAAA,KAAA/B,CAAA,GAAAY,MAAA,CAAAoB,cAAA,MAAAxB,CAAA,MAAAL,CAAA,IAAAH,CAAA,CAAAA,CAAA,IAAAG,CAAA,SAAAW,mBAAA,CAAAd,CAAA,OAAAG,CAAA,iCAAAH,CAAA,GAAAW,CAAA,GAAAoB,0BAAA,CAAAtB,SAAA,GAAAC,SAAA,CAAAD,SAAA,GAAAG,MAAA,CAAAC,MAAA,CAAAL,CAAA,YAAAO,EAAAhB,CAAA,WAAAa,MAAA,CAAAqB,cAAA,GAAArB,MAAA,CAAAqB,cAAA,CAAAlC,CAAA,EAAAgC,0BAAA,KAAAhC,CAAA,CAAAmC,SAAA,GAAAH,0BAAA,EAAAjB,mBAAA,CAAAf,CAAA,EAAAM,CAAA,yBAAAN,CAAA,CAAAU,SAAA,GAAAG,MAAA,CAAAC,MAAA,CAAAF,CAAA,GAAAZ,CAAA,WAAA+B,iBAAA,CAAArB,SAAA,GAAAsB,0BAAA,EAAAjB,mBAAA,CAAAH,CAAA,iBAAAoB,0BAAA,GAAAjB,mBAAA,CAAAiB,0BAAA,iBAAAD,iBAAA,GAAAA,iBAAA,CAAAK,WAAA,wBAAArB,mBAAA,CAAAiB,0BAAA,EAAA1B,CAAA,wBAAAS,mBAAA,CAAAH,CAAA,GAAAG,mBAAA,CAAAH,CAAA,EAAAN,CAAA,gBAAAS,mBAAA,CAAAH,CAAA,EAAAR,CAAA,iCAAAW,mBAAA,CAAAH,CAAA,8DAAAyB,YAAA,YAAAA,aAAA,aAAAC,CAAA,EAAA9B,CAAA,EAAA+B,CAAA,EAAAvB,CAAA;AAAA,SAAAD,oBAAAf,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAH,CAAA,QAAAO,CAAA,GAAAK,MAAA,CAAA2B,cAAA,QAAAhC,CAAA,uBAAAR,CAAA,IAAAQ,CAAA,QAAAO,mBAAA,YAAA0B,mBAAAzC,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAH,CAAA,aAAAK,EAAAJ,CAAA,EAAAE,CAAA,IAAAW,mBAAA,CAAAf,CAAA,EAAAE,CAAA,YAAAF,CAAA,gBAAA0C,OAAA,CAAAxC,CAAA,EAAAE,CAAA,EAAAJ,CAAA,SAAAE,CAAA,GAAAM,CAAA,GAAAA,CAAA,CAAAR,CAAA,EAAAE,CAAA,IAAA2B,KAAA,EAAAzB,CAAA,EAAAuC,UAAA,GAAA1C,CAAA,EAAA2C,YAAA,GAAA3C,CAAA,EAAA4C,QAAA,GAAA5C,CAAA,MAAAD,CAAA,CAAAE,CAAA,IAAAE,CAAA,IAAAE,CAAA,aAAAA,CAAA,cAAAA,CAAA,mBAAAS,mBAAA,CAAAf,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAH,CAAA;AAAA,SAAA6C,mBAAA1C,CAAA,EAAAH,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAI,CAAA,EAAAe,CAAA,EAAAZ,CAAA,cAAAD,CAAA,GAAAJ,CAAA,CAAAiB,CAAA,EAAAZ,CAAA,GAAAG,CAAA,GAAAJ,CAAA,CAAAqB,KAAA,WAAAzB,CAAA,gBAAAJ,CAAA,CAAAI,CAAA,KAAAI,CAAA,CAAAoB,IAAA,GAAA3B,CAAA,CAAAW,CAAA,IAAAmC,OAAA,CAAAC,OAAA,CAAApC,CAAA,EAAAqC,IAAA,CAAA/C,CAAA,EAAAI,CAAA;AAAA,SAAA4C,kBAAA9C,CAAA,6BAAAH,CAAA,SAAAD,CAAA,GAAAmD,SAAA,aAAAJ,OAAA,WAAA7C,CAAA,EAAAI,CAAA,QAAAe,CAAA,GAAAjB,CAAA,CAAAgD,KAAA,CAAAnD,CAAA,EAAAD,CAAA,YAAAqD,MAAAjD,CAAA,IAAA0C,kBAAA,CAAAzB,CAAA,EAAAnB,CAAA,EAAAI,CAAA,EAAA+C,KAAA,EAAAC,MAAA,UAAAlD,CAAA,cAAAkD,OAAAlD,CAAA,IAAA0C,kBAAA,CAAAzB,CAAA,EAAAnB,CAAA,EAAAI,CAAA,EAAA+C,KAAA,EAAAC,MAAA,WAAAlD,CAAA,KAAAiD,KAAA;AADA;AACA;AACA;AACA;AACA;;AAEA,IAAI+B,IAAqC,EAAE;EACzC8G,oDAAwB,CAAC,CAAC;AAC5B;AAC2C;AACd;AACiB;AACb;AACH;AACJ;AACqC;AAChB;AACA;;AAE/C;;AAEA,IAAMM,GAAG,GAAGF,8CAAO,CAAC,CAAC;AACrB,IAAMG,IAAqB,GAAGrH,OAAO,CAACC,GAAG,CAACqH,QAAQ,IAAI,MAAM;AAE5DF,GAAG,CAACG,GAAG,CAAC,MAAM,EAAEF,IAAI,CAAC;;AAErB;AACAD,GAAG,CAACG,GAAG,CAAC,OAAO,EAAE9F,sCAAS,CAAC+F,SAAS,EAAE,OAAO,CAAC,CAAC;AAC/CJ,GAAG,CAACG,GAAG,CAAC,aAAa,EAAE,KAAK,CAAC;AAE7BH,GAAG,CAACK,GAAG,CAACR,mCAAM,CAAC,KAAK,CAAC,CAAC;AACtBG,GAAG,CAACK,GAAG,CAACP,mDAAY,CAAC,CAAC,CAAC;AACvBE,GAAG,CAACK,GAAG,CAACP,yDAAkB,CAAC;EAAES,QAAQ,EAAE;AAAM,CAAC,CAAC,CAAC;AAChDP,GAAG,CAACK,GAAG,CAACT,0CAAY,CAAC,CAAC,CAAC;;AAEvB;AACA,IAAIhH,IAAqC,EAAE;EACzCoH,GAAG,CAACK,GAAG,CAACP,wDAAc,CAACzF,sCAAS,CAACzB,OAAO,CAAC6H,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE;IAAEC,IAAI,EAAE;EAAM,CAAC,CAAC,CAAC;EAE1EV,GAAG,CAACK,GAAG,CAAC,UAAU3I,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAE;IAChCD,GAAG,CAACsH,MAAM,CAAC,eAAe,EAAE,8CAA8C,CAAC;IAC3EtH,GAAG,CAACsH,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC;IAC3BtH,GAAG,CAACsH,MAAM,CAAC,QAAQ,EAAE,UAAU,CAAC;IAChCrH,IAAI,CAAC,CAAC;EACR,CAAC,CAAC;AACJ,CAAC,MAAM;AAAA,EAGN;AAED,IAAM+I,WAAW,GAAGb,qDAAc,CAAC,CAAC;AACpCa,WAAW,CAAC5I,GAAG,CAAC,GAAG,EAAE,UAAUL,GAAG,EAAEC,GAAG,EAAE;EACvCA,GAAG,CAACkJ,MAAM,CAAC,gBAAgB,CAAC;AAC9B,CAAC,CAAC;AAEFb,GAAG,CAACK,GAAG,CAAC,GAAG,EAAEM,WAAW,CAAC;;AAEzB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACAX,GAAG,CAACjI,GAAG,CAAC,cAAc,EAAEwG,wDAAW,EAAEnH,wDAAW,CAAC;;AAEjD;AACA4I,GAAG,CAACjI,GAAG,CAAC,gBAAgB;EAAA,IAAAC,IAAA,GAAAtB,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAAE,SAAAkC,QAAOP,GAAQ,EAAEC,GAAQ;IAAA,OAAA9B,YAAA,GAAAC,CAAA,WAAAyC,QAAA;MAAA,kBAAAA,QAAA,CAAA3E,CAAA;QAAA;UAAA,OAAA2E,QAAA,CAAA1D,CAAA,IAC1C8C,GAAG,CAACmJ,QAAQ,CAAC,eAAe,CAAC;MAAA;IAAA,GAAA7I,OAAA;EAAA,CACrC;EAAA,iBAAAZ,EAAA,EAAAC,GAAA;IAAA,OAAAU,IAAA,CAAApB,KAAA,OAAAD,SAAA;EAAA;AAAA,IAAC;AAEFqJ,GAAG,CAACjI,GAAG,CAAC,0BAA0B;EAAA,IAAAgJ,KAAA,GAAArK,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAAE,SAAA0B,SAAOC,GAAQ,EAAEC,GAAQ;IAAA,OAAA9B,YAAA,GAAAC,CAAA,WAAAgC,SAAA;MAAA,kBAAAA,SAAA,CAAAlE,CAAA;QAAA;UAAA,OAAAkE,SAAA,CAAAjD,CAAA,IACpD8C,GAAG,CAACmJ,QAAQ,CAAC,yBAAyB,CAAC;MAAA;IAAA,GAAArJ,QAAA;EAAA,CAC/C;EAAA,iBAAAF,GAAA,EAAAkC,GAAA;IAAA,OAAAsH,KAAA,CAAAnK,KAAA,OAAAD,SAAA;EAAA;AAAA,IAAC;;AAEF;AACAqJ,GAAG,CAACK,GAAG,CAAC,UAAU3I,GAAQ,EAAEC,GAAQ,EAAEC,IAAS,EAAE;EAC/CA,IAAI,CAACX,wCAAW,CAAC,GAAG,CAAC,CAAC;AACxB,CAAC,CAAC;;AAEF;AACA+I,GAAG,CAACK,GAAG,CAAC,UAAU/G,GAAQ,EAAE5B,GAAQ,EAAEC,GAAQ,EAAE;EAC9C;EACAA,GAAG,CAACqJ,MAAM,CAACxH,OAAO,GAAGF,GAAG,CAACE,OAAO;EAChC7B,GAAG,CAACqJ,MAAM,CAACvI,KAAK,GAAGf,GAAG,CAACsI,GAAG,CAACjI,GAAG,CAAC,KAAK,CAAC,KAAK,aAAa,GAAGuB,GAAG,GAAG,CAAC,CAAC;;EAElE;EACA3B,GAAG,CAAC4B,MAAM,CAACD,GAAG,CAACC,MAAM,IAAI,GAAG,CAAC;EAC7B5B,GAAG,CAACkJ,MAAM,CAAC,OAAO,CAAC;AACrB,CAAC,CAAC;AAEFd,6EAAqB,CAAC,CAAC,CAACtJ,IAAI,CAAC,UAAC0D,OAAO,EAAK;EACxCpD,yDACe,CAACoD,OAAO,EAAE6F,GAAG,CAAC,CAC1BkB,MAAM,CAACjB,IAAI,EAAE;IAAA,OAAMnB,OAAO,CAACC,GAAG,sBAAAT,MAAA,CAAsB2B,IAAI,UAAA3B,MAAA,CAAO1F,aAAoB,UAAO,CAAC;EAAA,EAAC;AACjG,CAAC,CAAC,C", "sources": ["webpack://office-addin-taskpane-sso/./src/middle-tier/msgraph-helper.ts", "webpack://office-addin-taskpane-sso/./src/middle-tier/ssoauth-helper.ts", "webpack://office-addin-taskpane-sso/external commonjs \"cookie-parser\"", "webpack://office-addin-taskpane-sso/external commonjs \"dotenv\"", "webpack://office-addin-taskpane-sso/external commonjs \"express\"", "webpack://office-addin-taskpane-sso/external commonjs \"form-urlencoded\"", "webpack://office-addin-taskpane-sso/external commonjs \"http-errors\"", "webpack://office-addin-taskpane-sso/external node-commonjs \"https\"", "webpack://office-addin-taskpane-sso/external commonjs \"jsonwebtoken\"", "webpack://office-addin-taskpane-sso/external commonjs \"jwks-rsa\"", "webpack://office-addin-taskpane-sso/external commonjs \"morgan\"", "webpack://office-addin-taskpane-sso/external commonjs \"node-fetch\"", "webpack://office-addin-taskpane-sso/external commonjs \"office-addin-dev-certs\"", "webpack://office-addin-taskpane-sso/external commonjs \"path\"", "webpack://office-addin-taskpane-sso/webpack/bootstrap", "webpack://office-addin-taskpane-sso/webpack/runtime/compat get default export", "webpack://office-addin-taskpane-sso/webpack/runtime/define property getters", "webpack://office-addin-taskpane-sso/webpack/runtime/hasOwnProperty shorthand", "webpack://office-addin-taskpane-sso/webpack/runtime/make namespace object", "webpack://office-addin-taskpane-sso/./src/middle-tier/app.ts"], "sourcesContent": ["// Copyright (c) Microsoft. All rights reserved. Licensed under the MIT license. See full license in the root of the repo.\n/*\n    This file provides the provides functionality to get Microsoft Graph data.\n*/\nimport * as https from \"https\";\nimport { getAccessToken } from \"./ssoauth-helper\";\nimport * as createError from \"http-errors\";\n\n/* global process */\n\nconst domain: string = \"graph.microsoft.com\";\nconst version: string = \"v1.0\";\n\nexport async function getUserData(req: any, res: any, next: any) {\n  const authorization: string = req.get(\"Authorization\");\n\n  await getAccessToken(authorization)\n    .then(async (graphTokenResponse) => {\n      if (graphTokenResponse && (graphTokenResponse.claims || graphTokenResponse.error)) {\n        res.send(graphTokenResponse);\n      } else {\n        const graphToken: string = graphTokenResponse.access_token;\n        const graphUrlSegment: string = process.env.GRAPH_URL_SEGMENT || \"/me\";\n        const graphQueryParamSegment: string = process.env.QUERY_PARAM_SEGMENT || \"\";\n\n        const graphData = await getGraphData(graphToken, graphUrlSegment, graphQueryParamSegment);\n\n        // If Microsoft Graph returns an error, such as invalid or expired token,\n        // there will be a code property in the returned object set to a HTTP status (e.g. 401).\n        // Relay it to the client. It will caught in the fail callback of `makeGraphApiCall`.\n        if (graphData.code) {\n          next(createError(graphData.code, \"Microsoft Graph error \" + JSON.stringify(graphData)));\n        } else {\n          res.send(graphData);\n        }\n      }\n    })\n    .catch((err) => {\n      res.status(401).send(err.message);\n      return;\n    });\n}\n\nexport async function getGraphData(accessToken: string, apiUrl: string, queryParams?: string): Promise<any> {\n  return new Promise<any>((resolve, reject) => {\n    const options: https.RequestOptions = {\n      host: domain,\n      path: \"/\" + version + apiUrl + queryParams,\n      method: \"GET\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Accept: \"application/json\",\n        Authorization: \"Bearer \" + accessToken,\n        \"Cache-Control\": \"private, no-cache, no-store, must-revalidate\",\n        Expires: \"-1\",\n        Pragma: \"no-cache\",\n      },\n    };\n\n    https\n      .get(options, (response) => {\n        let body = \"\";\n        response.on(\"data\", (d) => {\n          body += d;\n        });\n        response.on(\"end\", () => {\n          // The response from the OData endpoint might be an error, say a\n          // 401 if the endpoint requires an access token and it was invalid\n          // or expired. But a message is not an error in the call of https.get,\n          // so the \"on('error', reject)\" line below isn't triggered.\n          // So, the code distinguishes success (200) messages from error\n          // messages and sends a JSON object to the caller with either the\n          // requested OData or error information.\n\n          let error;\n          if (response.statusCode === 200) {\n            let parsedBody = JSON.parse(body);\n            resolve(parsedBody);\n          } else {\n            error = new Error();\n            error.code = response.statusCode;\n            error.message = response.statusMessage;\n\n            // The error body sometimes includes an empty space\n            // before the first character, remove it or it causes an error.\n            body = body.trim();\n            error.bodyCode = JSON.parse(body).error.code;\n            error.bodyMessage = JSON.parse(body).error.message;\n            resolve(error);\n          }\n        });\n      })\n      .on(\"error\", reject);\n  });\n}\n", "/*\n * Copyright (c) Microsoft. All rights reserved. Licensed under the MIT license. See full license in root of repo. -->\n *\n * This file defines the routes within the authRoute router.\n */\n\nimport fetch from \"node-fetch\";\nimport form from \"form-urlencoded\";\nimport jwt from \"jsonwebtoken\";\nimport { JwksClient } from \"jwks-rsa\";\n\n/* global process, console */\n\nconst DISCOVERY_KEYS_ENDPOINT = \"https://login.microsoftonline.com/common/discovery/v2.0/keys\";\n\nexport async function getAccessToken(authorization: string): Promise<any> {\n  if (!authorization) {\n    let error = new Error(\"No Authorization header was found.\");\n    return Promise.reject(error);\n  } else {\n    const scopeName: string = process.env.SCOPE || \"User.Read\";\n    const [, /* schema */ assertion] = authorization.split(\" \");\n\n    const tokenScopes = (jwt.decode(assertion) as jwt.JwtPayload).scp.split(\" \");\n    const accessAsUserScope = tokenScopes.find((scope) => scope === \"access_as_user\");\n    if (!accessAsUserScope) {\n      throw new Error(\"Missing access_as_user\");\n    }\n\n    const formParams = {\n      client_id: process.env.CLIENT_ID,\n      client_secret: process.env.CLIENT_SECRET,\n      grant_type: \"urn:ietf:params:oauth:grant-type:jwt-bearer\",\n      assertion: assertion,\n      requested_token_use: \"on_behalf_of\",\n      scope: [scopeName].join(\" \"),\n    };\n\n    const stsDomain: string = \"https://login.microsoftonline.com\";\n    const tenant: string = \"common\";\n    const tokenURLSegment: string = \"oauth2/v2.0/token\";\n    const encodedForm = form(formParams);\n\n    const tokenResponse = await fetch(`${stsDomain}/${tenant}/${tokenURLSegment}`, {\n      method: \"POST\",\n      body: encodedForm,\n      headers: {\n        Accept: \"application/json\",\n        \"Content-Type\": \"application/x-www-form-urlencoded\",\n      },\n    });\n    const json = await tokenResponse.json();\n    return json;\n  }\n}\n\nexport function validateJwt(req, res, next): void {\n  const authHeader = req.headers.authorization;\n  if (authHeader) {\n    const token = authHeader.split(\" \")[1];\n\n    const validationOptions = {\n      audience: process.env.CLIENT_ID,\n    };\n\n    jwt.verify(token, getSigningKeys, validationOptions, (err) => {\n      if (err) {\n        console.log(err);\n        return res.sendStatus(403);\n      }\n\n      next();\n    });\n  }\n}\n\nfunction getSigningKeys(header: any, callback: any) {\n  var client: JwksClient = new JwksClient({\n    jwksUri: DISCOVERY_KEYS_ENDPOINT,\n  });\n\n  client.getSigningKey(header.kid, function (err, key) {\n    callback(null, key.getPublicKey());\n  });\n}\n", "module.exports = require(\"cookie-parser\");", "module.exports = require(\"dotenv\");", "module.exports = require(\"express\");", "module.exports = require(\"form-urlencoded\");", "module.exports = require(\"http-errors\");", "module.exports = require(\"https\");", "module.exports = require(\"jsonwebtoken\");", "module.exports = require(\"jwks-rsa\");", "module.exports = require(\"morgan\");", "module.exports = require(\"node-fetch\");", "module.exports = require(\"office-addin-dev-certs\");", "module.exports = require(\"path\");", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "/*\n * Copyright (c) Microsoft. All rights reserved. Licensed under the MIT license. See full license in root of repo. -->\n *\n * This file is the main Node.js server file that defines the express middleware.\n */\n\nif (process.env.NODE_ENV !== \"production\") {\n  require(\"dotenv\").config();\n}\nimport * as createError from \"http-errors\";\nimport * as path from \"path\";\nimport * as cookieParser from \"cookie-parser\";\nimport * as logger from \"morgan\";\nimport express from \"express\";\nimport https from \"https\";\nimport { getHttpsServerOptions } from \"office-addin-dev-certs\";\nimport { getUserData } from \"./msgraph-helper\";\nimport { validateJwt } from \"./ssoauth-helper\";\n\n/* global console, process, require, __dirname */\n\nconst app = express();\nconst port: number | string = process.env.API_PORT || \"3000\";\n\napp.set(\"port\", port);\n\n// view engine setup\napp.set(\"views\", path.join(__dirname, \"views\"));\napp.set(\"view engine\", \"pug\");\n\napp.use(logger(\"dev\"));\napp.use(express.json());\napp.use(express.urlencoded({ extended: false }));\napp.use(cookieParser());\n\n/* Turn off caching when developing */\nif (process.env.NODE_ENV !== \"production\") {\n  app.use(express.static(path.join(process.cwd(), \"dist\"), { etag: false }));\n\n  app.use(function (req, res, next) {\n    res.header(\"Cache-Control\", \"private, no-cache, no-store, must-revalidate\");\n    res.header(\"Expires\", \"-1\");\n    res.header(\"Pragma\", \"no-cache\");\n    next();\n  });\n} else {\n  // In production mode, let static files be cached.\n  app.use(express.static(path.join(process.cwd(), \"dist\")));\n}\n\nconst indexRouter = express.Router();\nindexRouter.get(\"/\", function (req, res) {\n  res.render(\"/taskpane.html\");\n});\n\napp.use(\"/\", indexRouter);\n\n// Middle-tier API calls\n// listen for 'ping' to verify service is running\n// Un comment for development debugging, but un needed for production deployment\n// app.get(\"/ping\", function (req: any, res: any) {\n//   res.send(process.platform);\n// });\n\n//app.get(\"/getuserdata\", validateJwt, getUserData);\napp.get(\"/getuserdata\", validateJwt, getUserData);\n\n// Get the client side task pane files requested\napp.get(\"/taskpane.html\", async (req: any, res: any) => {\n  return res.sendfile(\"taskpane.html\");\n});\n\napp.get(\"/fallbackauthdialog.html\", async (req: any, res: any) => {\n  return res.sendfile(\"fallbackauthdialog.html\");\n});\n\n// Catch 404 and forward to error handler\napp.use(function (req: any, res: any, next: any) {\n  next(createError(404));\n});\n\n// error handler\napp.use(function (err: any, req: any, res: any) {\n  // set locals, only providing error in development\n  res.locals.message = err.message;\n  res.locals.error = req.app.get(\"env\") === \"development\" ? err : {};\n\n  // render the error page\n  res.status(err.status || 500);\n  res.render(\"error\");\n});\n\ngetHttpsServerOptions().then((options) => {\n  https\n    .createServer(options, app)\n    .listen(port, () => console.log(`Server running on ${port} in ${process.env.NODE_ENV} mode`));\n});\n"], "names": ["e", "t", "r", "Symbol", "n", "iterator", "o", "toStringTag", "i", "c", "prototype", "Generator", "u", "Object", "create", "_regeneratorDefine2", "f", "p", "y", "G", "v", "a", "d", "bind", "length", "l", "TypeError", "call", "done", "value", "return", "GeneratorFunction", "GeneratorFunctionPrototype", "getPrototypeOf", "setPrototypeOf", "__proto__", "displayName", "_regenerator", "w", "m", "defineProperty", "_regeneratorDefine", "_invoke", "enumerable", "configurable", "writable", "asyncGeneratorStep", "Promise", "resolve", "then", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "https", "getAccessToken", "createError", "domain", "version", "getUserData", "_x", "_x2", "_x3", "_getUserData", "_callee2", "req", "res", "next", "authorization", "_context2", "get", "_ref", "_callee", "graphTokenResponse", "graphToken", "graphUrlSegment", "graphQueryParamSegment", "graphData", "_context", "claims", "error", "send", "access_token", "process", "env", "GRAPH_URL_SEGMENT", "QUERY_PARAM_SEGMENT", "getGraphData", "code", "JSON", "stringify", "_x7", "catch", "err", "status", "message", "_x4", "_x5", "_x6", "_getGraphData", "_callee3", "accessToken", "apiUrl", "queryParams", "_context3", "reject", "options", "host", "path", "method", "headers", "Accept", "Authorization", "Expires", "Pragma", "response", "body", "on", "statusCode", "parsedBody", "parse", "Error", "statusMessage", "trim", "bodyCode", "bodyMessage", "_slicedToArray", "_arrayWithHoles", "_iterableToArrayLimit", "_unsupportedIterableToArray", "_nonIterableRest", "_arrayLikeToArray", "toString", "slice", "constructor", "name", "Array", "from", "test", "push", "isArray", "fetch", "form", "jwt", "JwksClient", "DISCOVERY_KEYS_ENDPOINT", "_getAccessToken", "scopeName", "_authorization$split", "_authorization$split2", "assertion", "tokenScopes", "accessAsUserScope", "formParams", "stsDomain", "tenant", "tokenURLSegment", "encodedForm", "tokenResponse", "json", "SCOPE", "split", "decode", "scp", "find", "scope", "client_id", "CLIENT_ID", "client_secret", "CLIENT_SECRET", "grant_type", "requested_token_use", "join", "concat", "validateJwt", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "token", "validationOptions", "audience", "verify", "getSigningKeys", "console", "log", "sendStatus", "header", "callback", "client", "jwksUri", "getSigningKey", "kid", "key", "getPublicKey", "NODE_ENV", "require", "config", "cookie<PERSON>arser", "logger", "express", "getHttpsServerOptions", "app", "port", "API_PORT", "set", "__dirname", "use", "u<PERSON><PERSON><PERSON>", "extended", "static", "cwd", "etag", "indexRouter", "Router", "render", "sendfile", "_ref2", "locals", "createServer", "listen"], "sourceRoot": ""}