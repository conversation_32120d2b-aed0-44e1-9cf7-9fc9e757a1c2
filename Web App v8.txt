// RubiRecruit v7.0.0 - SECURE WEB APP (ENHANCED VERSION)
// Enhanced duplicate detection using Google File IDs as source of truth
// Now includes AI-generated candidate-specific interview questions

// ============================================
// WEB APP ENTRY POINTS
// ============================================

function doGet(e) {
  return HtmlService.createHtmlOutput('RubiRecruit v7.0.0 - Active (Enhanced)');
}

function doPost(e) {
  try {
    const data = JSON.parse(e.postData.contents);
    const action = data.action;
    
    // Validate license
    if (!validateClientLicense(data.licenseKey)) {
      return jsonResponse({
        success: false,
        error: 'Invalid or expired license'
      });
    }
    
    Logger.log(`Received action: ${action}`);
    Logger.log(`Full data: ${JSON.stringify(data)}`);
    
    // Route to appropriate function
    switch(action) {
      case 'test':
        return jsonResponse({
          success: true,
          message: 'Connection successful',
          version: '7.0.0-enhanced',
          timestamp: new Date().toISOString()
        });
        
      case 'setupSheets':
        return handleSetupSheets(data);
        
      case 'loadPositionDescription':
        return handleLoadPositionDescription(data);
        
      case 'generateDynamicRubric':
        return handleGenerateDynamicRubric(data);
        
      case 'lockRubric':
        return handleLockRubric(data);
        
      case 'unlockRubric':
        return handleUnlockRubric(data);
        
      case 'updateRubricFromSheet':
        return handleUpdateRubricFromSheet(data);
        
      case 'processAllCandidates':
        return handleProcessAllCandidates(data);
        
      case 'processBatch':
        return handleProcessBatch(data);
        
      case 'processBatchSize':
        return handleProcessBatchSize(data);
        
      case 'processSpecificCandidates':
        return handleProcessSpecificCandidates(data);
        
      case 'retryFailedCandidates':
        return handleRetryFailedCandidates(data);
        
      case 'testSingleCandidate':
        return handleTestSingleCandidate(data);
        
      case 'testDocumentMatching':
        return handleTestDocumentMatching(data);
        
      case 'validateApiKey':
        return handleValidateApiKey(data);
        
      case 'checkPermissions':
        return handleCheckPermissions(data);
        
      case 'getProcessingStatus':
        return handleGetProcessingStatus(data);
        
      case 'getUnprocessedCandidates':
        return handleGetUnprocessedCandidates(data);
        
      case 'getFailedCandidates':
        return handleGetFailedCandidates(data);
        
      case 'clearAllData':
        return handleClearAllData(data);
        
      case 'checkForDuplicates':
        return handleCheckForDuplicates(data);
        
      default:
        throw new Error(`Unknown action: ${action}`);
    }
    
  } catch (error) {
    Logger.log(`Error in doPost: ${error.toString()}`);
    return jsonResponse({
      success: false,
      error: error.toString(),
      stack: error.stack
    });
  }
}

// ============================================
// LICENSE VALIDATION
// ============================================

const LICENSE_SHEET_ID = '1-lUGLPt_Zn5tjchsru7xE2NyENYoQ-Buq17k_RdsZfw';

function validateClientLicense(licenseKey) {
  if (!licenseKey) return false;
  
  try {
    const licenseSheet = SpreadsheetApp.openById(LICENSE_SHEET_ID);
    const data = licenseSheet.getDataRange().getValues();
    
    const headers = data[0];
    const keyCol = headers.findIndex(h => h.toString().toLowerCase().includes('license'));
    const expiryCol = headers.findIndex(h => h.toString().toLowerCase().includes('expir'));
    const statusCol = headers.findIndex(h => h.toString().toLowerCase().includes('status'));
    
    for (let i = 1; i < data.length; i++) {
      if (data[i][keyCol] === licenseKey) {
        const expiryDate = new Date(data[i][expiryCol]);
        const status = data[i][statusCol];
        const now = new Date();
        
        return status === 'Active' && expiryDate > now;
      }
    }
    
    return false;
    
  } catch (error) {
    Logger.log('License validation error: ' + error);
    return true; // Allow for development
  }
}

// ============================================
// RESPONSE HELPER
// ============================================

function jsonResponse(data) {
  return ContentService
    .createTextOutput(JSON.stringify(data))
    .setMimeType(ContentService.MimeType.JSON);
}

// ============================================
// ENHANCED FILE ID TRACKING SYSTEM
// ============================================

/**
 * Initialize or get the processed files tracking sheet
 * This sheet stores file IDs to prevent duplicate processing
 */
function getProcessedFilesSheet(ss) {
  let sheet = ss.getSheetByName('_Processed_Files');
  
  if (!sheet) {
    sheet = ss.insertSheet('_Processed_Files');
    sheet.getRange(1, 1, 1, 7).setValues([
      ['CV File ID', 'Cover File ID', 'Candidate Name', 'Processed Date', 'Status', 'Score', 'File Names']
    ]);
    sheet.getRange(1, 1, 1, 7)
      .setFontWeight('bold')
      .setBackground('#E3F2FD');
    sheet.hideSheet();
  }
  
  return sheet;
}

/**
 * Check if a candidate has already been processed using file IDs
 * This is the PRIMARY duplicate detection mechanism
 */
function isFileAlreadyProcessed(cvFileId, coverFileId, ss) {
  const sheet = getProcessedFilesSheet(ss);
  if (sheet.getLastRow() <= 1) return false;
  
  const data = sheet.getRange(2, 1, sheet.getLastRow() - 1, 2).getValues();
  
  for (let i = 0; i < data.length; i++) {
    // Check if CV file ID matches (primary check)
    if (data[i][0] && data[i][0] === cvFileId) {
      Logger.log(`Duplicate detected by CV file ID: ${cvFileId}`);
      return true;
    }
    // Also check cover letter if provided
    if (coverFileId && data[i][1] && data[i][1] === coverFileId) {
      Logger.log(`Duplicate detected by cover letter file ID: ${coverFileId}`);
      return true;
    }
  }
  
  return false;
}

/**
 * Record processed file IDs to prevent future duplicates
 */
function recordProcessedFile(cvFileId, coverFileId, candidateName, score, cvFileName, coverFileName, ss) {
  const sheet = getProcessedFilesSheet(ss);
  
  const fileNames = cvFileName + (coverFileName ? ' | ' + coverFileName : '');
  
  sheet.appendRow([
    cvFileId,
    coverFileId || '',
    candidateName,
    new Date(),
    'Processed',
    score || 0,
    fileNames
  ]);
  
  Logger.log(`Recorded processed file: ${candidateName} (CV ID: ${cvFileId})`);
}

/**
 * Enhanced duplicate detection with file ID as primary source of truth
 * Falls back to name matching as secondary check
 */
function isDuplicateCandidate(candidateInfo, ss) {
  try {
    // First, try to get file IDs if we have file names
    if (candidateInfo.cvFileName && candidateInfo.folderId) {
      try {
        const folder = DriveApp.getFolderById(candidateInfo.folderId);
        const files = folder.getFilesByName(candidateInfo.cvFileName);
        
        if (files.hasNext()) {
          const cvFile = files.next();
          const cvFileId = cvFile.getId();
          
          // Check if this file ID has been processed
          if (isFileAlreadyProcessed(cvFileId, null, ss)) {
            Logger.log(`Duplicate detected for ${candidateInfo.name} using CV file ID: ${cvFileId}`);
            return true;
          }
        }
      } catch (e) {
        Logger.log(`Could not check file ID for ${candidateInfo.name}: ${e}`);
      }
    }
    
    // Secondary check: Look for exact name matches in evaluations
    // This catches cases where file might have been renamed
    const evalSheet = ss.getSheetByName('Candidate Evaluations');
    if (evalSheet && evalSheet.getLastRow() > 1) {
      const data = evalSheet.getRange(2, 2, evalSheet.getLastRow() - 1, 1).getValues();
      const normalizedName = normalizeNameForComparison(candidateInfo.name);
      
      for (let i = 0; i < data.length; i++) {
        if (data[i][0]) {
          const existingName = normalizeNameForComparison(data[i][0].toString());
          if (existingName === normalizedName) {
            Logger.log(`Duplicate found by normalized name match: ${candidateInfo.name}`);
            return true;
          }
        }
      }
    }
    
    return false;
    
  } catch (error) {
    Logger.log(`Error in duplicate check for ${candidateInfo.name}: ${error}`);
    return false;
  }
}

/**
 * Normalize names for more reliable comparison
 * Handles common variations and parsing issues
 */
function normalizeNameForComparison(name) {
  return name
    .toLowerCase()
    .trim()
    .replace(/\s+/g, ' ')  // Multiple spaces to single
    .replace(/[^a-z0-9\s]/g, '')  // Remove special characters
    .split(' ')
    .sort()  // Sort words to handle reordering
    .join(' ');
}

/**
 * Enhanced check for duplicates with file ID verification
 */
function handleCheckForDuplicates(data) {
  try {
    const spreadsheetId = data.spreadsheetId;
    const candidateNames = data.candidateNames || [];
    const cvFolderId = data.cvFolderId;
    
    const ss = SpreadsheetApp.openById(spreadsheetId);
    const duplicates = [];
    const newCandidates = [];
    
    // Get document mapping to find file names
    const mappingSheet = ss.getSheetByName('Document Mapping');
    const mappingData = {};
    
    if (mappingSheet && mappingSheet.getLastRow() > 1) {
      const data = mappingSheet.getRange(2, 1, mappingSheet.getLastRow() - 1, 3).getValues();
      data.forEach(row => {
        if (row[0]) {
          mappingData[row[0].toLowerCase()] = {
            cvFileName: row[1],
            coverFileName: row[2]
          };
        }
      });
    }
    
    candidateNames.forEach(name => {
      const candidateInfo = { 
        name: name,
        cvFileName: null,
        coverLetterFileName: null,
        folderId: cvFolderId
      };
      
      // Try to get file names from mapping
      const mappingKey = name.toLowerCase();
      if (mappingData[mappingKey]) {
        candidateInfo.cvFileName = mappingData[mappingKey].cvFileName;
        candidateInfo.coverLetterFileName = mappingData[mappingKey].coverFileName;
      }
      
      if (isDuplicateCandidate(candidateInfo, ss)) {
        duplicates.push(name);
      } else {
        newCandidates.push(name);
      }
    });
    
    return jsonResponse({
      success: true,
      duplicates: duplicates,
      newCandidates: newCandidates,
      hasDuplicates: duplicates.length > 0
    });
    
  } catch (error) {
    Logger.log(`Check duplicates error: ${error.toString()}`);
    return jsonResponse({
      success: false,
      error: error.toString()
    });
  }
}

// ============================================
// MATHEMATICAL SCORING FUNCTIONS
// ============================================

function calculateCategoryScore(attributes, categoryWeight, frameworkAttributes) {
  let weightedSum = 0;
  let totalWeight = 0;
  
  attributes.forEach((attr, index) => {
    const frameworkAttr = frameworkAttributes[index];
    const weightInCategory = frameworkAttr.weight / categoryWeight;
    
    const scorePercentage = (attr.score / 5) * 100;
    
    weightedSum += scorePercentage * weightInCategory;
    totalWeight += weightInCategory;
  });
  
  if (Math.abs(totalWeight - 1.0) > 0.01) {
    Logger.log(`Warning: Category weights sum to ${totalWeight}, normalizing`);
    return weightedSum / totalWeight;
  }
  
  return weightedSum;
}

function calculateTotalScore(categories, evaluationFramework) {
  let totalScore = 0;
  
  Object.entries(categories).forEach(([categoryName, categoryData]) => {
    const categoryWeight = evaluationFramework[categoryName].weight;
    const contribution = (categoryData.category_score * categoryWeight) / 100;
    categoryData.weighted_contribution = contribution;
    totalScore += contribution;
  });
  
  return totalScore;
}

function validateRubricConsistency(evaluationFramework, evaluation, candidateName) {
  const errors = [];
  
  const frameworkCategories = Object.keys(evaluationFramework);
  const evaluationCategories = Object.keys(evaluation.categories || {});
  
  if (frameworkCategories.length !== 8) {
    errors.push(`Framework has ${frameworkCategories.length} categories, should have 8`);
  }
  
  if (evaluationCategories.length !== frameworkCategories.length) {
    errors.push(`Evaluation has ${evaluationCategories.length} categories, framework has ${frameworkCategories.length}`);
  }
  
  frameworkCategories.forEach(categoryName => {
    const framework = evaluationFramework[categoryName];
    const evalCategory = evaluation.categories ? evaluation.categories[categoryName] : null;
    
    if (!evalCategory) {
      errors.push(`Missing category: ${categoryName}`);
      return;
    }
    
    if (!evalCategory.attributes || evalCategory.attributes.length !== 5) {
      errors.push(`${categoryName} has ${evalCategory.attributes ? evalCategory.attributes.length : 0} attributes, should have 5`);
    }
    
    if (evalCategory.attributes) {
      evalCategory.attributes.forEach((attr, index) => {
        if (attr.score < 0 || attr.score > 5) {
          errors.push(`${categoryName} attribute ${index + 1} has invalid score: ${attr.score}`);
        }
      });
    }
  });
  
  if (errors.length > 0) {
    throw new Error(`Validation failed for ${candidateName}:\n${errors.join('\n')}`);
  }
  
  return true;
}

function postProcessEvaluation(evaluation, evaluationFramework, candidateName) {
  try {
    // Ensure evaluation has the required structure
    if (!evaluation.categories) {
      evaluation.categories = {};
    }
    
    // Validate structure first
    validateRubricConsistency(evaluationFramework, evaluation, candidateName);
    
    // Calculate category scores from attributes
    Object.entries(evaluation.categories).forEach(([categoryName, categoryData]) => {
      const framework = evaluationFramework[categoryName];
      
      if (!framework) {
        throw new Error(`No framework found for category: ${categoryName}`);
      }
      
      const calculatedScore = calculateCategoryScore(
        categoryData.attributes,
        framework.weight,
        framework.attributes
      );
      
      if (categoryData.category_score && Math.abs(categoryData.category_score - calculatedScore) > 5) {
        Logger.log(`Score discrepancy for ${candidateName} - ${categoryName}: AI=${categoryData.category_score}, Calculated=${calculatedScore}`);
      }
      
      categoryData.category_score = calculatedScore;
      categoryData.weight = framework.weight;
      categoryData.scoring_method = 'calculated';
      
      categoryData.attributes.forEach((attr, index) => {
        attr.weight = framework.attributes[index].weight;
        attr.weight_in_category = framework.attributes[index].weight / framework.weight;
      });
    });
    
    // Calculate total score
    const totalScore = calculateTotalScore(evaluation.categories, evaluationFramework);
    evaluation.total_score = totalScore;
    evaluation.recommendation = getRecommendation(totalScore);
    evaluation.scoring_version = 'v7.0.0-enhanced';
    
    return evaluation;
    
  } catch (error) {
    Logger.log(`Post-processing error for ${candidateName}: ${error.toString()}`);
    throw error;
  }
}

// ============================================
// INTERVIEW QUESTIONS GENERATION (NEW)
// ============================================

function generateInterviewQuestions(candidateName, cvText, coverText, evaluation, metrics, positionContext, apiKey, ss) {
  const apiUrl = 'https://api.anthropic.com/v1/messages';
  
  const prompt = `You are an expert interviewer. Based on this candidate's CV, cover letter, and evaluation, generate 6 specific, insightful interview questions.

CANDIDATE: ${candidateName}
POSITION: ${positionContext.title || 'Unknown Position'}

EVALUATION SUMMARY:
- Total Score: ${evaluation.total_score ? evaluation.total_score.toFixed(1) : '0'}
- Top Strengths: ${evaluation.strengths ? evaluation.strengths.join(', ') : 'N/A'}
- Development Areas: ${evaluation.development_areas ? evaluation.development_areas.join(', ') : 'N/A'}

KEY METRICS:
- Total Experience: ${metrics.total_career_experience_years || 0} years
- Managerial Experience: ${metrics.years_managerial_experience || 0} years
- Current Company: ${metrics.current_company || 'Unknown'}
- Industries: ${metrics.industries_worked ? metrics.industries_worked.join(', ') : 'N/A'}
- Education: ${metrics.degrees ? metrics.degrees.join(', ') : 'N/A'}
- Notable Companies: ${metrics.notable_companies ? metrics.notable_companies.join(', ') : 'N/A'}

WEAK AREAS FROM EVALUATION:
${Object.entries(evaluation.categories || {})
  .filter(([_, data]) => data.category_score < 60)
  .map(([name, data]) => `- ${name}: ${data.category_score ? data.category_score.toFixed(1) : 0}%`)
  .join('\n') || 'None identified'}

CV TEXT (excerpt):
${cvText.substring(0, 3000)}

COVER LETTER (excerpt):
${coverText ? coverText.substring(0, 2000) : 'No cover letter provided'}

Create 6 interview questions that:
1. Probe specific gaps or inconsistencies in their experience
2. Explore transitions between roles/companies if there are concerns
3. Dig deeper into claims that seem vague or unsubstantiated
4. Test technical/functional expertise in areas crucial to the role
5. Uncover motivations and cultural fit
6. Challenge them on their weakest evaluation areas

Make questions SPECIFIC to THIS candidate - reference their actual companies, projects, or claims.
Avoid generic questions. Each question should reveal something important about their fit for this role.

Return JSON with exactly 6 questions:
{
  "questions": [
    "Specific question 1 that references something from their CV/cover letter",
    "Specific question 2 that probes a gap or concern",
    "Specific question 3 about their experience at [specific company]",
    "Specific question 4 testing expertise in [specific area]",
    "Specific question 5 about motivation or transition",
    "Specific question 6 challenging their weakest area"
  ]
}`;

  const options = {
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
      'x-api-key': apiKey,
      'anthropic-version': '2023-06-01'
    },
    payload: JSON.stringify({
      model: 'claude-3-5-sonnet-20241022',
      max_tokens: 1500,
      temperature: 0.7,
      messages: [{role: 'user', content: prompt}]
    }),
    muteHttpExceptions: true
  };
  
  try {
    const response = UrlFetchApp.fetch(apiUrl, options);
    const responseCode = response.getResponseCode();
    
    if (responseCode !== 200) {
      throw new Error(`API returned status ${responseCode}`);
    }
    
    const responseText = response.getContentText();
    const data = JSON.parse(responseText);
    
    if (!data || !data.content || !Array.isArray(data.content) || data.content.length === 0) {
      throw new Error('Invalid API response structure');
    }
    
    let questionsText = data.content[0].text;
    
    if (ss) {
      let inputTokens = data.usage?.input_tokens || estimateTokens(prompt);
      let outputTokens = data.usage?.output_tokens || estimateTokens(questionsText);
      trackAPIUsage(candidateName, 'Interview Questions Generation', inputTokens, outputTokens, ss);
    }
    
    questionsText = questionsText.replace(/```json\n?/gi, '').replace(/```\n?/gi, '').trim();
    const jsonMatch = questionsText.match(/\{[\s\S]*\}/);
    
    if (!jsonMatch) {
      throw new Error('No valid JSON found in response');
    }
    
    const questionsData = JSON.parse(jsonMatch[0]);
    
    // Ensure we have exactly 6 questions
    if (questionsData.questions && Array.isArray(questionsData.questions)) {
      // Trim to 6 if more, or pad with defaults if less
      while (questionsData.questions.length < 6) {
        questionsData.questions.push(`Tell me more about your experience in ${positionContext.industry || 'this field'}.`);
      }
      questionsData.questions = questionsData.questions.slice(0, 6);
      
      return questionsData.questions;
    }
    
    throw new Error('Invalid questions format in response');
    
  } catch (error) {
    Logger.log(`Interview questions generation error for ${candidateName}: ${error}`);
    
    // Return default questions on error
    return [
      `Walk me through your experience at ${metrics.current_company || 'your current company'}.`,
      `What specific achievements from your CV are you most proud of and why?`,
      `How do you see your experience translating to this ${positionContext.title || 'position'}?`,
      `Describe a challenging situation you faced and how you resolved it.`,
      `What interests you most about this opportunity?`,
      `Where do you see areas for your own professional development?`
    ];
  }
}

// ============================================
// RUBRIC EDITING FUNCTIONS
// ============================================

function handleUnlockRubric(data) {
  try {
    const spreadsheetId = data.spreadsheetId;
    const ss = SpreadsheetApp.openById(spreadsheetId);
    
    const configSheet = ss.getSheetByName('Position Configuration');
    if (configSheet) {
      const lastRow = configSheet.getLastRow();
      for (let i = 2; i <= lastRow; i++) {
        if (configSheet.getRange(i, 1).getValue() === 'Rubric Status') {
          configSheet.getRange(i, 2).setValue('Generated - Not Locked');
          configSheet.getRange(i, 3).setValue('Editing');
          configSheet.getRange(i, 3).setBackground('#FFF9C4');
          break;
        }
      }
    }
    
    const rubricSheet = ss.getSheetByName('Dynamic Rubric Configuration');
    if (rubricSheet) {
      const protections = rubricSheet.getProtections(SpreadsheetApp.ProtectionType.SHEET);
      protections.forEach(protection => protection.remove());
    }
    
    logMessage('Rubric unlocked for editing', 'Info', ss);
    
    return jsonResponse({
      success: true,
      message: 'Rubric unlocked successfully. You can now edit it.'
    });
    
  } catch (error) {
    Logger.log(`Unlock rubric error: ${error.toString()}`);
    return jsonResponse({
      success: false,
      error: error.toString()
    });
  }
}

function handleUpdateRubricFromSheet(data) {
  try {
    const spreadsheetId = data.spreadsheetId;
    const ss = SpreadsheetApp.openById(spreadsheetId);
    
    const rubricSheet = ss.getSheetByName('Dynamic Rubric Configuration');
    if (!rubricSheet || rubricSheet.getLastRow() <= 1) {
      throw new Error('No rubric found in Dynamic Rubric Configuration sheet');
    }
    
    const rubric = parseRubricFromSheet(rubricSheet);
    
    const validation = validateRubricStructure(rubric);
    if (!validation.valid) {
      throw new Error(validation.error);
    }
    
    storeEvaluationFramework(rubric, ss);
    
    const configSheet = ss.getSheetByName('Position Configuration');
    if (configSheet) {
      const lastRow = configSheet.getLastRow();
      for (let i = 2; i <= lastRow; i++) {
        if (configSheet.getRange(i, 1).getValue() === 'Rubric Status') {
          configSheet.getRange(i, 2).setValue('Generated - Not Locked');
          configSheet.getRange(i, 3).setValue('Updated');
          configSheet.getRange(i, 3).setBackground('#C8E6C9');
          break;
        }
      }
      
      configSheet.appendRow(['Last Updated', new Date().toISOString(), 'Modified']);
    }
    
    logMessage('Rubric updated from manual edits', 'Success', ss);
    
    return jsonResponse({
      success: true,
      message: 'Rubric updated successfully',
      validation: validation
    });
    
  } catch (error) {
    Logger.log(`Update rubric error: ${error.toString()}`);
    return jsonResponse({
      success: false,
      error: error.toString()
    });
  }
}

function parseRubricFromSheet(sheet) {
  const data = sheet.getDataRange().getValues();
  const rubric = {};
  
  let currentCategory = null;
  let currentCategoryWeight = 0;
  
  for (let i = 1; i < data.length; i++) {
    const row = data[i];
    
    if (row[0] && row[0].toString().trim() !== '') {
      currentCategory = row[0].toString().trim();
      currentCategoryWeight = parseFloat(row[1]) || 0;
      
      rubric[currentCategory] = {
        weight: currentCategoryWeight,
        importance: currentCategoryWeight >= 15 ? 'critical' : 
                    currentCategoryWeight >= 10 ? 'important' : 'supporting',
        attributes: []
      };
    }
    
    if (currentCategory && row[2]) {
      const attribute = {
        name: row[2].toString().trim(),
        weight: parseFloat(row[3]) || 0,
        weight_in_category: 0,
        description: row[4] || '',
        rubric: {
          '0': row[5] || 'No evidence',
          '1': row[6] || 'Minimal evidence',
          '2': row[7] || 'Some evidence',
          '3': row[8] || 'Adequate evidence',
          '4': row[9] || 'Strong evidence',
          '5': row[10] || 'Exceptional evidence'
        }
      };
      
      attribute.weight_in_category = attribute.weight / currentCategoryWeight;
      
      rubric[currentCategory].attributes.push(attribute);
    }
  }
  
  return rubric;
}

function validateRubricStructure(rubric) {
  const categories = Object.keys(rubric);
  
  if (categories.length !== 8) {
    return {
      valid: false,
      error: `Rubric must have exactly 8 categories, found ${categories.length}`
    };
  }
  
  let totalWeight = 0;
  categories.forEach(cat => {
    totalWeight += rubric[cat].weight;
  });
  
  if (Math.abs(totalWeight - 100) > 0.5) {
    return {
      valid: false,
      error: `Category weights must sum to 100%, currently ${totalWeight.toFixed(1)}%`
    };
  }
  
  for (const cat of categories) {
    const category = rubric[cat];
    
    if (!category.attributes || category.attributes.length !== 5) {
      return {
        valid: false,
        error: `Category "${cat}" must have exactly 5 attributes, found ${category.attributes ? category.attributes.length : 0}`
      };
    }
    
    const attrWeightSum = category.attributes.reduce((sum, attr) => sum + (attr.weight || 0), 0);
    if (Math.abs(attrWeightSum - category.weight) > 0.5) {
      return {
        valid: false,
        error: `Attributes in "${cat}" must sum to ${category.weight}%, currently ${attrWeightSum.toFixed(1)}%`
      };
    }
    
    if (category.weight < 5 || category.weight > 20) {
      return {
        valid: false,
        error: `Category "${cat}" weight must be between 5% and 20%, currently ${category.weight}%`
      };
    }
  }
  
  return {
    valid: true,
    totalWeight: totalWeight,
    categoryCount: categories.length
  };
}

// ============================================
// POSITION DESCRIPTION HANDLERS
// ============================================

function handleLoadPositionDescription(data) {
  try {
    const fileId = data.positionFileId;
    const spreadsheetId = data.spreadsheetId;
    
    const ss = SpreadsheetApp.openById(spreadsheetId);
    
    const file = DriveApp.getFileById(fileId);
    const positionText = extractTextFromPDF(file);
    
    let sheet = ss.getSheetByName('Position Configuration');
    if (!sheet) {
      sheet = ss.insertSheet('Position Configuration');
      sheet.getRange(1, 1, 1, 3).setValues([['Field', 'Value', 'Status']]);
      sheet.getRange(1, 1, 1, 3).setFontWeight('bold').setBackground('#E3F2FD');
    }
    
    const rows = [
      ['Position Title', file.getName().replace(/\.[^/.]+$/, ''), 'Loaded'],
      ['File ID', fileId, 'Active'],
      ['Load Date', new Date().toISOString(), 'Current'],
      ['Text Length', positionText.length + ' characters', 'Info'],
      ['Rubric Status', 'Not Generated', 'Pending']
    ];
    
    sheet.getRange(2, 1, rows.length, 3).setValues(rows);
    
    let textSheet = ss.getSheetByName('Position Description Text');
    if (!textSheet) {
      textSheet = ss.insertSheet('Position Description Text');
    }
    textSheet.clear();
    textSheet.getRange(1, 1).setValue('POSITION DESCRIPTION FULL TEXT');
    textSheet.getRange(2, 1).setValue(positionText);
    
    logMessage(`Position description loaded: ${file.getName()}`, 'Success', ss);
    
    return jsonResponse({
      success: true,
      message: 'Position description loaded successfully',
      positionTitle: file.getName(),
      textLength: positionText.length
    });
    
  } catch (error) {
    Logger.log(`Load position error: ${error.toString()}`);
    return jsonResponse({
      success: false,
      error: error.toString()
    });
  }
}

function handleGenerateDynamicRubric(data) {
  try {
    const spreadsheetId = data.spreadsheetId;
    const claudeApiKey = data.claudeApiKey;
    
    const ss = SpreadsheetApp.openById(spreadsheetId);
    
    const textSheet = ss.getSheetByName('Position Description Text');
    if (!textSheet || textSheet.getLastRow() < 2) {
      throw new Error('No position description loaded. Please load a position description first.');
    }
    
    const positionText = textSheet.getRange(2, 1).getValue();
    const configSheet = ss.getSheetByName('Position Configuration');
    const positionTitle = configSheet ? configSheet.getRange(2, 2).getValue() : 'Unknown Position';
    
    logMessage('Generating dynamic rubric with AI...', 'Info', ss);
    
    const rubric = generateRubricWithAI(positionText, positionTitle, claudeApiKey, ss);
    
    storeDynamicRubric(rubric, ss);
    
    if (configSheet) {
      const lastRow = configSheet.getLastRow();
      for (let i = 2; i <= lastRow; i++) {
        if (configSheet.getRange(i, 1).getValue() === 'Rubric Status') {
          configSheet.getRange(i, 2).setValue('Generated - Not Locked');
          configSheet.getRange(i, 3).setValue('Ready');
          break;
        }
      }
    }
    
    logMessage('Dynamic rubric generated successfully', 'Success', ss);
    
    return jsonResponse({
      success: true,
      message: 'Dynamic rubric generated successfully',
      categories: Object.keys(rubric),
      positionTitle: positionTitle
    });
    
  } catch (error) {
    Logger.log(`Generate rubric error: ${error.toString()}`);
    return jsonResponse({
      success: false,
      error: error.toString()
    });
  }
}

function handleLockRubric(data) {
  try {
    const spreadsheetId = data.spreadsheetId;
    const ss = SpreadsheetApp.openById(spreadsheetId);
    
    const rubricSheet = ss.getSheetByName('Dynamic Rubric Configuration');
    if (!rubricSheet || rubricSheet.getLastRow() <= 1) {
      throw new Error('No rubric generated. Please generate a rubric first.');
    }
    
    const rubric = parseRubricFromSheet(rubricSheet);
    const validation = validateRubricStructure(rubric);
    if (!validation.valid) {
      throw new Error(`Rubric validation failed: ${validation.error}`);
    }
    
    storeEvaluationFramework(rubric, ss);
    
    const configSheet = ss.getSheetByName('Position Configuration');
    if (configSheet) {
      const lastRow = configSheet.getLastRow();
      for (let i = 2; i <= lastRow; i++) {
        if (configSheet.getRange(i, 1).getValue() === 'Rubric Status') {
          configSheet.getRange(i, 2).setValue('LOCKED');
          configSheet.getRange(i, 3).setValue('Active');
          configSheet.getRange(i, 3).setBackground('#C8E6C9');
          break;
        }
      }
      
      configSheet.appendRow(['Locked At', new Date().toISOString(), 'Locked']);
    }
    
    const protection = rubricSheet.protect();
    protection.setDescription('Rubric locked for evaluation');
    protection.setWarningOnly(true);
    
    logMessage('Rubric locked and ready for evaluation', 'Success', ss);
    
    return jsonResponse({
      success: true,
      message: 'Rubric locked successfully. Ready to evaluate candidates.'
    });
    
  } catch (error) {
    Logger.log(`Lock rubric error: ${error.toString()}`);
    return jsonResponse({
      success: false,
      error: error.toString()
    });
  }
}

// ============================================
// DYNAMIC RUBRIC GENERATION WITH AI
// ============================================

function generateRubricWithAI(positionText, positionTitle, apiKey, ss) {
  const apiUrl = 'https://api.anthropic.com/v1/messages';
  
  const prompt = `You are a world-leading expert HR consultant creating a comprehensive evaluation rubric for candidate assessment based on a position description.

POSITION: ${positionTitle}

POSITION DESCRIPTION:
${positionText.substring(0, 10000)}

Create a detailed candidate evaluation rubric with EXACTLY 8 categories that are most critical for success in this specific role.

CRITICAL REQUIREMENTS:
1. EXACTLY 8 categories - each must be highly relevant to THIS position
2. Each category should have a weight between 5% and 20% based on its importance to success in this role
3. Categories MUST sum to exactly 100%
4. More critical categories should have higher weights (up to 20%)
5. Less critical categories should have lower weights (minimum 5%)
6. EXACTLY 5 attributes per category
7. Attribute weights within each category should vary based on importance (not all equal)
8. Attributes within a category must sum to the category's total weight

WEIGHTING GUIDELINES:
- Technical/core competencies critical to the role: 15-20%
- Essential skills that directly impact performance: 12-18%
- Important supporting capabilities: 10-15%
- Valuable but less critical areas: 5-10%

The categories and attributes MUST be:
- Specific to the position requirements
- Observable/inferable from CV and cover letter
- Measurable with concrete evidence
- Relevant to predicting success in this role

Each attribute needs:
- Clear name and description
- Weight reflecting its importance within the category
- Detailed 0-5 scoring rubric with specific, observable criteria

Return ONLY valid JSON in this EXACT format:
{
  "Category Name 1": {
    "weight": 18.0,
    "importance": "critical",
    "attributes": [
      {
        "name": "Specific Attribute Name",
        "weight": 4.5,
        "description": "Clear description of what this measures",
        "rubric": {
          "0": "No evidence or completely lacking",
          "1": "Minimal evidence, significant gaps",
          "2": "Some evidence, below expectations",
          "3": "Adequate evidence, meets basic requirements",
          "4": "Strong evidence, exceeds expectations",
          "5": "Exceptional evidence, outstanding capability"
        }
      }
    ]
  }
}

Base the weights on what matters MOST for success in this specific position. Critical technical skills and core competencies should be weighted highest.`;

  const options = {
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
      'x-api-key': apiKey,
      'anthropic-version': '2023-06-01'
    },
    payload: JSON.stringify({
      model: 'claude-3-5-sonnet-20241022',
      max_tokens: 8000,
      temperature: 0.3,
      messages: [{role: 'user', content: prompt}]
    }),
    muteHttpExceptions: true
  };
  
  try {
    const response = UrlFetchApp.fetch(apiUrl, options);
    const data = JSON.parse(response.getContentText());
    
    // Check if the response has the expected structure
    if (!data.content || !Array.isArray(data.content) || data.content.length === 0) {
      throw new Error('Invalid API response structure');
    }
    
    let responseText = data.content[0].text;
    
    if (ss) {
      const inputTokens = data.usage?.input_tokens || estimateTokens(prompt);
      const outputTokens = data.usage?.output_tokens || estimateTokens(responseText);
      trackAPIUsage('RUBRIC GENERATION', 'Generate Dynamic Rubric', inputTokens, outputTokens, ss);
    }
    
    responseText = responseText.replace(/```json\n?/gi, '').replace(/```\n?/gi, '').trim();
    const jsonMatch = responseText.match(/\{[\s\S]*\}/);
    
    if (!jsonMatch) {
      throw new Error('No valid JSON found in AI response');
    }
    
    const rubric = JSON.parse(jsonMatch[0]);
    
    const categories = Object.keys(rubric);
    if (categories.length !== 8) {
      throw new Error(`Rubric must have exactly 8 categories, got ${categories.length}`);
    }
    
    let totalWeight = 0;
    categories.forEach(cat => {
      totalWeight += rubric[cat].weight;
    });
    
    if (Math.abs(totalWeight - 100) > 0.1) {
      categories.forEach(cat => {
        rubric[cat].weight = (rubric[cat].weight / totalWeight) * 100;
      });
    }
    
    // Calculate weight_in_category for each attribute
    categories.forEach(cat => {
      if (!rubric[cat].attributes || rubric[cat].attributes.length !== 5) {
        throw new Error(`Category "${cat}" must have exactly 5 attributes`);
      }
      
      const catWeight = rubric[cat].weight;
      const totalAttrWeight = rubric[cat].attributes.reduce((sum, attr) => sum + (attr.weight || 1), 0);
      
      rubric[cat].attributes.forEach(attr => {
        attr.weight = (attr.weight || 1) * catWeight / totalAttrWeight;
        attr.weight_in_category = attr.weight / catWeight;
      });
    });
    
    return rubric;
    
  } catch (error) {
    Logger.log(`Rubric generation error: ${error}`);
    throw new Error(`Failed to generate rubric: ${error.toString()}`);
  }
}

function storeDynamicRubric(rubric, ss) {
  let sheet = ss.getSheetByName('Dynamic Rubric Configuration');
  
  if (!sheet) {
    sheet = ss.insertSheet('Dynamic Rubric Configuration');
  }
  
  sheet.clear();
  
  const headers = [
    'Category',
    'Category Weight (%)',
    'Attribute',
    'Attribute Weight (%)',
    'Description',
    'Score 0',
    'Score 1',
    'Score 2',
    'Score 3',
    'Score 4',
    'Score 5'
  ];
  
  sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
  sheet.getRange(1, 1, 1, headers.length)
    .setFontWeight('bold')
    .setBackground('#1E88E5')
    .setFontColor('#FFFFFF');
  
  const rows = [];
  Object.entries(rubric).forEach(([categoryName, category]) => {
    const categoryWeight = category.weight;
    
    category.attributes.forEach((attr, index) => {
      rows.push([
        index === 0 ? categoryName : '',
        index === 0 ? categoryWeight.toFixed(1) : '',
        attr.name,
        attr.weight.toFixed(2),
        attr.description,
        attr.rubric['0'] || '',
        attr.rubric['1'] || '',
        attr.rubric['2'] || '',
        attr.rubric['3'] || '',
        attr.rubric['4'] || '',
        attr.rubric['5'] || ''
      ]);
    });
  });
  
  if (rows.length > 0) {
    sheet.getRange(2, 1, rows.length, headers.length).setValues(rows);
    
    for (let i = 2; i <= rows.length + 1; i++) {
      if (sheet.getRange(i, 1).getValue()) {
        sheet.getRange(i, 1, 1, headers.length).setBackground('#E8F5E9');
      }
    }
  }
  
  sheet.setColumnWidth(1, 180);
  sheet.setColumnWidth(2, 120);
  sheet.setColumnWidth(3, 200);
  sheet.setColumnWidth(4, 100);
  sheet.setColumnWidth(5, 450);
  for (let i = 6; i <= 11; i++) {
    sheet.setColumnWidth(i, 250);
  }
  
  sheet.setFrozenRows(1);
  sheet.getRange(1, 1, sheet.getLastRow(), sheet.getLastColumn()).setWrap(true);
  
  storeEvaluationFramework(rubric, ss);
}

function storeEvaluationFramework(rubric, ss) {
  let sheet = ss.getSheetByName('Evaluation Framework');
  
  if (!sheet) {
    sheet = ss.insertSheet('Evaluation Framework');
  }
  
  sheet.clear();
  
  sheet.getRange(1, 1).setValue('EVALUATION FRAMEWORK JSON');
  sheet.getRange(2, 1).setValue(JSON.stringify(rubric));
  
  sheet.hideSheet();
}

// ============================================
// FIXED EVALUATION FUNCTIONS WITH FILE ID TRACKING
// ============================================

function loadDynamicFramework(ss) {
  const sheet = ss.getSheetByName('Evaluation Framework');
  
  if (!sheet || sheet.getLastRow() < 2) {
    throw new Error('No evaluation framework found. Please generate and lock a rubric first.');
  }
  
  try {
    const frameworkJson = sheet.getRange(2, 1).getValue();
    return JSON.parse(frameworkJson);
  } catch (error) {
    throw new Error('Invalid evaluation framework. Please regenerate the rubric.');
  }
}

function processCandidateBatch(batch, ss, apiKey, folderId) {
  const results = {
    successful: 0,
    failed: 0,
    skipped: 0,
    errors: []
  };
  
  ensureProcessingStatusSheet(ss);
  
  // Load DYNAMIC framework ONCE for all candidates
  const evaluationFramework = loadDynamicFramework(ss);
  
  // Get position context for interview questions
  const positionContext = getPositionContext(ss);
  
  Logger.log(`Processing batch with framework: ${Object.keys(evaluationFramework).join(', ')}`);
  
  batch.forEach((candidateInfo, index) => {
    candidateInfo.folderId = folderId;
    
    // Enhanced duplicate check with file ID
    if (isDuplicateCandidate(candidateInfo, ss)) {
      logMessage(`⚠️ DUPLICATE SKIPPED: ${candidateInfo.name}`, 'Warning', ss);
      updateCandidateStatus(candidateInfo.name, 'Skipped - Duplicate', ss, 
        `File: ${candidateInfo.cvFileName || 'Unknown'}`);
      results.skipped++;
      return;
    }
    
    let retryCount = 0;
    const maxRetries = 2;
    let processed = false;
    
    while (!processed && retryCount <= maxRetries) {
      try {
        logMessage(`Processing ${index + 1}/${batch.length}: ${candidateInfo.name}` + 
          (retryCount > 0 ? ` (Retry ${retryCount})` : ''), 'Info', ss);
        
        updateCandidateStatus(candidateInfo.name, 'Processing', ss, 
          `File: ${candidateInfo.cvFileName || 'Unknown'}`);
        
        // Process with the SAME framework for consistency
        const result = processCandidate(candidateInfo, ss, apiKey, folderId, evaluationFramework, positionContext);
        
        if (result.error) {
          if (retryCount < maxRetries) {
            retryCount++;
            logMessage(`Retrying ${candidateInfo.name} due to error: ${result.error}`, 'Warning', ss);
            Utilities.sleep(3000);
            continue;
          } else {
            logMessage(`Using default values for ${candidateInfo.name} after ${maxRetries} retries`, 'Warning', ss);
            result.total_score = 0;
            result.recommendation = 'Unable to Evaluate';
            result.hasEvaluationError = true;
            result.categories = {};
            result.strengths = ['Unable to evaluate'];
            result.development_areas = ['Unable to evaluate'];
            result.overall_assessment = 'Evaluation failed - using default values';
            result.interview_questions = getDefaultInterviewQuestions(candidateInfo.name, positionContext);
            
            if (!result.metrics) {
              result.metrics = getDefaultMetrics();
            }
          }
        }
        
        // Add source file info to result for tracking
        result._source_cv_file = candidateInfo.cvFileName;
        result._source_cover_file = candidateInfo.coverLetterFileName;
        result._cv_file_id = result.cvFileId;
        result._cover_file_id = result.coverFileId;
        
        // Write to sheets
        addToCompanyMetrics(result, ss);
        addToCandidateEvaluations(result, ss);
        addToDetailedEvaluations(result, ss);
        addToCombinedAnalysis(result, ss);
        
        // Record the processed file IDs to prevent duplicates
        if (result.cvFileId) {
          recordProcessedFile(
            result.cvFileId,
            result.coverFileId || null,
            candidateInfo.name,
            result.total_score,
            candidateInfo.cvFileName,
            candidateInfo.coverLetterFileName,
            ss
          );
        }
        
        updateCandidateStatus(candidateInfo.name, 'Completed', ss, 
          `File: ${candidateInfo.cvFileName || 'Unknown'}`);
        
        results.successful++;
        processed = true;
        
        const scoreDisplay = result.total_score ? result.total_score.toFixed(1) : '0.0';
        logMessage(`✔ COMPLETED: ${candidateInfo.name} - Score: ${scoreDisplay}` + 
          (result.hasEvaluationError ? ' (with errors)' : ''), 'Success', ss);
        
      } catch (error) {
        if (retryCount < maxRetries) {
          retryCount++;
          logMessage(`Retrying ${candidateInfo.name} due to error: ${error.toString()}`, 'Warning', ss);
          Utilities.sleep(3000);
        } else {
          results.failed++;
          results.errors.push({
            candidate: candidateInfo.name,
            file: candidateInfo.cvFileName,
            error: error.toString()
          });
          
          updateCandidateStatus(candidateInfo.name, 'Failed', ss, 
            `Error: ${error.toString()} | File: ${candidateInfo.cvFileName}`);
          logMessage(`✗ FAILED: ${candidateInfo.name} - Error: ${error.toString()}`, 'Error', ss);
          processed = true;
        }
      }
    }
    
    if (index < batch.length - 1) {
      Utilities.sleep(4000);
    }
  });
  
  return results;
}

function processCandidate(candidateInfo, ss, apiKey, folderId, evaluationFramework, positionContext) {
  const MAX_OCR_RETRIES = 2;
  let lastError = null;
  
  for (let attempt = 0; attempt <= MAX_OCR_RETRIES; attempt++) {
    try {
      const folder = DriveApp.getFolderById(folderId);
      const files = folder.getFiles();
      
      let cvFile = null;
      let coverFile = null;
      let cvFileId = null;
      let coverFileId = null;
      
      while (files.hasNext()) {
        const file = files.next();
        const fileName = file.getName();
        
        if (candidateInfo.cvFileName && fileName === candidateInfo.cvFileName) {
          cvFile = file;
          cvFileId = file.getId();
        }
        if (candidateInfo.coverLetterFileName && fileName === candidateInfo.coverLetterFileName) {
          coverFile = file;
          coverFileId = file.getId();
        }
        
        if (cvFile && (coverFile || !candidateInfo.coverLetterFileName)) {
          break;
        }
      }
      
      if (!cvFile) {
        throw new Error('CV file not found');
      }
      
      // Check if this file has already been processed
      if (isFileAlreadyProcessed(cvFileId, coverFileId, ss)) {
        Logger.log(`File already processed - CV ID: ${cvFileId}`);
        return {
          error: 'File already processed',
          candidate_name: candidateInfo.name,
          cvFileId: cvFileId,
          coverFileId: coverFileId
        };
      }
      
      let cvText, coverText;
      
      try {
        cvText = extractTextFromPDF(cvFile);
      } catch (ocrError) {
        if (ocrError.toString().includes('rate limit') && attempt < MAX_OCR_RETRIES) {
          const backoffTime = Math.pow(2, attempt + 1) * 5000;
          logMessage(`OCR rate limit hit for ${candidateInfo.name}, waiting ${backoffTime/1000}s before retry ${attempt + 1}`, 'Warning', ss);
          Utilities.sleep(backoffTime);
          continue;
        }
        throw ocrError;
      }
      
      coverText = '';
      if (coverFile) {
        try {
          coverText = extractTextFromPDF(coverFile);
        } catch (ocrError) {
          if (ocrError.toString().includes('rate limit') && attempt < MAX_OCR_RETRIES) {
            const backoffTime = Math.pow(2, attempt + 1) * 5000;
            logMessage(`OCR rate limit hit for cover letter, waiting ${backoffTime/1000}s before retry ${attempt + 1}`, 'Warning', ss);
            Utilities.sleep(backoffTime);
            continue;
          }
          logMessage(`Warning: Could not extract cover letter for ${candidateInfo.name}: ${ocrError}`, 'Warning', ss);
        }
      }
      
      const combinedText = cvText + '\n\n---COVER LETTER---\n\n' + coverText;
      
      // Extract enhanced metrics with new fields
      const metrics = extractCompanyMetricsEnhanced(combinedText, candidateInfo.name, positionContext, apiKey, ss);
      
      // Evaluate candidate using SAME framework for all candidates
      const evaluation = evaluateCandidateWithDeterministicScoring(
        combinedText, 
        candidateInfo.name, 
        evaluationFramework,
        apiKey, 
        !!coverFile, 
        ss
      );
      
      // Generate interview questions based on evaluation
      const interviewQuestions = generateInterviewQuestions(
        candidateInfo.name,
        cvText,
        coverText,
        evaluation,
        metrics,
        positionContext,
        apiKey,
        ss
      );
      
      // Combine results and include file IDs
      return {
        ...evaluation,
        metrics: metrics,
        interview_questions: interviewQuestions,
        hasCV: true,
        hasCoverLetter: !!coverFile && coverText.length > 0,
        documentsStatus: candidateInfo.documentsStatus || (coverFile ? 'Both' : 'CV Only'),
        matchConfidence: candidateInfo.matchConfidence,
        matchMethod: candidateInfo.matchMethod,
        cvFileId: cvFileId,
        coverFileId: coverFileId
      };
      
    } catch (error) {
      lastError = error;
      if (attempt === MAX_OCR_RETRIES || !error.toString().includes('rate limit')) {
        return {
          error: lastError.toString(),
          candidate_name: candidateInfo.name,
          interview_questions: getDefaultInterviewQuestions(candidateInfo.name, positionContext)
        };
      }
    }
  }
  
  return {
    error: lastError ? lastError.toString() : 'Unknown error in processing',
    candidate_name: candidateInfo.name,
    interview_questions: getDefaultInterviewQuestions(candidateInfo.name, positionContext)
  };
}

function getDefaultInterviewQuestions(candidateName, positionContext) {
  return [
    `Walk me through your career progression and what led you to apply for this ${positionContext.title || 'position'}.`,
    `What specific accomplishments from your experience are most relevant to this role?`,
    `Describe a challenging project or situation you've faced and how you handled it.`,
    `What interests you most about this opportunity and our organization?`,
    `How do you see yourself contributing to our team in the first 90 days?`,
    `What areas of professional development are you currently focusing on?`
  ];
}

function evaluateCandidateWithDeterministicScoring(text, candidateName, evaluationFramework, apiKey, hasCoverLetter, ss) {
  const apiUrl = 'https://api.anthropic.com/v1/messages';
  
  const categories = Object.keys(evaluationFramework);
  
  const rubricDetails = categories.map(cat => {
    const framework = evaluationFramework[cat];
    return `\n${cat} (${framework.weight.toFixed(1)}% weight):
${framework.attributes.map(attr => {
  const rubricText = attr.rubric ? 
    '\nScoring criteria:\n' + Object.entries(attr.rubric).map(([score, desc]) => 
      `  ${score}: ${desc}`
    ).join('\n') : '';
  return `- ${attr.name}: ${attr.description}${rubricText}`;
}).join('\n')}`;
  }).join('\n');
  
  const documentStatus = hasCoverLetter ? 'BOTH CV and cover letter are provided' : 'ONLY CV is provided (no cover letter)';
  
  const prompt = `Objectively evaluate this candidate using the EXACT scoring rubrics below.
IMPORTANT: ${documentStatus}. Always evaluate using ALL available documents.

CANDIDATE: ${candidateName}

CRITICAL INSTRUCTIONS:
1. Use ONLY the 8 categories and attributes provided below
2. DO NOT modify weights or create new categories
3. Score each attribute 0-5 based STRICTLY on the evidence
4. Provide specific evidence from the documents for each score
5. Be conservative - only give high scores with clear evidence
6. DO NOT calculate category scores - only provide attribute scores
7. Ensure evaluations are unbiased and based solely on merit

FIXED RUBRIC (DO NOT MODIFY WEIGHTS):
${rubricDetails}

DOCUMENT TEXT (${documentStatus}):
${text.substring(0, 14000)}

Return JSON with ONLY attribute scores (DO NOT include category_score):
{
  "candidate_name": "${candidateName}",
  "categories": {
    ${categories.map(cat => 
      `"${cat}": {
        "attributes": [
          {
            "name": "exact attribute name from rubric",
            "score": 0,
            "evidence": "specific evidence from documents"
          },
          {
            "name": "exact attribute name from rubric",
            "score": 0,
            "evidence": "specific evidence from documents"
          },
          {
            "name": "exact attribute name from rubric",
            "score": 0,
            "evidence": "specific evidence from documents"
          },
          {
            "name": "exact attribute name from rubric",
            "score": 0,
            "evidence": "specific evidence from documents"
          },
          {
            "name": "exact attribute name from rubric",
            "score": 0,
            "evidence": "specific evidence from documents"
          }
        ]
      }`
    ).join(',\n    ')}
  },
  "strengths": ["top 3 specific strengths"],
  "development_areas": ["top 2 areas needing development"],
  "overall_assessment": "2-3 sentence balanced summary",
  "documents_evaluated": "${documentStatus}"
}`;

  const options = {
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
      'x-api-key': apiKey,
      'anthropic-version': '2023-06-01'
    },
    payload: JSON.stringify({
      model: 'claude-3-5-sonnet-20241022',
      max_tokens: 4000,
      temperature: 0.1,
      messages: [{role: 'user', content: prompt}]
    }),
    muteHttpExceptions: true
  };
  
  try {
    const response = UrlFetchApp.fetch(apiUrl, options);
    const responseCode = response.getResponseCode();
    
    if (responseCode !== 200) {
      throw new Error(`API returned status ${responseCode}`);
    }
    
    const responseText = response.getContentText();
    const data = JSON.parse(responseText);
    
    // Validate response structure
    if (!data || !data.content || !Array.isArray(data.content) || data.content.length === 0) {
      throw new Error('Invalid API response structure - missing content array');
    }
    
    if (!data.content[0] || !data.content[0].text) {
      throw new Error('Invalid API response structure - missing text in content');
    }
    
    let evaluationText = data.content[0].text;
    
    // Track API usage
    if (ss) {
      let inputTokens, outputTokens;
      
      if (data.usage) {
        inputTokens = data.usage.input_tokens;
        outputTokens = data.usage.output_tokens;
      } else {
        inputTokens = estimateTokens(prompt);
        outputTokens = estimateTokens(evaluationText);
      }
      
      trackAPIUsage(candidateName, 'Candidate Evaluation', inputTokens, outputTokens, ss);
    }
    
    // Clean and parse JSON
    evaluationText = evaluationText.replace(/```json\n?/gi, '').replace(/```\n?/gi, '').trim();
    const jsonMatch = evaluationText.match(/\{[\s\S]*\}/);
    
    if (!jsonMatch) {
      throw new Error('No valid JSON found in response');
    }
    
    const evaluation = JSON.parse(jsonMatch[0]);
    
    // Ensure the evaluation has the expected structure
    if (!evaluation.categories) {
      evaluation.categories = {};
    }
    
    // Fill in missing categories with default values
    categories.forEach(cat => {
      if (!evaluation.categories[cat]) {
        evaluation.categories[cat] = {
          attributes: evaluationFramework[cat].attributes.map(attr => ({
            name: attr.name,
            score: 0,
            evidence: 'No evidence found'
          }))
        };
      }
    });
    
    // Post-process to calculate scores deterministically
    const processedEvaluation = postProcessEvaluation(evaluation, evaluationFramework, candidateName);
    
    return processedEvaluation;
    
  } catch (error) {
    Logger.log(`Evaluation error for ${candidateName}: ${error.toString()}`);
    
    // Return a default evaluation structure
    const defaultEvaluation = {
      candidate_name: candidateName,
      categories: {},
      strengths: ['Unable to evaluate'],
      development_areas: ['Unable to evaluate'],
      overall_assessment: 'Evaluation failed',
      total_score: 0,
      recommendation: 'Unable to Evaluate'
    };
    
    // Fill categories with zeros
    categories.forEach(cat => {
      defaultEvaluation.categories[cat] = {
        category_score: 0,
        weight: evaluationFramework[cat].weight,
        attributes: evaluationFramework[cat].attributes.map(attr => ({
          name: attr.name,
          score: 0,
          evidence: 'Evaluation failed'
        }))
      };
    });
    
    return defaultEvaluation;
  }
}

// ============================================
// COMPANY METRICS EXTRACTION
// ============================================

function extractCompanyMetricsEnhanced(text, candidateName, positionContext, apiKey, ss) {
  const apiUrl = 'https://api.anthropic.com/v1/messages';
  
  const industryPrompt = positionContext ? 
    `The position is for: ${positionContext.title} in the ${positionContext.industry || 'professional services'} industry.` : 
    'Determine the industry from the candidate\'s background.';
  
  const prompt = `Extract comprehensive metrics from this CV and cover letter. 
IMPORTANT: Always extract information from BOTH documents if both are present.

CANDIDATE: ${candidateName}
${industryPrompt}

CRITICAL EXTRACTION RULES:

1. PARSE BOTH CV AND COVER LETTER TOGETHER - Extract information from the entire combined text

2. CAREER EXPERIENCE: Calculate total years of professional experience

3. SHORT STINTS: Only count positions where person LEFT within 12 months (not current roles)

4. MANAGERIAL EXPERIENCE: Calculate years managing people based on titles and responsibilities

5. TEAM SIZE: Find the maximum team size ever managed (look for phrases like "team of X", "managed X people")

6. BUDGET MANAGED: Extract specific amounts with original currency (e.g., "$5M", "€2.5 million", "£100K")

7. EDUCATION: Extract ALL universities and degrees from BOTH CV and cover letter

8. CERTIFICATIONS: Extract professional certifications and licenses

9. COMPANIES: List all notable/prestigious companies worked for (Fortune 500, industry leaders, well-known firms)

10. INDUSTRIES: List all industries the candidate has worked in

11. FUNCTIONAL EXPERTISE: Extract functional areas of expertise (e.g., "Supply Chain", "Marketing", "Finance")

12. TECHNICAL SKILLS: Extract programming languages, tools, technologies, software proficiencies

13. INDUSTRY EXPERIENCE: Calculate total years of experience in the relevant industry/field for this position

14. LANGUAGE QUALITY: Assess the quality of written communication (Very high/High/Medium/Low/Poor) based on grammar, clarity, professionalism

15. X-FACTOR: Extract ONLY the TOP 3 most unique, impressive, or distinctive qualities/achievements. Each should be concise (max 15 words). Focus on:
    - Quantifiable achievements (e.g., "Saved $2M through process optimization")
    - Prestigious awards or recognitions
    - Unique experiences or qualifications
    - Exceptional results or performance

DOCUMENT TEXT:
${text.substring(0, 14000)}

Return JSON with complete information from BOTH documents:
{
  "current_company": "exact company name",
  "time_at_current_company_years": 0.0,
  "advancement_current_company": "specific progression or None evident",
  "total_career_experience_years": 0.0,
  "years_industry_experience": 0.0,
  "relevant_industry": "identified industry",
  "avg_time_per_role_years": 0.0,
  "years_managerial_experience": 0.0,
  "max_team_size_managed": 0,
  "budget_managed": "amount with currency or N/A",
  "short_stints_count": 0,
  "job_hopping_flag": false,
  "notable_companies": ["Company 1", "Company 2"],
  "industries_worked": ["Industry 1", "Industry 2"],
  "universities": ["University Name 1", "University Name 2"],
  "degrees": ["LLB", "BCL", "MBA"],
  "certifications": ["PMP", "CPA", "AWS Certified"],
  "functional_expertise": ["Supply Chain", "Procurement", "Finance"],
  "technical_skills": ["Python", "SAP", "Excel", "Tableau"],
  "language_quality": "Very high",
  "x_factor": ["Max 3 unique achievements, each under 15 words"],
  "employment_history": [
    {
      "company": "name",
      "role": "title",
      "duration_years": 0.0,
      "dates": "2020-2023",
      "is_current": false,
      "had_management_responsibilities": false
    }
  ],
  "data_sources": ["CV", "Cover Letter"]
}`;

  const options = {
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
      'x-api-key': apiKey,
      'anthropic-version': '2023-06-01'
    },
    payload: JSON.stringify({
      model: 'claude-3-5-sonnet-20241022',
      max_tokens: 3000,
      messages: [{role: 'user', content: prompt}]
    }),
    muteHttpExceptions: true
  };
  
  try {
    const response = UrlFetchApp.fetch(apiUrl, options);
    const responseCode = response.getResponseCode();
    
    if (responseCode !== 200) {
      throw new Error(`API returned status ${responseCode}`);
    }
    
    const responseText = response.getContentText();
    const data = JSON.parse(responseText);
    
    if (!data || !data.content || !Array.isArray(data.content) || data.content.length === 0) {
      throw new Error('Invalid API response structure');
    }
    
    let metricsText = data.content[0].text;
    
    if (ss) {
      let inputTokens = data.usage?.input_tokens || estimateTokens(prompt);
      let outputTokens = data.usage?.output_tokens || estimateTokens(metricsText);
      trackAPIUsage(candidateName, 'Enhanced Metrics Extraction', inputTokens, outputTokens, ss);
    }
    
    metricsText = metricsText.replace(/```json\n?/gi, '').replace(/```\n?/gi, '').trim();
    const jsonMatch = metricsText.match(/\{[\s\S]*\}/);
    
    if (!jsonMatch) {
      throw new Error('No valid JSON found in response');
    }
    
    const metrics = JSON.parse(jsonMatch[0]);
    
    // Ensure X-Factor is limited to 3 items
    if (metrics.x_factor && metrics.x_factor.length > 3) {
      metrics.x_factor = metrics.x_factor.slice(0, 3);
    }
    
    // Calculate years of managerial experience if not provided
    if (!metrics.years_managerial_experience && metrics.employment_history) {
      let mgmtYears = 0;
      metrics.employment_history.forEach(job => {
        if (job.had_management_responsibilities || 
            /manager|director|lead|head|supervisor|chief/i.test(job.role)) {
          mgmtYears += job.duration_years || 0;
        }
      });
      metrics.years_managerial_experience = mgmtYears;
    }
    
    return metrics;
    
  } catch (error) {
    Logger.log(`Enhanced metrics extraction error for ${candidateName}: ${error}`);
    logMessage(`Metrics extraction failed for ${candidateName}, using defaults: ${error}`, 'Warning', ss);
    return getDefaultMetrics();
  }
}

function getDefaultMetrics() {
  return {
    current_company: "Error extracting",
    time_at_current_company_years: 0,
    advancement_current_company: "Unknown",
    total_career_experience_years: 0,
    years_industry_experience: 0,
    relevant_industry: "Unknown",
    avg_time_per_role_years: 0,
    years_managerial_experience: 0,
    max_team_size_managed: 0,
    budget_managed: "N/A",
    short_stints_count: 0,
    job_hopping_flag: false,
    notable_companies: [],
    industries_worked: [],
    universities: [],
    degrees: [],
    certifications: [],
    functional_expertise: [],
    technical_skills: [],
    language_quality: "Unknown",
    x_factor: [],
    employment_history: [],
    data_sources: []
  };
}

function getRecommendation(score) {
  if (score >= 80) return 'Strong Candidate';
  if (score >= 70) return 'Good Candidate';
  if (score >= 60) return 'Developing Candidate';
  return 'Poor fit';
}

// ============================================
// POSITION CONTEXT HELPER
// ============================================

function getPositionContext(ss) {
  const configSheet = ss.getSheetByName('Position Configuration');
  const textSheet = ss.getSheetByName('Position Description Text');
  
  let context = {
    title: 'Unknown Position',
    industry: null
  };
  
  if (configSheet && configSheet.getLastRow() > 1) {
    for (let i = 2; i <= configSheet.getLastRow(); i++) {
      if (configSheet.getRange(i, 1).getValue() === 'Position Title') {
        context.title = configSheet.getRange(i, 2).getValue();
        break;
      }
    }
  }
  
  if (textSheet && textSheet.getLastRow() > 1) {
    const positionText = textSheet.getRange(2, 1).getValue();
    
    const industries = {
      'legal': /\b(law|legal|barrister|solicitor|attorney|court|litigation)\b/i,
      'technology': /\b(software|technology|tech|IT|programming|developer|engineer)\b/i,
      'finance': /\b(finance|banking|investment|accounting|financial|treasury)\b/i,
      'healthcare': /\b(medical|healthcare|health|hospital|clinical|patient|doctor|nurse)\b/i,
      'education': /\b(education|teaching|academic|university|school|professor)\b/i,
      'marketing': /\b(marketing|advertising|brand|digital|social media|campaign)\b/i,
      'sales': /\b(sales|business development|account management|revenue)\b/i,
      'consulting': /\b(consulting|advisory|strategy|management consultant)\b/i,
      'manufacturing': /\b(manufacturing|production|factory|assembly|operations)\b/i,
      'retail': /\b(retail|store|shop|customer service|merchandise)\b/i,
      'engineering': /\b(engineering|mechanical|electrical|civil|structural)\b/i,
      'real estate': /\b(real estate|property|realty|leasing|rental)\b/i,
      'hospitality': /\b(hospitality|hotel|restaurant|tourism|catering)\b/i,
      'government': /\b(government|public sector|civil service|municipal|federal)\b/i,
      'non-profit': /\b(non-profit|nonprofit|charity|NGO|foundation)\b/i
    };
    
    for (const [industry, pattern] of Object.entries(industries)) {
      if (pattern.test(positionText.substring(0, 2000))) {
        context.industry = industry;
        break;
      }
    }
    
    if (!context.industry) {
      context.industry = 'professional services';
    }
  }
  
  return context;
}

// ============================================
// HANDLER FUNCTIONS (continued...)
// ============================================

function handleSetupSheets(data) {
  try {
    const spreadsheetId = data.spreadsheetId;
    const ss = SpreadsheetApp.openById(spreadsheetId);
    
    setupAllSheets(ss);
    
    const sheets = ss.getSheets().map(s => s.getName());
    
    return jsonResponse({
      success: true,
      message: 'All sheets created successfully',
      sheets: sheets,
      spreadsheetName: ss.getName()
    });
    
  } catch (error) {
    Logger.log(`Setup sheets error: ${error.toString()}`);
    
    if (error.toString().includes('does not have permission')) {
      const serviceEmail = Session.getActiveUser().getEmail() || 
                          '<EMAIL>';
      
      return jsonResponse({
        success: false,
        error: 'Permission denied to access spreadsheet',
        serviceAccount: serviceEmail,
        suggestion: 'Please share the spreadsheet with the service account above'
      });
    }
    
    return jsonResponse({
      success: false,
      error: error.toString()
    });
  }
}

function handleProcessAllCandidates(data) {
  return processBatchWithSize(data, 5);
}

function handleProcessBatchSize(data) {
  const batchSize = data.batchSize || 5;
  return processBatchWithSize(data, batchSize);
}

function handleProcessBatch(data) {
  return handleProcessBatchSize(data);
}

function handleProcessSpecificCandidates(data) {
  try {
    const spreadsheetId = data.spreadsheetId;
    const claudeApiKey = data.claudeApiKey;
    const cvFolderId = data.cvFolderId;
    const candidateNames = data.candidateNames || [];
    const skipDuplicates = data.skipDuplicates !== false;
    
    if (candidateNames.length === 0) {
      return jsonResponse({
        success: false,
        error: 'No candidates specified'
      });
    }
    
    PropertiesService.getScriptProperties().setProperties({
      'TEMP_CLAUDE_KEY': claudeApiKey,
      'TEMP_FOLDER_ID': cvFolderId,
      'TEMP_CUSTOMER': data.customerIdentifier || 'Unknown'
    });
    
    const ss = SpreadsheetApp.openById(spreadsheetId);
    
    const configSheet = ss.getSheetByName('Position Configuration');
    let rubricLocked = false;
    if (configSheet) {
      for (let i = 2; i <= configSheet.getLastRow(); i++) {
        if (configSheet.getRange(i, 1).getValue() === 'Rubric Status' && 
            configSheet.getRange(i, 2).getValue() === 'LOCKED') {
          rubricLocked = true;
          break;
        }
      }
    }
    
    if (!rubricLocked) {
      return jsonResponse({
        success: false,
        error: 'Rubric not locked. Please generate and lock a rubric before processing candidates.'
      });
    }
    
    logMessage(`Processing specific candidates: ${candidateNames.join(', ')}`, 'Info', ss);
    
    const allCandidates = getAllCandidatesFromMapping(ss);
    const candidatesToProcess = [];
    const skippedDuplicates = [];
    
    candidateNames.forEach(name => {
      const matches = allCandidates.filter(c => 
        c.name.toLowerCase().includes(name.toLowerCase())
      );
      if (matches.length > 0) {
        const candidate = matches[0];
        candidate.folderId = cvFolderId;
        
        if (skipDuplicates && isDuplicateCandidate(candidate, ss)) {
          skippedDuplicates.push(candidate.name);
          logMessage(`Skipping duplicate: ${candidate.name}`, 'Warning', ss);
        } else {
          candidatesToProcess.push(candidate);
        }
      } else {
        logMessage(`Warning: No match found for "${name}"`, 'Warning', ss);
      }
    });
    
    if (candidatesToProcess.length === 0) {
      return jsonResponse({
        success: false,
        error: 'No new candidates to process (all were duplicates or not found)',
        skippedDuplicates: skippedDuplicates
      });
    }
    
    const results = processCandidateBatch(candidatesToProcess, ss, claudeApiKey, cvFolderId);
    
    updateSummaryDashboard(ss);
    
    return jsonResponse({
      success: true,
      message: `Processed ${results.successful} of ${candidatesToProcess.length} candidates`,
      stats: {
        requested: candidateNames.length,
        found: candidatesToProcess.length + skippedDuplicates.length,
        successful: results.successful,
        failed: results.failed,
        skipped: results.skipped + skippedDuplicates.length,
        errors: results.errors,
        skippedDuplicates: skippedDuplicates
      }
    });
    
  } catch (error) {
    Logger.log(`Process specific error: ${error.toString()}`);
    return jsonResponse({
      success: false,
      error: error.toString()
    });
  } finally {
    PropertiesService.getScriptProperties().deleteProperty('TEMP_CLAUDE_KEY');
    PropertiesService.getScriptProperties().deleteProperty('TEMP_FOLDER_ID');
    PropertiesService.getScriptProperties().deleteProperty('TEMP_CUSTOMER');
  }
}

function handleRetryFailedCandidates(data) {
  try {
    const spreadsheetId = data.spreadsheetId;
    const claudeApiKey = data.claudeApiKey;
    const cvFolderId = data.cvFolderId;
    
    const ss = SpreadsheetApp.openById(spreadsheetId);
    
    const failedCandidates = getFailedCandidatesFromStatus(ss);
    
    if (failedCandidates.length === 0) {
      return jsonResponse({
        success: true,
        message: 'No failed candidates to retry',
        stats: {
          failed: 0
        }
      });
    }
    
    logMessage(`Retrying ${failedCandidates.length} failed candidates`, 'Info', ss);
    
    clearFailedStatus(failedCandidates, ss);
    
    failedCandidates.forEach(c => c.folderId = cvFolderId);
    
    const results = processCandidateBatch(failedCandidates, ss, claudeApiKey, cvFolderId);
    
    updateSummaryDashboard(ss);
    
    return jsonResponse({
      success: true,
      message: `Retry complete: ${results.successful} successful, ${results.failed} failed`,
      stats: results
    });
    
  } catch (error) {
    Logger.log(`Retry failed error: ${error.toString()}`);
    return jsonResponse({
      success: false,
      error: error.toString()
    });
  }
}

function handleTestSingleCandidate(data) {
  try {
    const spreadsheetId = data.spreadsheetId;
    const candidateName = data.candidateName;
    const claudeApiKey = data.claudeApiKey;
    const cvFolderId = data.cvFolderId;
    const forceReprocess = data.forceReprocess || false;
    
    const ss = SpreadsheetApp.openById(spreadsheetId);
    
    const candidates = getAllCandidatesFromMapping(ss);
    const matches = candidates.filter(c => 
      c.name.toLowerCase().includes(candidateName.toLowerCase())
    );
    
    if (matches.length === 0) {
      throw new Error(`No candidates found matching "${candidateName}"`);
    }
    
    const candidate = matches[0];
    candidate.folderId = cvFolderId;
    
    if (!forceReprocess && isDuplicateCandidate(candidate, ss)) {
      return jsonResponse({
        success: false,
        error: `${candidate.name} has already been processed. Use "Force Reprocess" option to evaluate again.`,
        isDuplicate: true
      });
    }
    
    const configSheet = ss.getSheetByName('Position Configuration');
    let rubricLocked = false;
    if (configSheet) {
      for (let i = 2; i <= configSheet.getLastRow(); i++) {
        if (configSheet.getRange(i, 1).getValue() === 'Rubric Status' && 
            configSheet.getRange(i, 2).getValue() === 'LOCKED') {
          rubricLocked = true;
          break;
        }
      }
    }
    
    if (!rubricLocked) {
      return jsonResponse({
        success: false,
        error: 'Rubric not locked. Please generate and lock a rubric before testing candidates.'
      });
    }
    
    const folder = DriveApp.getFolderById(cvFolderId);
    
    logMessage(`Testing single candidate: ${candidate.name}${forceReprocess ? ' (Force Reprocess)' : ''}`, 'Info', ss);
    
    const evaluationFramework = loadDynamicFramework(ss);
    const positionContext = getPositionContext(ss);
    
    const result = processCandidate(candidate, ss, claudeApiKey, cvFolderId, evaluationFramework, positionContext);
    
    if (result.error) {
      throw new Error(result.error);
    }
    
    // Record file IDs if successful
    if (result.cvFileId) {
      recordProcessedFile(
        result.cvFileId,
        result.coverFileId || null,
        candidate.name,
        result.total_score,
        candidate.cvFileName,
        candidate.coverLetterFileName,
        ss
      );
    }
    
    addToCompanyMetrics(result, ss);
    addToCandidateEvaluations(result, ss);
    addToDetailedEvaluations(result, ss);
    addToCombinedAnalysis(result, ss);
    
    updateSummaryDashboard(ss);
    
    return jsonResponse({
      success: true,
      message: `Successfully evaluated ${candidate.name}`,
      result: {
        name: result.candidate_name,
        score: result.total_score,
        recommendation: result.recommendation,
        currentCompany: result.metrics.current_company,
        strengths: result.strengths,
        interviewQuestions: result.interview_questions
      }
    });
    
  } catch (error) {
    return jsonResponse({
      success: false,
      error: error.toString()
    });
  }
}

function handleTestDocumentMatching(data) {
  try {
    const spreadsheetId = data.spreadsheetId;
    const cvFolderId = data.cvFolderId;
    
    const ss = SpreadsheetApp.openById(spreadsheetId);
    
    const matches = matchCandidateDocuments(ss, cvFolderId);
    
    const summary = {
      total: matches.length,
      withBoth: matches.filter(m => m.coverLetter).length,
      cvOnly: matches.filter(m => !m.coverLetter).length
    };
    
    return jsonResponse({
      success: true,
      summary: summary,
      sampleMatches: matches.slice(0, 5).map(m => ({
        name: m.name,
        hasCV: !!m.cv,
        hasCoverLetter: !!m.coverLetter
      }))
    });
    
  } catch (error) {
    return jsonResponse({
      success: false,
      error: error.toString()
    });
  }
}

function handleValidateApiKey(data) {
  try {
    const apiKey = data.claudeApiKey;
    
    const testResponse = UrlFetchApp.fetch('https://api.anthropic.com/v1/messages', {
      method: 'post',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': apiKey,
        'anthropic-version': '2023-06-01'
      },
      payload: JSON.stringify({
        model: 'claude-3-5-sonnet-20241022',
        max_tokens: 10,
        messages: [{role: 'user', content: 'Hi'}]
      }),
      muteHttpExceptions: true
    });
    
    if (testResponse.getResponseCode() === 200) {
      return jsonResponse({
        success: true,
        message: 'API key is valid'
      });
    } else {
      return jsonResponse({
        success: false,
        message: 'API key is invalid or has insufficient permissions'
      });
    }
    
  } catch (error) {
    return jsonResponse({
      success: false,
      error: error.toString()
    });
  }
}

function handleCheckPermissions(data) {
  try {
    const spreadsheetId = data.spreadsheetId;
    
    const ss = SpreadsheetApp.openById(spreadsheetId);
    const name = ss.getName();
    
    return jsonResponse({
      success: true,
      hasAccess: true,
      spreadsheetName: name,
      serviceAccount: Session.getActiveUser().getEmail() || 'Service account email'
    });
    
  } catch (error) {
    return jsonResponse({
      success: false,
      hasAccess: false,
      serviceAccount: Session.getActiveUser().getEmail() || 'Service account email',
      error: 'No access to spreadsheet'
    });
  }
}

function handleGetProcessingStatus(data) {
  try {
    const ss = SpreadsheetApp.openById(data.spreadsheetId);
    
    const stats = getDetailedProcessingStats(ss);
    
    return jsonResponse({
      success: true,
      stats: stats
    });
    
  } catch (error) {
    return jsonResponse({
      success: false,
      error: error.toString()
    });
  }
}

function handleGetUnprocessedCandidates(data) {
  try {
    const ss = SpreadsheetApp.openById(data.spreadsheetId);
    
    const processed = getProcessedCandidates(ss);
    const all = getAllCandidatesFromMapping(ss);
    const unprocessed = all.filter(c => !processed.has(c.name));
    
    return jsonResponse({
      success: true,
      candidates: unprocessed.map(c => c.name),
      count: unprocessed.length
    });
    
  } catch (error) {
    return jsonResponse({
      success: false,
      error: error.toString()
    });
  }
}

function handleGetFailedCandidates(data) {
  try {
    const ss = SpreadsheetApp.openById(data.spreadsheetId);
    
    const failed = getFailedCandidatesFromStatus(ss);
    
    return jsonResponse({
      success: true,
      candidates: failed.map(c => c.name),
      count: failed.length
    });
    
  } catch (error) {
    return jsonResponse({
      success: false,
      error: error.toString()
    });
  }
}

function handleClearAllData(data) {
  try {
    const ss = SpreadsheetApp.openById(data.spreadsheetId);
    
    const sheetsToClear = [
      'Processing Log',
      'Processing Status',
      'Document Mapping',
      'Position Configuration',
      'Position Description Text',
      'Dynamic Rubric Configuration',
      'Evaluation Framework',
      'Company Metrics',
      'Candidate Evaluations',
      'Detailed Evaluations',
      'Combined Analysis',
      'API Usage Tracking',
      '_Processed_Files'
    ];
    
    let clearedCount = 0;
    const errors = [];
    
    sheetsToClear.forEach(sheetName => {
      try {
        const sheet = ss.getSheetByName(sheetName);
        if (sheet && sheet.getLastRow() > 1) {
          const lastRow = sheet.getLastRow();
          const lastCol = sheet.getLastColumn();
          if (lastRow > 1) {
            sheet.getRange(2, 1, lastRow - 1, lastCol).clear();
          }
          clearedCount++;
        }
      } catch (e) {
        errors.push(`${sheetName}: ${e.toString()}`);
      }
    });
    
    const dashboardSheet = ss.getSheetByName('Summary Dashboard');
    if (dashboardSheet) {
      dashboardSheet.clear();
      dashboardSheet.getRange(1, 1).setValue("RubiRecruit v7.0.0")
        .setFontSize(18).setFontWeight('bold');
      dashboardSheet.getRange(2, 1).setValue("Last Updated: " + new Date().toLocaleString())
        .setFontSize(12).setFontColor('#666666');
      dashboardSheet.getRange(4, 1).setValue("No data - ready for processing")
        .setFontSize(12);
    }
    
    logMessage('All evaluation data cleared', 'Success', ss);
    
    return jsonResponse({
      success: true,
      message: `Successfully cleared ${clearedCount} sheets`,
      errors: errors
    });
    
  } catch (error) {
    return jsonResponse({
      success: false,
      error: error.toString()
    });
  }
}

// ============================================
// BATCH PROCESSING FUNCTIONS
// ============================================

function processBatchWithSize(data, batchSize) {
  try {
    const spreadsheetId = data.spreadsheetId;
    const claudeApiKey = data.claudeApiKey;
    const cvFolderId = data.cvFolderId;
    const customerIdentifier = data.customerIdentifier;
    
    PropertiesService.getScriptProperties().setProperties({
      'TEMP_CLAUDE_KEY': claudeApiKey,
      'TEMP_FOLDER_ID': cvFolderId,
      'TEMP_CUSTOMER': customerIdentifier
    });
    
    const ss = SpreadsheetApp.openById(spreadsheetId);
    
    const configSheet = ss.getSheetByName('Position Configuration');
    let rubricLocked = false;
    if (configSheet) {
      for (let i = 2; i <= configSheet.getLastRow(); i++) {
        if (configSheet.getRange(i, 1).getValue() === 'Rubric Status' && 
            configSheet.getRange(i, 2).getValue() === 'LOCKED') {
          rubricLocked = true;
          break;
        }
      }
    }
    
    if (!rubricLocked) {
      return jsonResponse({
        success: false,
        error: 'Rubric not locked. Please generate and lock a rubric before processing candidates.'
      });
    }
    
    logMessage(`Starting batch processing (v7.0.0-enhanced) - Max ${batchSize} candidates per batch`, 'Info', ss);
    
    const docMappingSheet = ss.getSheetByName('Document Mapping');
    if (!docMappingSheet || docMappingSheet.getLastRow() <= 1) {
      logMessage('Creating document mapping...', 'Info', ss);
      const candidates = matchCandidateDocuments(ss, cvFolderId);
      logMessage(`Found ${candidates.length} candidates in folder`, 'Success', ss);
    }
    
    const processedCandidates = getProcessedCandidates(ss);
    logMessage(`Found ${processedCandidates.size} already processed candidates`, 'Info', ss);
    
    const allCandidates = getAllCandidatesFromMapping(ss);
    const unprocessedCandidates = allCandidates.filter(c => !processedCandidates.has(c.name));
    
    unprocessedCandidates.forEach(c => c.folderId = cvFolderId);
    
    logMessage(`${unprocessedCandidates.length} candidates remaining to process`, 'Info', ss);
    
    if (unprocessedCandidates.length === 0) {
      return jsonResponse({
        success: true,
        message: 'All candidates have already been processed',
        stats: {
          total: allCandidates.length,
          processed: processedCandidates.size,
          remaining: 0
        }
      });
    }
    
    const batch = unprocessedCandidates.slice(0, batchSize);
    
    logMessage(`Processing batch of ${batch.length} candidates...`, 'Info', ss);
    
    const results = processCandidateBatch(batch, ss, claudeApiKey, cvFolderId);
    
    const stats = {
      total: allCandidates.length,
      processed: processedCandidates.size + results.successful,
      successful: results.successful,
      failed: results.failed,
      skipped: results.skipped,
      remaining: unprocessedCandidates.length - batch.length,
      batchSize: batch.length
    };
    
    updateSummaryDashboard(ss);
    
    const message = stats.remaining > 0 
      ? `Processed ${results.successful} of ${batch.length} candidates. ${stats.remaining} remaining.`
      : `Processing complete! All ${stats.total} candidates have been evaluated.`;
    
    logMessage(message, 'Success', ss);
    
    return jsonResponse({
      success: true,
      message: message,
      stats: stats,
      hasMore: stats.remaining > 0,
      nextBatchSize: Math.min(batchSize, stats.remaining)
    });
    
  } catch (error) {
    Logger.log(`Process batch error: ${error.toString()}`);
    return jsonResponse({
      success: false,
      error: error.toString()
    });
  } finally {
    PropertiesService.getScriptProperties().deleteProperty('TEMP_CLAUDE_KEY');
    PropertiesService.getScriptProperties().deleteProperty('TEMP_FOLDER_ID');
    PropertiesService.getScriptProperties().deleteProperty('TEMP_CUSTOMER');
  }
}

function getProcessedCandidates(ss) {
  const evalSheet = ss.getSheetByName('Candidate Evaluations');
  const processed = new Set();
  
  if (evalSheet && evalSheet.getLastRow() > 1) {
    const data = evalSheet.getRange(2, 2, evalSheet.getLastRow() - 1, 1).getValues();
    data.forEach(row => {
      if (row[0]) processed.add(row[0]);
    });
  }
  
  return processed;
}

function getAllCandidatesFromMapping(ss) {
  const mappingSheet = ss.getSheetByName('Document Mapping');
  const candidates = [];
  
  if (mappingSheet && mappingSheet.getLastRow() > 1) {
    const data = mappingSheet.getRange(2, 1, mappingSheet.getLastRow() - 1, 6).getValues();
    data.forEach(row => {
      if (row[0]) {
        candidates.push({
          name: row[0],
          cvFileName: row[1],
          coverLetterFileName: row[2],
          documentsStatus: row[3],
          matchMethod: row[4],
          matchConfidence: row[5]
        });
      }
    });
  }
  
  return candidates;
}

function getFailedCandidatesFromStatus(ss) {
  const statusSheet = ss.getSheetByName('Processing Status');
  const failed = [];
  
  if (!statusSheet || statusSheet.getLastRow() <= 1) {
    return failed;
  }
  
  const processedNames = getProcessedCandidates(ss);
  const data = statusSheet.getRange(2, 1, statusSheet.getLastRow() - 1, 3).getValues();
  
  data.forEach(row => {
    if (row[2] === 'Failed' && row[1] && !processedNames.has(row[1])) {
      const candidate = getAllCandidatesFromMapping(ss).find(c => c.name === row[1]);
      if (candidate) {
        failed.push(candidate);
      }
    }
  });
  
  return failed;
}

function clearFailedStatus(candidates, ss) {
  const statusSheet = ss.getSheetByName('Processing Status');
  if (!statusSheet) return;
  
  const candidateNames = new Set(candidates.map(c => c.name));
  
  if (statusSheet.getLastRow() > 1) {
    const data = statusSheet.getRange(2, 1, statusSheet.getLastRow() - 1, 3).getValues();
    
    data.forEach((row, index) => {
      if (candidateNames.has(row[1]) && row[2] === 'Failed') {
        statusSheet.getRange(index + 2, 3).setValue('Retrying');
      }
    });
  }
}

function getDetailedProcessingStats(ss) {
  const allCandidates = getAllCandidatesFromMapping(ss);
  const processedCandidates = getProcessedCandidates(ss);
  const failedCandidates = getFailedCandidatesFromStatus(ss);
  
  const evalSheet = ss.getSheetByName('Candidate Evaluations');
  
  const stats = {
    total: allCandidates.length,
    processed: processedCandidates.size,
    failed: failedCandidates.length,
    remaining: allCandidates.length - processedCandidates.size,
    averageScore: 0,
    recommendations: {}
  };
  
  if (evalSheet && evalSheet.getLastRow() > 1) {
    const data = evalSheet.getRange(2, 1, evalSheet.getLastRow() - 1, 13).getValues();
    const scores = [];
    
    data.forEach(row => {
      if (row[2] && !isNaN(row[2])) {
        scores.push(parseFloat(row[2]));
      }
      if (row[11]) {
        stats.recommendations[row[11]] = (stats.recommendations[row[11]] || 0) + 1;
      }
    });
    
    if (scores.length > 0) {
      stats.averageScore = scores.reduce((a, b) => a + b, 0) / scores.length;
    }
  }
  
  return stats;
}

// ============================================
// SHEET SETUP FUNCTIONS
// ============================================

function setupAllSheets(ss) {
  setupProcessingLog(ss);
  setupProcessingStatusSheet(ss);
  setupDocumentMappingSheet(ss);
  setupPositionConfigurationSheet(ss);
  setupPositionTextSheet(ss);
  setupDynamicRubricSheet(ss);
  setupEvaluationFrameworkSheet(ss);
  setupCompanyMetricsSheet(ss);
  setupCandidateEvaluationsSheet(ss);
  setupDetailedEvaluationsSheet(ss);
  setupCombinedAnalysisSheet(ss);
  setupSummaryDashboard(ss);
  setupAPIUsageSheet(ss);
  getProcessedFilesSheet(ss);  // Ensure processed files sheet exists
    
  logMessage('All sheets initialized successfully (RubiRecruit v7.0.0-enhanced)', 'Success', ss);
}

const RUBY_COLOR = '#8B1538';

function setupProcessingLog(ss) {
  let sheet = ss.getSheetByName(CONFIG.SHEETS.PROCESSING_LOG);
  
  if (!sheet) {
    sheet = ss.insertSheet(CONFIG.SHEETS.PROCESSING_LOG);
  }
  
  if (sheet.getLastRow() === 0) {
    const headers = ['Timestamp', 'Message', 'Level', 'Batch Progress'];
    sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
    sheet.getRange(1, 1, 1, headers.length)
      .setFontWeight('bold')
      .setBackground(RUBY_COLOR)
      .setFontColor('#FFFFFF');
    
    sheet.setColumnWidth(1, 150);
    sheet.setColumnWidth(2, 800);
    sheet.setColumnWidth(3, 80);
    sheet.setColumnWidth(4, 150);
    
    sheet.setFrozenRows(1);
    sheet.getRange(1, 1, sheet.getMaxRows(), headers.length).setWrap(true);
  }
}

function setupProcessingStatusSheet(ss) {
  let sheet = ss.getSheetByName(CONFIG.SHEETS.PROCESSING_STATUS);
  
  if (!sheet) {
    sheet = ss.insertSheet(CONFIG.SHEETS.PROCESSING_STATUS);
  }
  
  if (sheet.getLastRow() === 0) {
    const headers = ['Timestamp', 'Candidate', 'Status', 'Error Details'];
    sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
    sheet.getRange(1, 1, 1, headers.length)
      .setFontWeight('bold')
      .setBackground(RUBY_COLOR)
      .setFontColor('#FFFFFF');
    
    sheet.setColumnWidth(1, 150);
    sheet.setColumnWidth(2, 300);
    sheet.setColumnWidth(3, 100);
    sheet.setColumnWidth(4, 500);
    
    sheet.setFrozenRows(1);
    sheet.getRange(1, 1, sheet.getMaxRows(), headers.length).setWrap(true);
  }
}

function setupDocumentMappingSheet(ss) {
  let sheet = ss.getSheetByName(CONFIG.SHEETS.DOCUMENT_MAPPING);
  
  if (!sheet) {
    sheet = ss.insertSheet(CONFIG.SHEETS.DOCUMENT_MAPPING);
  }
  
  if (sheet.getLastRow() === 0) {
    const headers = [
      'Candidate Name',
      'CV File',
      'Cover Letter File',
      'Documents Status',
      'Match Method',
      'Match Confidence'
    ];
    
    sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
    sheet.getRange(1, 1, 1, headers.length)
      .setFontWeight('bold')
      .setBackground(RUBY_COLOR)
      .setFontColor('#FFFFFF');
    
    sheet.setColumnWidth(1, 400);
    sheet.setColumnWidth(2, 450);
    sheet.setColumnWidth(3, 450);
    sheet.setColumnWidth(4, 150);
    sheet.setColumnWidth(5, 120);
    sheet.setColumnWidth(6, 120);
    
    sheet.setFrozenRows(1);
    sheet.getRange(1, 1, sheet.getMaxRows(), headers.length).setWrap(true);
  }
}

function setupPositionConfigurationSheet(ss) {
  let sheet = ss.getSheetByName(CONFIG.SHEETS.POSITION_CONFIG);
  
  if (!sheet) {
    sheet = ss.insertSheet(CONFIG.SHEETS.POSITION_CONFIG);
  }
  
  if (sheet.getLastRow() === 0) {
    const headers = ['Field', 'Value', 'Status'];
    sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
    sheet.getRange(1, 1, 1, headers.length)
      .setFontWeight('bold')
      .setBackground(RUBY_COLOR)
      .setFontColor('#FFFFFF');
    
    sheet.setColumnWidth(1, 200);
    sheet.setColumnWidth(2, 300);
    sheet.setColumnWidth(3, 150);
    
    sheet.setFrozenRows(1);
    sheet.getRange(1, 1, sheet.getMaxRows(), headers.length).setWrap(true);
  }
}

function setupDynamicRubricSheet(ss) {
  let sheet = ss.getSheetByName(CONFIG.SHEETS.DYNAMIC_RUBRIC);
  
  if (!sheet) {
    sheet = ss.insertSheet(CONFIG.SHEETS.DYNAMIC_RUBRIC);
  }
  
  if (sheet.getLastRow() === 0) {
    const headers = [
      'Category',
      'Category Weight (%)',
      'Attribute',
      'Attribute Weight (%)',
      'Description',
      'Score 0',
      'Score 1',
      'Score 2',
      'Score 3',
      'Score 4',
      'Score 5'
    ];
    
    sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
    sheet.getRange(1, 1, 1, headers.length)
      .setFontWeight('bold')
      .setBackground(RUBY_COLOR)
      .setFontColor('#FFFFFF');
  }
}

function setupCompanyMetricsSheet(ss) {
  let sheet = ss.getSheetByName(CONFIG.SHEETS.COMPANY_METRICS);
  
  if (!sheet) {
    sheet = ss.insertSheet(CONFIG.SHEETS.COMPANY_METRICS);
  }
  
  if (sheet.getLastRow() === 0) {
    const headers = [
      'Evaluation Date',
      'Candidate Name',
      'Overall Score',
      'Current Company',
      'Time at Current (years)',
      'Advancement at Current',
      'Total Career Experience (years)',
      'Industry Experience (years)',
      'Avg Time per Role (years)',
      'Years Managerial Experience',
      'Max Team Size Managed',
      'Budget Managed',
      'Short Stints (<1yr)',
      'Job Hopping Flag',
      'Notable Companies',
      'Industries Worked',
      'Universities',
      'Degrees',
      'Certifications',
      'Functional Expertise',
      'Technical Skills',
      'X-Factor',
      'Language Quality',
      'Documents Status',
      'Match Confidence'
    ];
    
    sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
    sheet.getRange(1, 1, 1, headers.length)
      .setFontWeight('bold')
      .setBackground(RUBY_COLOR)
      .setFontColor('#FFFFFF');
    
    sheet.setColumnWidth(1, 120);
    sheet.setColumnWidth(2, 200);
    sheet.setColumnWidth(3, 100);
    sheet.setColumnWidth(4, 200);
    sheet.setColumnWidth(5, 120);
    sheet.setColumnWidth(6, 180);
    sheet.setColumnWidth(7, 150);
    sheet.setColumnWidth(8, 150);
    sheet.setColumnWidth(9, 120);
    sheet.setColumnWidth(10, 150);
    sheet.setColumnWidth(11, 150);
    sheet.setColumnWidth(12, 150);
    sheet.setColumnWidth(13, 120);
    sheet.setColumnWidth(14, 120);
    sheet.setColumnWidth(15, 250);
    sheet.setColumnWidth(16, 350);
    sheet.setColumnWidth(17, 350);
    sheet.setColumnWidth(18, 350);
    sheet.setColumnWidth(19, 200);
    sheet.setColumnWidth(20, 250);
    sheet.setColumnWidth(21, 300);
    sheet.setColumnWidth(22, 650);
    sheet.setColumnWidth(23, 150);
    sheet.setColumnWidth(24, 150);
    sheet.setColumnWidth(25, 120);
    
    sheet.setFrozenRows(1);
    sheet.getRange(1, 1, sheet.getMaxRows(), headers.length).setWrap(true);
  }
}

function setupDetailedEvaluationsSheet(ss) {
  let sheet = ss.getSheetByName(CONFIG.SHEETS.DETAILED_EVALS);
  
  if (!sheet) {
    sheet = ss.insertSheet(CONFIG.SHEETS.DETAILED_EVALS);
  }
  
  if (sheet.getLastRow() === 0) {
    const headers = [
      'Date',
      'Candidate',
      'Category',
      'Category Weight (%)',
      'Category Score',
      'Attribute',
      'Attribute Weight',
      'Score (0-5)',
      'Evidence'
    ];
    
    sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
    sheet.getRange(1, 1, 1, headers.length)
      .setFontWeight('bold')
      .setBackground(RUBY_COLOR)
      .setFontColor('#FFFFFF');
    
    sheet.setFrozenRows(1);
    sheet.getRange(1, 1, sheet.getMaxRows(), headers.length).setWrap(true);
  }
}

function setupCombinedAnalysisSheet(ss) {
  let sheet = ss.getSheetByName(CONFIG.SHEETS.COMBINED_ANALYSIS);
  
  if (!sheet) {
    sheet = ss.insertSheet(CONFIG.SHEETS.COMBINED_ANALYSIS);
  }
  
  if (sheet.getLastRow() === 0) {
    const headers = [
      'Candidate',
      'Total Score',
      'Recommendation',
      'Documents Status',
      'Current Company',
      'Industry Experience (years)',
      'Job Hopping Risk',
      'Top Strengths',
      'Development Areas',
      'Next Steps',
      'Interview Questions (6 Tailored)'
    ];
    
    sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
    sheet.getRange(1, 1, 1, headers.length)
      .setFontWeight('bold')
      .setBackground(RUBY_COLOR)
      .setFontColor('#FFFFFF');
    
    sheet.setColumnWidth(1, 200);
    sheet.setColumnWidth(2, 100);
    sheet.setColumnWidth(3, 150);
    sheet.setColumnWidth(4, 150);
    sheet.setColumnWidth(5, 200);
    sheet.setColumnWidth(6, 120);
    sheet.setColumnWidth(7, 120);
    sheet.setColumnWidth(8, 400);
    sheet.setColumnWidth(9, 400);
    sheet.setColumnWidth(10, 300);
    sheet.setColumnWidth(11, 800); // Wide column for interview questions
    
    sheet.setFrozenRows(1);
    sheet.getRange(1, 1, sheet.getMaxRows(), headers.length).setWrap(true);
  }
}

function setupAPIUsageSheet(ss) {
  let sheet = ss.getSheetByName(CONFIG.SHEETS.API_USAGE);
  
  if (!sheet) {
    sheet = ss.insertSheet(CONFIG.SHEETS.API_USAGE);
  }
  
  if (sheet.getLastRow() === 0) {
    const headers = [
      'Timestamp',
      'Candidate',
      'API Call Type',
      'Input Tokens',
      'Output Tokens',
      'Total Tokens',
      'Input Cost ($)',
      'Output Cost ($)',
      'Total Cost ($)'
    ];
    
    sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
    sheet.getRange(1, 1, 1, headers.length)
      .setFontWeight('bold')
      .setBackground(RUBY_COLOR)
      .setFontColor('#FFFFFF');
    
    sheet.setFrozenRows(1);
  }
}

function setupPositionTextSheet(ss) {
  let sheet = ss.getSheetByName(CONFIG.SHEETS.POSITION_TEXT);
  
  if (!sheet) {
    sheet = ss.insertSheet(CONFIG.SHEETS.POSITION_TEXT);
  }
  
  if (sheet.getLastRow() === 0) {
    const headers = ['Position Description Text'];
    sheet.getRange(1, 1).setValue(headers[0]);
    sheet.getRange(1, 1)
      .setFontWeight('bold')
      .setBackground(RUBY_COLOR)
      .setFontColor('#FFFFFF')
      .setFontSize(14);
    
    sheet.setColumnWidth(1, 800);
    sheet.getRange(1, 1, sheet.getMaxRows(), 1).setWrap(true);
  }
}

function setupEvaluationFrameworkSheet(ss) {
  let sheet = ss.getSheetByName(CONFIG.SHEETS.EVAL_FRAMEWORK);
  
  if (!sheet) {
    sheet = ss.insertSheet(CONFIG.SHEETS.EVAL_FRAMEWORK);
  }
  
  if (sheet.getLastRow() === 0) {
    const headers = ['Evaluation Framework JSON'];
    sheet.getRange(1, 1).setValue(headers[0]);
    sheet.getRange(1, 1)
      .setFontWeight('bold')
      .setBackground(RUBY_COLOR)
      .setFontColor('#FFFFFF');
    
    sheet.setColumnWidth(1, 800);
  }
  
  sheet.hideSheet();
}

function setupCandidateEvaluationsSheet(ss) {
  let sheet = ss.getSheetByName(CONFIG.SHEETS.CANDIDATE_EVALS);
  
  if (!sheet) {
    sheet = ss.insertSheet(CONFIG.SHEETS.CANDIDATE_EVALS);
  }
  
  if (sheet.getLastRow() === 0) {
    const headers = [
      'Date',
      'Candidate Name',
      'Total Score',
      'Category 1',
      'Category 2', 
      'Category 3',
      'Category 4',
      'Category 5',
      'Category 6',
      'Category 7',
      'Category 8',
      'Recommendation',
      'Overall Assessment'
    ];
    
    sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
    sheet.getRange(1, 1, 1, headers.length)
      .setFontWeight('bold')
      .setBackground(RUBY_COLOR)
      .setFontColor('#FFFFFF');
    
    sheet.setColumnWidth(1, 120);
    sheet.setColumnWidth(2, 200);
    sheet.setColumnWidth(3, 100);
    for (let i = 4; i <= 11; i++) {
      sheet.setColumnWidth(i, 100);
    }
    sheet.setColumnWidth(12, 150);
    sheet.setColumnWidth(13, 400);
    
    sheet.setFrozenRows(1);
    sheet.getRange(1, 1, sheet.getMaxRows(), headers.length).setWrap(true);
  }
}

function setupSummaryDashboard(ss) {
  let sheet = ss.getSheetByName(CONFIG.SHEETS.SUMMARY_DASHBOARD);
  
  if (!sheet) {
    sheet = ss.insertSheet(CONFIG.SHEETS.SUMMARY_DASHBOARD);
  }
  
  sheet.clear();
  sheet.getRange(1, 1).setValue("RubiRecruit v7.0.0-enhanced")
    .setFontSize(18)
    .setFontWeight('bold')
    .setFontColor(RUBY_COLOR);
  
  sheet.getRange(2, 1).setValue("Last Updated: " + new Date().toLocaleString())
    .setFontSize(12)
    .setFontColor('#666666');
  
  sheet.getRange(4, 1).setValue("No data - ready for processing")
    .setFontSize(12);
  
  sheet.setColumnWidth(1, 200);
  sheet.setColumnWidth(2, 150);
  sheet.setColumnWidth(3, 100);
  sheet.setColumnWidth(4, 150);
}

// ============================================
// DATA WRITING FUNCTIONS (ENHANCED)
// ============================================

function addToCompanyMetrics(result, ss) {
  const sheet = ss.getSheetByName(CONFIG.SHEETS.COMPANY_METRICS);
  const metrics = result.metrics || getDefaultMetrics();
  
  const row = [
    new Date(),
    result.candidate_name,
    result.total_score ? result.total_score.toFixed(1) : '0.0',
    metrics.current_company || 'Not found',
    metrics.time_at_current_company_years || 0,
    metrics.advancement_current_company || 'None evident',
    metrics.total_career_experience_years || 0,
    metrics.years_industry_experience || 0,
    metrics.avg_time_per_role_years || 0,
    metrics.years_managerial_experience || 0,
    metrics.max_team_size_managed || 0,
    metrics.budget_managed || 'N/A',
    metrics.short_stints_count || 0,
    metrics.job_hopping_flag ? 'Yes' : 'No',
    metrics.notable_companies ? metrics.notable_companies.join('; ') : '',
    metrics.industries_worked ? metrics.industries_worked.join('; ') : '',
    metrics.universities ? metrics.universities.join('; ') : '',
    metrics.degrees ? metrics.degrees.join('; ') : '',
    metrics.certifications ? metrics.certifications.join('; ') : '',
    metrics.functional_expertise ? metrics.functional_expertise.join('; ') : '',
    metrics.technical_skills ? metrics.technical_skills.join('; ') : '',
    metrics.x_factor && metrics.x_factor.length > 0 ? metrics.x_factor.join('; ') : 'None identified',
    metrics.language_quality || 'Unknown',
    result.documentsStatus || 'Unknown',
    result.matchConfidence || 'N/A'
  ];
  
  sheet.appendRow(row);
  
  const lastRow = sheet.getLastRow();
  
  const scoreCell = sheet.getRange(lastRow, 3);
  const score = parseFloat(result.total_score || 0);
  
  if (score >= 80) {
    scoreCell.setBackground('#4CAF50').setFontColor('#FFFFFF');
  } else if (score >= 70) {
    scoreCell.setBackground('#8BC34A');
  } else if (score >= 60) {
    scoreCell.setBackground('#FFC107');
  } else {
    scoreCell.setBackground('#FF9800').setFontColor('#FFFFFF');
  }
  
  if (metrics.job_hopping_flag) {
    sheet.getRange(lastRow, 14).setBackground('#FFCDD2');
  } else {
    sheet.getRange(lastRow, 14).setBackground('#C8E6C9');
  }
  
  if (metrics.x_factor && metrics.x_factor.length > 0) {
    sheet.getRange(lastRow, 22).setBackground('#E1BEE7');
  }
  
  const langQualityCell = sheet.getRange(lastRow, 23);
  switch(metrics.language_quality) {
    case 'Very high':
      langQualityCell.setBackground('#1B5E20').setFontColor('#FFFFFF');
      break;
    case 'High':
      langQualityCell.setBackground('#4CAF50').setFontColor('#FFFFFF');
      break;
    case 'Medium':
      langQualityCell.setBackground('#FFC107');
      break;
    case 'Low':
      langQualityCell.setBackground('#FF9800').setFontColor('#FFFFFF');
      break;
    case 'Poor':
      langQualityCell.setBackground('#B71C1C').setFontColor('#FFFFFF');
      break;
  }
  
  const matchConfidence = result.matchConfidence;
  if (matchConfidence === 'high') {
    sheet.getRange(lastRow, 25).setBackground('#C8E6C9');
  } else if (matchConfidence === 'medium') {
    sheet.getRange(lastRow, 25).setBackground('#FFF9C4');
  } else if (matchConfidence === 'low') {
    sheet.getRange(lastRow, 25).setBackground('#FFE0B2');
  }
}

function addToCandidateEvaluations(result, ss) {
  const sheet = ss.getSheetByName('Candidate Evaluations');
  
  const categoryNames = Object.keys(result.categories || {});
  
  const row = [
    new Date(),
    result.candidate_name,
    result.total_score ? result.total_score.toFixed(1) : '0.0'
  ];
  
  for (let i = 0; i < 8; i++) {
    if (categoryNames[i] && result.categories[categoryNames[i]]) {
      const categoryScore = result.categories[categoryNames[i]].category_score;
      row.push(categoryScore ? categoryScore.toFixed(1) : '0.0');
    } else {
      row.push('N/A');
    }
  }
  
  row.push(result.recommendation || 'Unable to Evaluate');
  row.push(result.overall_assessment || 'Error in evaluation');
  
  sheet.appendRow(row);
  
  const lastRow = sheet.getLastRow();
  
  const scoreCell = sheet.getRange(lastRow, 3);
  const score = parseFloat(result.total_score || 0);
  
  if (score >= 80) {
    scoreCell.setBackground('#4CAF50').setFontColor('#FFFFFF');
  } else if (score >= 70) {
    scoreCell.setBackground('#8BC34A');
  } else if (score >= 60) {
    scoreCell.setBackground('#FFC107');
  } else {
    scoreCell.setBackground('#FF9800').setFontColor('#FFFFFF');
  }
  
  for (let col = 4; col <= 11; col++) {
    const categoryScore = parseFloat(row[col - 1]);
    if (!isNaN(categoryScore)) {
      const categoryCell = sheet.getRange(lastRow, col);
      
      if (categoryScore >= 80) {
        categoryCell.setBackground('#4CAF50').setFontColor('#FFFFFF');
      } else if (categoryScore >= 70) {
        categoryCell.setBackground('#8BC34A');
      } else if (categoryScore >= 60) {
        categoryCell.setBackground('#FFC107');
      } else {
        categoryCell.setBackground('#FF9800').setFontColor('#FFFFFF');
      }
    }
  }
  
  const recCell = sheet.getRange(lastRow, 12);
  const rec = result.recommendation;
  
  if (rec === 'Strong Candidate') {
    recCell.setBackground('#4CAF50').setFontColor('#FFFFFF');
  } else if (rec === 'Good Candidate') {
    recCell.setBackground('#8BC34A');
  } else if (rec === 'Developing Candidate') {
    recCell.setBackground('#FFC107');
  } else {
    recCell.setBackground('#FF9800').setFontColor('#FFFFFF');
  }
}

function addToDetailedEvaluations(result, ss) {
  const sheet = ss.getSheetByName('Detailed Evaluations');
  const rows = [];
  const evalDate = new Date();
  
  if (result.categories) {
    Object.entries(result.categories).forEach(([categoryName, categoryData]) => {
      const categoryWeight = categoryData.weight || 0;
      
      if (categoryData.attributes) {
        categoryData.attributes.forEach(attr => {
          rows.push([
            evalDate,
            result.candidate_name,
            categoryName,
            categoryWeight,
            categoryData.category_score ? categoryData.category_score.toFixed(1) : '0.0',
            attr.name,
            attr.weight ? attr.weight.toFixed(2) : '0.00',
            attr.score || 0,
            attr.evidence || ''
          ]);
        });
      }
    });
  }
  
  if (rows.length > 0) {
    const startRow = sheet.getLastRow() + 1;
    sheet.getRange(startRow, 1, rows.length, rows[0].length).setValues(rows);
    
    for (let i = 0; i < rows.length; i++) {
      const rowNum = startRow + i;
      const score = rows[i][7];
      const scoreCell = sheet.getRange(rowNum, 8);
      
      if (score >= 4) {
        scoreCell.setBackground('#4CAF50').setFontColor('#FFFFFF');
      } else if (score >= 3) {
        scoreCell.setBackground('#8BC34A');
      } else if (score >= 2) {
        scoreCell.setBackground('#FFC107');
      } else if (score >= 1) {
        scoreCell.setBackground('#FF9800').setFontColor('#FFFFFF');
      } else {
        scoreCell.setBackground('#F44336').setFontColor('#FFFFFF');
      }
    }
  }
}

function addToCombinedAnalysis(result, ss) {
  const sheet = ss.getSheetByName('Combined Analysis');
  const metrics = result.metrics || {};
  
  // Format interview questions as numbered list
  let interviewQuestions = '';
  if (result.interview_questions && Array.isArray(result.interview_questions)) {
    interviewQuestions = result.interview_questions
      .map((q, index) => `${index + 1}. ${q}`)
      .join('\n\n');
  } else {
    interviewQuestions = 'No specific questions generated';
  }
  
  const row = [
    result.candidate_name,
    result.total_score ? result.total_score.toFixed(1) : '0.0',
    result.recommendation || 'Unable to Evaluate',
    result.documentsStatus || 'Unknown',
    metrics.current_company || 'Not found',
    metrics.years_industry_experience || 0,
    metrics.job_hopping_flag ? 'High' : 'Low',
    result.strengths ? result.strengths.join('; ') : 'None identified',
    result.development_areas ? result.development_areas.join('; ') : 'None identified',
    getNextSteps(result.total_score),
    interviewQuestions
  ];
  
  sheet.appendRow(row);
  
  const lastRow = sheet.getLastRow();
  
  const scoreCell = sheet.getRange(lastRow, 2);
  const score = parseFloat(result.total_score || 0);
  
  if (score >= 80) {
    scoreCell.setBackground('#4CAF50').setFontColor('#FFFFFF');
  } else if (score >= 70) {
    scoreCell.setBackground('#8BC34A');
  } else if (score >= 60) {
    scoreCell.setBackground('#FFC107');
  } else {
    scoreCell.setBackground('#FF9800').setFontColor('#FFFFFF');
  }
  
  if (metrics.job_hopping_flag) {
    sheet.getRange(lastRow, 7).setBackground('#FFCDD2');
  } else {
    sheet.getRange(lastRow, 7).setBackground('#C8E6C9');
  }
  
  // Highlight the interview questions column
  sheet.getRange(lastRow, 11).setBackground('#F3E5F5').setVerticalAlignment('top');
}

function getNextSteps(score) {
  if (score >= 80) {
    return 'Schedule first interview immediately; Fast-track through process';
  } else if (score >= 70) {
    return 'Schedule first interview; Standard process';
  } else if (score >= 60) {
    return 'Consider for interview if strong in key areas; Additional screening may be needed';
  } else {
    return 'Not recommended for interview at this time';
  }
}

// ============================================
// DASHBOARD UPDATE FUNCTION
// ============================================

function updateSummaryDashboard(ss) {
  const dashboardSheet = ss.getSheetByName('Summary Dashboard');
  if (!dashboardSheet) return;
  
  dashboardSheet.clear();
  
  dashboardSheet.getRange(1, 1).setValue("RubiRecruit v7.0.0-enhanced - Summary Dashboard")
    .setFontSize(18)
    .setFontWeight('bold')
    .setFontColor(RUBY_COLOR);
  
  dashboardSheet.getRange(2, 1).setValue("Last Updated: " + new Date().toLocaleString())
    .setFontSize(12)
    .setFontColor('#666666');
  
  const stats = getDetailedProcessingStats(ss);
  
  let row = 4;
  dashboardSheet.getRange(row, 1).setValue("OVERALL STATISTICS")
    .setFontSize(14)
    .setFontWeight('bold')
    .setBackground(RUBY_COLOR)
    .setFontColor('#FFFFFF');
  
  row++;
  const statsData = [
    ['Total Candidates', stats.total],
    ['Processed', stats.processed],
    ['Failed', stats.failed],
    ['Remaining', stats.remaining],
    ['Average Score', stats.averageScore ? stats.averageScore.toFixed(1) : 'N/A']
  ];
  
  dashboardSheet.getRange(row, 1, statsData.length, 2).setValues(statsData);
  row += statsData.length + 2;
  
  if (Object.keys(stats.recommendations).length > 0) {
    dashboardSheet.getRange(row, 1).setValue("RECOMMENDATIONS DISTRIBUTION")
      .setFontSize(14)
      .setFontWeight('bold')
      .setBackground(RUBY_COLOR)
      .setFontColor('#FFFFFF');
    
    row++;
    Object.entries(stats.recommendations).forEach(([rec, count]) => {
      dashboardSheet.getRange(row, 1).setValue(rec);
      dashboardSheet.getRange(row, 2).setValue(count);
      
      if (rec === 'Strong Candidate') {
        dashboardSheet.getRange(row, 1, 1, 2).setBackground('#4CAF50').setFontColor('#FFFFFF');
      } else if (rec === 'Good Candidate') {
        dashboardSheet.getRange(row, 1, 1, 2).setBackground('#8BC34A');
      } else if (rec === 'Developing Candidate') {
        dashboardSheet.getRange(row, 1, 1, 2).setBackground('#FFC107');
      } else {
        dashboardSheet.getRange(row, 1, 1, 2).setBackground('#FF9800').setFontColor('#FFFFFF');
      }
      row++;
    });
    row += 2;
  }
  
  const evalSheet = ss.getSheetByName('Candidate Evaluations');
  if (evalSheet && evalSheet.getLastRow() > 1) {
    dashboardSheet.getRange(row, 1).setValue("TOP CANDIDATES")
      .setFontSize(14)
      .setFontWeight('bold')
      .setBackground(RUBY_COLOR)
      .setFontColor('#FFFFFF');
    
    row++;
    dashboardSheet.getRange(row, 1, 1, 4).setValues([['Rank', 'Name', 'Score', 'Recommendation']]);
    dashboardSheet.getRange(row, 1, 1, 4).setFontWeight('bold').setBackground('#E3F2FD');
    row++;
    
    const candidateData = [];
    if (evalSheet.getLastRow() > 1) {
      const data = evalSheet.getRange(2, 2, evalSheet.getLastRow() - 1, 11).getValues();
      data.forEach(row => {
        if (row[0] && row[1]) {
          candidateData.push({
            name: row[0],
            score: parseFloat(row[1]),
            recommendation: row[10]
          });
        }
      });
    }
    
    candidateData.sort((a, b) => b.score - a.score);
    
    const topCandidates = candidateData.slice(0, 10);
    topCandidates.forEach((candidate, index) => {
      dashboardSheet.getRange(row, 1).setValue(index + 1);
      dashboardSheet.getRange(row, 2).setValue(candidate.name);
      dashboardSheet.getRange(row, 3).setValue(candidate.score.toFixed(1));
      dashboardSheet.getRange(row, 4).setValue(candidate.recommendation);
      
      const scoreCell = dashboardSheet.getRange(row, 3);
      if (candidate.score >= 80) {
        scoreCell.setBackground('#4CAF50').setFontColor('#FFFFFF');
      } else if (candidate.score >= 70) {
        scoreCell.setBackground('#8BC34A');
      } else if (candidate.score >= 60) {
        scoreCell.setBackground('#FFC107');
      } else {
        scoreCell.setBackground('#FF9800').setFontColor('#FFFFFF');
      }
      row++;
    });
  }
  
  dashboardSheet.setColumnWidth(1, 200);
  dashboardSheet.setColumnWidth(2, 250);
  dashboardSheet.setColumnWidth(3, 100);
  dashboardSheet.setColumnWidth(4, 180);
  
  dashboardSheet.getRange(1, 1, dashboardSheet.getLastRow(), 4).setWrap(true);
}

// ============================================
// DOCUMENT MATCHING FUNCTIONS
// ============================================

function matchCandidateDocuments(ss, folderId) {
  const folder = DriveApp.getFolderById(folderId);
  const files = folder.getFiles();
  
  const documents = [];
  while (files.hasNext()) {
    const file = files.next();
    const fileName = file.getName();
    
    if (fileName.toLowerCase().endsWith('.pdf')) {
      documents.push({
        name: fileName,
        type: detectDocumentType(fileName),
        candidateName: extractCandidateName(fileName),
        file: file
      });
    }
  }
  
  const candidateMap = new Map();
  
  documents.forEach(doc => {
    const key = doc.candidateName.toLowerCase();
    if (!candidateMap.has(key)) {
      candidateMap.set(key, {
        name: doc.candidateName,
        cv: null,
        coverLetter: null,
        cvFileName: null,
        coverLetterFileName: null
      });
    }
    
    const candidate = candidateMap.get(key);
    if (doc.type === 'cv') {
      candidate.cv = doc.file;
      candidate.cvFileName = doc.name;
    } else if (doc.type === 'cover') {
      candidate.coverLetter = doc.file;
      candidate.coverLetterFileName = doc.name;
    }
  });
  
  performAdvancedMatching(documents, candidateMap);
  
  const mappingSheet = ss.getSheetByName('Document Mapping');
  if (!mappingSheet) {
    setupDocumentMappingSheet(ss);
  }
  
  const rows = [];
  candidateMap.forEach(candidate => {
    if (candidate.cv) {
      rows.push([
        candidate.name,
        candidate.cvFileName,
        candidate.coverLetterFileName || '',
        candidate.coverLetter ? 'Both' : 'CV Only',
        candidate.matchMethod || 'Direct',
        candidate.matchConfidence || 'high'
      ]);
    }
  });
  
  if (rows.length > 0) {
    if (mappingSheet.getLastRow() > 1) {
      mappingSheet.getRange(2, 1, mappingSheet.getLastRow() - 1, 6).clear();
    }
    mappingSheet.getRange(2, 1, rows.length, 6).setValues(rows);
  }
  
  logMessage(`Document mapping complete: ${rows.length} candidates found`, 'Success', ss);
  
  return Array.from(candidateMap.values());
}

function detectDocumentType(fileName) {
  const lowerName = fileName.toLowerCase();
  
  if (lowerName.includes('cover') || lowerName.includes('cl_') || 
      lowerName.includes('letter') || lowerName.includes('motivation')) {
    return 'cover';
  }
  
  if (lowerName.includes('cv') || lowerName.includes('resume') || 
      lowerName.includes('curriculum')) {
    return 'cv';
  }
  
  return 'cv';
}

function extractCandidateName(fileName) {
  let name = fileName.replace(/\.[^/.]+$/, '');
  
  name = name.replace(/[\-_]?(cv|resume|cover[\s_-]?letter|cl|curriculum[\s_-]?vitae)/gi, '');
  
  name = name.replace(/[\-_]?(senior|junior|manager|coordinator|specialist|analyst|developer|engineer|consultant|administrator|director|executive|assistant|associate)/gi, '');
  
  name = name.replace(/[\d]{2,4}[\-\/]?[\d]{0,2}[\-\/]?[\d]{0,4}/g, '');
  name = name.replace(/v?\d+(\.\d+)?/gi, '');
  
  name = name.replace(/[\-_]+/g, ' ');
  
  name = name.trim();
  
  name = name.split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
  
  return name || 'Unknown Candidate';
}

function performAdvancedMatching(documents, candidateMap) {
  const unmatchedCVs = [];
  const unmatchedCovers = [];
  
  documents.forEach(doc => {
    const key = doc.candidateName.toLowerCase();
    const candidate = candidateMap.get(key);
    
    if (doc.type === 'cv' && (!candidate || !candidate.cv)) {
      unmatchedCVs.push(doc);
    } else if (doc.type === 'cover' && (!candidate || !candidate.coverLetter)) {
      unmatchedCovers.push(doc);
    }
  });
  
  unmatchedCVs.forEach(cv => {
    const bestMatch = findBestMatch(cv.candidateName, unmatchedCovers);
    if (bestMatch && bestMatch.score > 0.7) {
      const key = cv.candidateName.toLowerCase();
      if (!candidateMap.has(key)) {
        candidateMap.set(key, {
          name: cv.candidateName,
          cv: cv.file,
          coverLetter: bestMatch.doc.file,
          cvFileName: cv.name,
          coverLetterFileName: bestMatch.doc.name,
          matchMethod: 'Fuzzy',
          matchConfidence: bestMatch.score > 0.9 ? 'high' : 
                          bestMatch.score > 0.8 ? 'medium' : 'low'
        });
      }
    }
  });
}

function findBestMatch(name, documents) {
  let bestMatch = null;
  let bestScore = 0;
  
  documents.forEach(doc => {
    const score = calculateSimilarity(name, doc.candidateName);
    if (score > bestScore) {
      bestScore = score;
      bestMatch = { doc: doc, score: score };
    }
  });
  
  return bestMatch;
}

function calculateSimilarity(str1, str2) {
  const s1 = str1.toLowerCase().replace(/[^a-z0-9]/g, '');
  const s2 = str2.toLowerCase().replace(/[^a-z0-9]/g, '');
  
  if (s1 === s2) return 1;
  
  const longer = s1.length > s2.length ? s1 : s2;
  const shorter = s1.length > s2.length ? s2 : s1;
  
  if (longer.length === 0) return 0;
  
  const editDistance = getEditDistance(longer, shorter);
  return (longer.length - editDistance) / longer.length;
}

function getEditDistance(s1, s2) {
  const costs = [];
  for (let i = 0; i <= s1.length; i++) {
    let lastValue = i;
    for (let j = 0; j <= s2.length; j++) {
      if (i === 0) {
        costs[j] = j;
      } else if (j > 0) {
        let newValue = costs[j - 1];
        if (s1.charAt(i - 1) !== s2.charAt(j - 1)) {
          newValue = Math.min(Math.min(newValue, lastValue), costs[j]) + 1;
        }
        costs[j - 1] = lastValue;
        lastValue = newValue;
      }
    }
    if (i > 0) costs[s2.length] = lastValue;
  }
  return costs[s2.length];
}

// ============================================
// UTILITY FUNCTIONS
// ============================================

function logMessage(message, level, ss) {
  try {
    if (ss) {
      const logSheet = ss.getSheetByName(CONFIG.SHEETS.PROCESSING_LOG);
      if (logSheet) {
        const progress = getProcessingProgress(ss);
        logSheet.appendRow([new Date(), message, level, progress]);
        
        if (logSheet.getLastRow() > 1000) {
          logSheet.deleteRows(2, 100);
        }
      }
    }
  } catch (e) {
    Logger.log(`Failed to log message: ${e}`);
  }
  
  Logger.log(`[${level}] ${message}`);
}

function getProcessingProgress(ss) {
  try {
    const stats = getDetailedProcessingStats(ss);
    if (stats.total === 0) return 'N/A';
    return `${stats.processed}/${stats.total} (${Math.round(stats.processed/stats.total*100)}%)`;
  } catch (e) {
    return 'N/A';
  }
}

function updateCandidateStatus(candidateName, status, ss, errorDetails = '') {
  try {
    const statusSheet = ss.getSheetByName(CONFIG.SHEETS.PROCESSING_STATUS);
    if (!statusSheet) {
      ensureProcessingStatusSheet(ss);
      return;
    }
    
    const lastRow = statusSheet.getLastRow();
    let updated = false;
    
    if (lastRow > 1) {
      const data = statusSheet.getRange(2, 2, lastRow - 1, 1).getValues();
      for (let i = 0; i < data.length; i++) {
        if (data[i][0] === candidateName) {
          statusSheet.getRange(i + 2, 1, 1, 4).setValues([
            [new Date(), candidateName, status, errorDetails]
          ]);
          updated = true;
          break;
        }
      }
    }
    
    if (!updated) {
      statusSheet.appendRow([new Date(), candidateName, status, errorDetails]);
    }
  } catch (e) {
    Logger.log(`Failed to update status for ${candidateName}: ${e}`);
  }
}

function ensureProcessingStatusSheet(ss) {
  let sheet = ss.getSheetByName(CONFIG.SHEETS.PROCESSING_STATUS);
  if (!sheet) {
    setupProcessingStatusSheet(ss);
  }
}

function trackAPIUsage(candidateName, callType, inputTokens, outputTokens, ss) {
  try {
    const usageSheet = ss.getSheetByName(CONFIG.SHEETS.API_USAGE);
    if (!usageSheet) {
      setupAPIUsageSheet(ss);
      return;
    }
    
    const INPUT_COST_PER_MILLION = 3.00;
    const OUTPUT_COST_PER_MILLION = 15.00;
    
    const inputCost = (inputTokens / 1000000) * INPUT_COST_PER_MILLION;
    const outputCost = (outputTokens / 1000000) * OUTPUT_COST_PER_MILLION;
    const totalCost = inputCost + outputCost;
    
    usageSheet.appendRow([
      new Date(),
      candidateName,
      callType,
      inputTokens,
      outputTokens,
      inputTokens + outputTokens,
      inputCost,
      outputCost,
      totalCost
    ]);
  } catch (e) {
    Logger.log(`Failed to track API usage: ${e}`);
  }
}

function estimateTokens(text) {
  // Rough estimation: ~4 characters per token
  return Math.ceil(text.length / 4);
}

function extractTextFromPDF(file) {
  try {
    const blob = file.getBlob();
    const resource = {
      title: blob.getName(),
      mimeType: blob.getContentType()
    };
    
    const options = {
      ocr: true,
      ocrLanguage: "en"
    };
    
    const response = Drive.Files.insert(resource, blob, options);
    const doc = DocumentApp.openById(response.id);
    const text = doc.getBody().getText();
    
    DriveApp.getFileById(response.id).setTrashed(true);
    
    return text;
  } catch (error) {
    Logger.log(`PDF extraction error for ${file.getName()}: ${error}`);
    throw new Error(`Failed to extract text from PDF: ${error.toString()}`);
  }
}

// ============================================
// CONFIGURATION OBJECT
// ============================================

const CONFIG = {
  VERSION: '7.0.0-enhanced',
  MODEL: 'claude-3-5-sonnet-20241022',
  API_URL: 'https://api.anthropic.com/v1/messages',
  SHEETS: {
    PROCESSING_LOG: 'Processing Log',
    PROCESSING_STATUS: 'Processing Status',
    DOCUMENT_MAPPING: 'Document Mapping',
    POSITION_CONFIG: 'Position Configuration',
    POSITION_TEXT: 'Position Description Text',
    DYNAMIC_RUBRIC: 'Dynamic Rubric Configuration',
    EVAL_FRAMEWORK: 'Evaluation Framework',
    COMPANY_METRICS: 'Company Metrics',
    CANDIDATE_EVALS: 'Candidate Evaluations',
    DETAILED_EVALS: 'Detailed Evaluations',
    COMBINED_ANALYSIS: 'Combined Analysis',
    SUMMARY_DASHBOARD: 'Summary Dashboard',
    API_USAGE: 'API Usage Tracking'
  },
  PRICING: {
    INPUT_PER_MILLION: 3.00,
    OUTPUT_PER_MILLION: 15.00
  }
};

// ============================================
// END OF ENHANCED WEB APP SCRIPT
// ============================================