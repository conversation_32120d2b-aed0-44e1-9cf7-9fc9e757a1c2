# RubiRecruit - AI-Powered Recruitment Excel Add-in

RubiRecruit is a sophisticated Microsoft Excel add-in that leverages AI to streamline and enhance the recruitment process. It integrates with Claude AI to automatically evaluate candidates against job requirements, generate dynamic rubrics, and provide comprehensive candidate assessments.

## 🎯 Project Overview

RubiRecruit transforms traditional recruitment workflows by:
- **Automated Candidate Evaluation**: Uses Claude AI to assess CVs and cover letters against position requirements
- **Dynamic Rubric Generation**: Creates customized evaluation criteria based on job descriptions
- **Comprehensive Analytics**: Provides detailed scoring, metrics, and interview questions
- **Excel Integration**: Seamlessly works within Excel for familiar data management
- **Multi-Version Support**: Includes different versions for various deployment scenarios

## 📁 Project Structure

This repository contains three main versions:

### 1. **Main Version** (Root Directory)
- **Purpose**: SSO-enabled version with Microsoft Graph integration
- **Authentication**: Single Sign-On (SSO) with Azure AD
- **Features**: Full Microsoft Graph API access, SharePoint integration
- **Target**: Enterprise deployments with Microsoft 365 integration

### 2. **RubiRecruit v7** 
- **Purpose**: Local file processing version with comprehensive features
- **Authentication**: API key-based (no SSO required)
- **Features**: Local PDF processing, multiple service integrations, advanced analytics
- **Target**: Standalone deployments, development, and testing

### 3. **RubiRecruit - Excel**
- **Purpose**: Simplified Excel-focused version
- **Features**: Basic Excel operations, streamlined UI
- **Target**: Minimal deployment scenarios

## 🚀 Key Features

### AI-Powered Evaluation
- **Claude AI Integration**: Uses Anthropic's Claude for natural language processing
- **PDF Processing**: Extracts and analyzes text from PDF documents
- **Dynamic Scoring**: Generates weighted scores across multiple criteria
- **Personalized Questions**: Creates tailored interview questions for each candidate

### Excel Integration
- **Multiple Worksheets**: Automatically creates and manages specialized sheets
- **Data Persistence**: Stores configuration and results within Excel
- **Real-time Updates**: Live progress tracking and status updates
- **Export Ready**: Results formatted for easy reporting and analysis

### Document Management
- **Multi-format Support**: Handles CVs, cover letters, and position descriptions
- **Automatic Mapping**: Intelligently matches documents to candidates
- **Batch Processing**: Evaluates multiple candidates efficiently
- **Duplicate Detection**: Prevents re-processing of existing candidates

## 🛠️ Technical Architecture

### Frontend (Task Pane)
- **TypeScript**: Type-safe development with modern ES6+ features
- **Office.js**: Microsoft Office JavaScript API integration
- **Webpack**: Module bundling and asset management
- **Fabric UI**: Microsoft's design system for consistent UX

### Backend (Middle Tier)
- **Node.js/Express**: Server-side processing and API endpoints
- **Authentication**: JWT validation and Azure AD integration
- **Microsoft Graph**: Office 365 and SharePoint connectivity
- **CORS Support**: Cross-origin resource sharing for web deployment

### AI Services
- **Claude API**: Anthropic's language model for text analysis
- **PDF Processing**: Text extraction and document parsing
- **Natural Language Processing**: Candidate evaluation and rubric generation

## 📋 Prerequisites

- **Microsoft Excel**: Desktop or web version with add-in support
- **Node.js**: Version 14 or higher
- **Claude API Key**: From Anthropic for AI functionality
- **Microsoft 365**: For SSO versions (optional for local versions)

## 🔧 Installation & Setup

### Quick Start (Local Version)
```bash
# Clone the repository
git clone [repository-url]
cd "RubiRecruit v7"

# Install dependencies
npm install

# Start development server
npm run dev

# In another terminal, start the proxy server
npm run proxy
```

### SSO Version Setup
```bash
# Navigate to main directory
cd RubiRecruit-main

# Install dependencies
npm install

# Configure SSO
npm run configure-sso

# Start development
npm start
```

## 📖 Usage Guide

### 1. Initial Setup
- Configure Claude API key
- Set up Excel worksheets
- Load position description (PDF)

### 2. Rubric Generation
- AI analyzes job requirements
- Creates weighted evaluation criteria
- Stores rubric for consistent evaluation

### 3. Document Processing
- Upload candidate CVs and cover letters
- Automatic document-to-candidate mapping
- Batch processing capabilities

### 4. Candidate Evaluation
- AI evaluates each candidate against rubric
- Generates detailed scores and feedback
- Creates personalized interview questions

### 5. Results Analysis
- Review comprehensive candidate rankings
- Export results for further analysis
- Track API usage and processing metrics

## 📊 Output & Analytics

RubiRecruit generates multiple types of analysis:

- **Candidate Evaluations**: Detailed scoring across all criteria
- **Company Metrics**: Career progression and experience analysis
- **Interview Questions**: Personalized questions based on candidate profile
- **Summary Dashboard**: High-level overview and rankings
- **Processing Logs**: Detailed audit trail of all operations

## 🔒 Security & Privacy

- **API Key Management**: Secure storage within Excel
- **Data Isolation**: All processing contained within user's Excel file
- **No Data Retention**: AI service doesn't store candidate information
- **Audit Logging**: Complete tracking of all operations

## 🤝 Contributing

This project follows Microsoft Office Add-in development best practices. See individual component READMEs for specific development guidelines.

## 📄 License

Licensed under the MIT License. See LICENSE file for details.

## 🆘 Support

For technical support and documentation, refer to the component-specific README files in each directory.

---

## 📚 Component Documentation

- [SSO Authentication System](./docs/SSO-Authentication-README.md)
- [Task Pane Interface](./docs/TaskPane-README.md)
- [Middle Tier Services](./docs/MiddleTier-README.md)
- [AI Integration Services](./docs/AI-Services-README.md)
- [Excel Integration](./docs/Excel-Integration-README.md)
- [Local File Processing](./docs/LocalFile-Processing-README.md)
- [Build and Deployment](./docs/Build-Deployment-README.md)
