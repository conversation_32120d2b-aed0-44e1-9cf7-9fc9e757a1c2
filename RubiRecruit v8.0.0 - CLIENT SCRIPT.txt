// RubiRecruit v7.0.0 - CLIENT SCRIPT
// Dynamic position-based evaluation system with Enhanced UI and Sidebar
// This script runs in the customer's Google Sheet and communicates with the secure web app

// ============================================
// CONFIGURATION - CUSTOMER MUST UPDATE THIS
// ============================================

const WEB_APP_URL = 'https://script.google.com/macros/s/AKfycbx0pkI26jrvmE3uJdP6boqT5ygIUpVvNRbakcFQHz-q1ANaItG6RrIsHaVdd1Z2Wj7H/exec'; // Must end with /exec
const LICENSE_SHEET_ID = '1-lUGLPt_Zn5tjchsru7xE2NyENYoQ-Buq17k_RdsZfw'; // Sheet ID containing license keys
const VERSION = '7.0.0';
const RUBY_COLOR = '#8B1538';

// ============================================
// SIDEBAR HTML CONTENT WITH PREREQUISITES
// ============================================

function getSidebarHtml() {
  return `
<!DOCTYPE html>
<html>
<head>
  <base target="_top">
  <style>
    :root {
      --ruby-color: #8B1538;
      --ruby-gradient-start: #8B1538;
      --ruby-gradient-end: #A91B60;
    }
    
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
      background: linear-gradient(135deg, var(--ruby-gradient-start) 0%, var(--ruby-gradient-end) 100%);
      height: 100vh;
      overflow-y: auto;
    }
    .sidebar-container {
      background: white;
      height: 100%;
      display: flex;
      flex-direction: column;
    }
    .header {
      background: linear-gradient(135deg, var(--ruby-gradient-start) 0%, var(--ruby-gradient-end) 100%);
      color: white;
      padding: 20px 15px;
      text-align: center;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .logo {
      font-size: 32px;
      margin-bottom: 5px;
    }
    .title {
      font-size: 22px;
      font-weight: 600;
      margin: 0;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    .subtitle {
      font-size: 12px;
      margin-top: 5px;
      opacity: 0.95;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    .content {
      flex: 1;
      padding: 20px 15px;
      overflow-y: auto;
    }
    .prerequisites-section {
      background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%);
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 15px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    .prerequisites-title {
      font-size: 14px;
      font-weight: 600;
      color: #2d3436;
      margin-bottom: 12px;
      display: flex;
      align-items: center;
    }
    .prerequisites-title .icon {
      margin-right: 8px;
      font-size: 18px;
    }
    .checklist-item {
      background: white;
      border-radius: 6px;
      padding: 10px;
      margin-bottom: 8px;
      display: flex;
      align-items: center;
      transition: all 0.3s ease;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    .checklist-item:hover {
      transform: translateX(2px);
      box-shadow: 0 2px 6px rgba(0,0,0,0.15);
    }
    .check-icon {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      margin-right: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: bold;
      background: #e0e0e0;
      color: #999;
    }
    .checklist-item.ready .check-icon {
      background: #4CAF50;
      color: white;
    }
    .checklist-text {
      flex: 1;
      font-size: 12px;
      color: #333;
    }
    .checklist-hint {
      font-size: 10px;
      color: #666;
      margin-top: 2px;
    }
    .journey-section {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 15px;
    }
    .journey-title {
      font-size: 14px;
      font-weight: 600;
      color: #333;
      margin-bottom: 12px;
      display: flex;
      align-items: center;
    }
    .journey-title .icon {
      margin-right: 8px;
      font-size: 16px;
    }
    .journey-step {
      background: white;
      border-radius: 6px;
      padding: 12px;
      margin-bottom: 8px;
      border-left: 3px solid #dee2e6;
      transition: all 0.3s ease;
      cursor: pointer;
      position: relative;
    }
    .journey-step:hover {
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      transform: translateX(2px);
    }
    .journey-step.completed {
      border-left-color: #28a745;
      background: #f0fff4;
    }
    .journey-step.current {
      border-left-color: var(--ruby-color);
      background: #fff5f8;
      box-shadow: 0 2px 8px rgba(139, 21, 56, 0.2);
    }
    .journey-step.pending {
      opacity: 0.7;
    }
    .step-header {
      display: flex;
      align-items: center;
      margin-bottom: 6px;
    }
    .step-number {
      background: #dee2e6;
      color: #495057;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 10px;
      font-size: 12px;
      font-weight: bold;
      transition: all 0.3s ease;
    }
    .journey-step.completed .step-number {
      background: #28a745;
      color: white;
    }
    .journey-step.current .step-number {
      background: var(--ruby-color);
      color: white;
      animation: pulse 2s infinite;
    }
    @keyframes pulse {
      0% { box-shadow: 0 0 0 0 rgba(139, 21, 56, 0.7); }
      70% { box-shadow: 0 0 0 10px rgba(139, 21, 56, 0); }
      100% { box-shadow: 0 0 0 0 rgba(139, 21, 56, 0); }
    }
    .step-title {
      font-size: 13px;
      font-weight: 600;
      color: #333;
      flex: 1;
    }
    .step-status {
      font-size: 18px;
    }
    .step-description {
      font-size: 11px;
      color: #666;
      margin-left: 34px;
    }
    .guidance-section {
      background: #fff9e6;
      border: 2px solid #ffc107;
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 15px;
    }
    .guidance-title {
      font-size: 14px;
      font-weight: 600;
      color: #ff6b00;
      margin-bottom: 8px;
      display: flex;
      align-items: center;
    }
    .guidance-title .icon {
      margin-right: 8px;
      font-size: 16px;
    }
    .guidance-text {
      font-size: 12px;
      color: #666;
      line-height: 1.5;
    }
    .guidance-action {
      margin-top: 10px;
      padding: 8px 12px;
      background: linear-gradient(135deg, var(--ruby-gradient-start) 0%, var(--ruby-gradient-end) 100%);
      color: white;
      border: none;
      border-radius: 6px;
      font-size: 12px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    .guidance-action:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(139, 21, 56, 0.3);
    }
    .stats-section {
      background: #e8f5e9;
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 15px;
    }
    .stats-title {
      font-size: 14px;
      font-weight: 600;
      color: #2e7d32;
      margin-bottom: 10px;
    }
    .stat-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 6px;
      font-size: 12px;
    }
    .stat-label {
      color: #555;
    }
    .stat-value {
      font-weight: 600;
      color: #2e7d32;
    }
    .footer {
      padding: 15px;
      background: #f8f9fa;
      border-top: 1px solid #dee2e6;
      text-align: center;
      font-size: 11px;
      color: #666;
    }
    .refresh-btn {
      margin-top: 10px;
      padding: 6px 12px;
      background: #e9ecef;
      border: none;
      border-radius: 4px;
      font-size: 11px;
      cursor: pointer;
      transition: all 0.2s ease;
    }
    .refresh-btn:hover {
      background: #dee2e6;
    }
  </style>
</head>
<body>
  <div class="sidebar-container">
    <div class="header">
      <div class="logo">💎</div>
      <h1 class="title">RubiRecruit™</h1>
      <div class="subtitle">AI-Powered Recruitment Excellence</div>
    </div>
    
    <div class="content">
      <div class="prerequisites-section">
        <div class="prerequisites-title">
          <span class="icon">📋</span>
          Prerequisites Checklist
        </div>
        
        <div class="checklist-item" id="prereq-api">
          <div class="check-icon">✔</div>
          <div>
            <div class="checklist-text">Claude API Key</div>
            <div class="checklist-hint">Get from console.anthropic.com</div>
          </div>
        </div>
        
        <div class="checklist-item" id="prereq-pd">
          <div class="check-icon">✔</div>
          <div>
            <div class="checklist-text">Position Description PDF</div>
            <div class="checklist-hint">Upload to a Google Drive folder</div>
          </div>
        </div>
        
        <div class="checklist-item" id="prereq-cvs">
          <div class="check-icon">✔</div>
          <div>
            <div class="checklist-text">CVs in PDF Format</div>
            <div class="checklist-hint">All in another Google Drive folder</div>
          </div>
        </div>
        
        <div class="checklist-item" id="prereq-covers">
          <div class="check-icon">✔</div>
          <div>
            <div class="checklist-text">Cover Letters (Optional)</div>
            <div class="checklist-hint">Same folder as CVs</div>
          </div>
        </div>
        
        <div class="checklist-item" id="prereq-folders">
          <div class="check-icon">✔</div>
          <div>
            <div class="checklist-text">Google Drive Folder IDs</div>
            <div class="checklist-hint">Copy from Drive URL after /folders/</div>
          </div>
        </div>
      </div>
    
      <div class="guidance-section" id="guidanceSection">
        <div class="guidance-title">
          <span class="icon">💡</span>
          Next Step
        </div>
        <div class="guidance-text" id="guidanceText">
          Loading status...
        </div>
        <button class="guidance-action" id="guidanceAction" onclick="executeNextStep()">
          Get Started
        </button>
      </div>
      
      <div class="journey-section">
        <div class="journey-title">
          <span class="icon">🗺️</span>
          Your Journey Progress
        </div>
        
        <div class="journey-step" id="step1" onclick="executeStep(1)">
          <div class="step-header">
            <div class="step-number">1</div>
            <div class="step-title">Initial Setup</div>
            <div class="step-status" id="status1"></div>
          </div>
          <div class="step-description">Configure API key and folders</div>
        </div>
        
        <div class="journey-step" id="step2" onclick="executeStep(2)">
          <div class="step-header">
            <div class="step-number">2</div>
            <div class="step-title">Create all Sheets</div>
            <div class="step-status" id="status2"></div>
          </div>
          <div class="step-description">Setup evaluation worksheets</div>
        </div>
        
        <div class="journey-step" id="step3" onclick="executeStep(3)">
          <div class="step-header">
            <div class="step-number">3</div>
            <div class="step-title">Load Position</div>
            <div class="step-status" id="status3"></div>
          </div>
          <div class="step-description">Select position description PDF</div>
        </div>
        
        <div class="journey-step" id="step4" onclick="executeStep(4)">
          <div class="step-header">
            <div class="step-number">4</div>
            <div class="step-title">Document Mapping</div>
            <div class="step-status" id="status4"></div>
          </div>
          <div class="step-description">Map CVs and cover letters</div>
        </div>
        
        <div class="journey-step" id="step5" onclick="executeStep(5)">
          <div class="step-header">
            <div class="step-number">5</div>
            <div class="step-title">Generate Rubric</div>
            <div class="step-status" id="status5"></div>
          </div>
          <div class="step-description">AI creates evaluation criteria</div>
        </div>
        
        <div class="journey-step" id="step6" onclick="executeStep(6)">
          <div class="step-header">
            <div class="step-number">6</div>
            <div class="step-title">Lock Rubric</div>
            <div class="step-status" id="status6"></div>
          </div>
          <div class="step-description">Finalize evaluation framework</div>
        </div>
        
        <div class="journey-step" id="step7" onclick="executeStep(7)">
          <div class="step-header">
            <div class="step-number">7</div>
            <div class="step-title">Process Candidates</div>
            <div class="step-status" id="status7"></div>
          </div>
          <div class="step-description">Evaluate applications</div>
        </div>
        
        <div class="journey-step" id="step8" onclick="executeStep(8)">
          <div class="step-header">
            <div class="step-number">8</div>
            <div class="step-title">Review Results</div>
            <div class="step-status" id="status8"></div>
          </div>
          <div class="step-description">Analyze evaluations</div>
        </div>
      </div>
      
      <div class="stats-section" id="statsSection" style="display: none;">
        <div class="stats-title">📊 Current Statistics</div>
        <div class="stat-item">
          <span class="stat-label">Position:</span>
          <span class="stat-value" id="statPosition">-</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">Total Candidates:</span>
          <span class="stat-value" id="statTotal">0</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">Processed:</span>
          <span class="stat-value" id="statProcessed">0</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">Remaining:</span>
          <span class="stat-value" id="statRemaining">0</span>
        </div>
      </div>
    </div>
    
    <div class="footer">
      RubiRecruit v7.0.0
      <br>
      <button class="refresh-btn" onclick="refreshStatus()">🔄 Refresh Status</button>
    </div>
  </div>
  
  <script>
    let currentJourneyStatus = {};
    
    function updateJourneyStatus(status) {
      currentJourneyStatus = status;
      
      // Update prerequisites checklist
      if (status.prerequisites) {
        document.getElementById('prereq-api').className = 
          status.prerequisites.apiKey ? 'checklist-item ready' : 'checklist-item';
        document.getElementById('prereq-pd').className = 
          status.prerequisites.positionFolder ? 'checklist-item ready' : 'checklist-item';
        document.getElementById('prereq-cvs').className = 
          status.prerequisites.cvFolder ? 'checklist-item ready' : 'checklist-item';
        document.getElementById('prereq-covers').className = 
          'checklist-item ready'; // Always ready as optional
        document.getElementById('prereq-folders').className = 
          status.prerequisites.foldersConfigured ? 'checklist-item ready' : 'checklist-item';
      }
      
      // Update step statuses (now 8 steps)
      for (let i = 1; i <= 8; i++) {
        const stepEl = document.getElementById('step' + i);
        const statusEl = document.getElementById('status' + i);
        
        if (stepEl && statusEl) {
          stepEl.className = 'journey-step';
          
          if (status['step' + i] === 'completed') {
            stepEl.classList.add('completed');
            statusEl.textContent = '✅';
          } else if (status['step' + i] === 'current') {
            stepEl.classList.add('current');
            statusEl.textContent = '▶️';
          } else {
            stepEl.classList.add('pending');
            statusEl.textContent = '';
          }
        }
      }
      
      // Update guidance
      document.getElementById('guidanceText').innerHTML = status.guidance || 'Ready to begin';
      document.getElementById('guidanceAction').textContent = status.actionText || 'Continue';
      
      // Update stats if available
      if (status.stats) {
        document.getElementById('statsSection').style.display = 'block';
        document.getElementById('statPosition').textContent = status.stats.position || '-';
        document.getElementById('statTotal').textContent = status.stats.total || '0';
        document.getElementById('statProcessed').textContent = status.stats.processed || '0';
        document.getElementById('statRemaining').textContent = status.stats.remaining || '0';
      }
    }
    
    function executeStep(stepNumber) {
      google.script.run
        .withSuccessHandler(refreshStatus)
        .executeJourneyStep(stepNumber);
    }
    
    function executeNextStep() {
      google.script.run
        .withSuccessHandler(refreshStatus)
        .executeNextJourneyStep();
    }
    
    function refreshStatus() {
      google.script.run
        .withSuccessHandler(updateJourneyStatus)
        .getJourneyStatus();
    }
    
    // Initial load
    window.onload = function() {
      refreshStatus();
      // Auto-refresh every 10 seconds
      setInterval(refreshStatus, 10000);
    };
  </script>
</body>
</html>
`;
}

// ============================================
// SIDEBAR MANAGEMENT WITH PREREQUISITES
// ============================================

function showSidebar() {
  const htmlOutput = HtmlService.createHtmlOutput(getSidebarHtml())
    .setTitle('RubiRecruit Journey')
    .setWidth(300);
  
  SpreadsheetApp.getUi().showSidebar(htmlOutput);
}

function getJourneyStatus() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const props = PropertiesService.getDocumentProperties();
  
  const status = {
    step1: 'pending',
    step2: 'pending',
    step3: 'pending',
    step4: 'pending',
    step5: 'pending',
    step6: 'pending',
    step7: 'pending',
    step8: 'pending',
    guidance: '',
    actionText: 'Continue',
    stats: {},
    prerequisites: {
      apiKey: false,
      positionFolder: false,
      cvFolder: false,
      foldersConfigured: false
    }
  };
  
  // Check prerequisites
  const apiKey = props.getProperty('CLAUDE_API_KEY');
  const cvFolderId = props.getProperty('CV_FOLDER_ID');
  const positionFolderId = props.getProperty('POSITION_FOLDER_ID');
  
  status.prerequisites.apiKey = !!apiKey;
  status.prerequisites.cvFolder = !!cvFolderId;
  status.prerequisites.positionFolder = !!positionFolderId;
  status.prerequisites.foldersConfigured = !!(cvFolderId && positionFolderId);
  
  // Check Step 1: Initial Setup
  if (apiKey && cvFolderId) {
    status.step1 = 'completed';
    
    // Check Step 2: Create all Sheets
    const allSheetsCreated = checkIfAllSheetsExist(ss);
    if (allSheetsCreated) {
      status.step2 = 'completed';
      
      // Check Step 3: Load Position
      const configSheet = ss.getSheetByName('Position Configuration');
      let positionLoaded = false;
      let rubricStatus = '';
      let positionTitle = '';
      
      if (configSheet && configSheet.getLastRow() > 1) {
        for (let i = 2; i <= configSheet.getLastRow(); i++) {
          const field = configSheet.getRange(i, 1).getValue();
          if (field === 'Position Title') {
            positionTitle = configSheet.getRange(i, 2).getValue();
            positionLoaded = true;
          } else if (field === 'Rubric Status') {
            rubricStatus = configSheet.getRange(i, 2).getValue();
          }
        }
      }
      
      if (positionLoaded) {
        status.step3 = 'completed';
        status.stats.position = positionTitle;
        
        // Check Step 4: Document Mapping
        const mappingSheet = ss.getSheetByName('Document Mapping');
        if (mappingSheet && mappingSheet.getLastRow() > 1) {
          status.step4 = 'completed';
          
          // Check Step 5: Generate Rubric
          if (rubricStatus === 'Generated - Not Locked' || rubricStatus === 'LOCKED') {
            status.step5 = 'completed';
            
            // Check Step 6: Lock Rubric
            if (rubricStatus === 'LOCKED') {
              status.step6 = 'completed';
              
              // Check Step 7: Process Candidates
              const evalSheet = ss.getSheetByName('Candidate Evaluations');
              const processed = evalSheet ? Math.max(0, evalSheet.getLastRow() - 1) : 0;
              const total = mappingSheet ? Math.max(0, mappingSheet.getLastRow() - 1) : 0;
              
              status.stats.total = total;
              status.stats.processed = processed;
              status.stats.remaining = Math.max(0, total - processed);
              
              if (processed > 0) {
                if (processed >= total) {
                  status.step7 = 'completed';
                  status.step8 = 'current';
                  status.guidance = 'All candidates processed! Review your results in the evaluation sheets.';
                  status.actionText = 'View Dashboard';
                } else {
                  status.step7 = 'current';
                  status.guidance = `Processing candidates: ${processed} of ${total} completed. Continue processing the remaining ${total - processed} candidates.`;
                  status.actionText = 'Process Next Batch';
                }
              } else {
                status.step7 = 'current';
                status.guidance = 'Ready to process candidates. Start with a small batch to verify everything is working.';
                status.actionText = 'Process Next 5';
              }
            } else {
              status.step6 = 'current';
              status.guidance = 'Rubric has been generated. You can edit it in the Dynamic Rubric Configuration sheet, then lock it for evaluation.';
              status.actionText = 'Lock Rubric';
            }
          } else {
            status.step5 = 'current';
            status.guidance = 'Documents mapped successfully. Generate an AI rubric based on the position requirements.';
            status.actionText = 'Generate Rubric';
          }
        } else {
          status.step4 = 'current';
          status.guidance = 'Position loaded. Create document mapping to organize CVs and cover letters.';
          status.actionText = 'Create Mapping';
        }
      } else {
        status.step3 = 'current';
        status.guidance = 'Sheets created successfully. Load a position description to define evaluation criteria.';
        status.actionText = 'Load Position';
      }
    } else {
      status.step2 = 'current';
      status.guidance = 'Initial setup complete. Create all evaluation sheets to organize your data.';
      status.actionText = 'Create Sheets';
    }
  } else {
    status.step1 = 'current';
    status.guidance = 'Welcome to RubiRecruit! Check the prerequisites above, then run Initial Setup to configure your API key and folders.';
    status.actionText = 'Run Setup';
  }
  
  return status;
}

// Helper function to check if all sheets exist
function checkIfAllSheetsExist(ss) {
  const requiredSheets = [
    'Processing Log',
    'Processing Status',
    'Document Mapping',
    'Position Configuration',
    'Position Description Text',
    'Dynamic Rubric Configuration',
    'Evaluation Framework',
    'Company Metrics',
    'Candidate Evaluations',
    'Detailed Evaluations',
    'Combined Analysis',
    'Summary Dashboard',
    'API Usage Tracking'
  ];
  
  for (const sheetName of requiredSheets) {
    if (!ss.getSheetByName(sheetName)) {
      return false;
    }
  }
  return true;
}

function executeNextJourneyStep() {
  const status = getJourneyStatus();
  
  // Find current step and execute it (now checking up to 8)
  for (let i = 1; i <= 8; i++) {
    if (status['step' + i] === 'current') {
      executeJourneyStep(i);
      break;
    }
  }
}

function executeJourneyStep(stepNumber) {
  switch(stepNumber) {
    case 1:
      runInitialSetup();
      break;
    case 2:
      setupAllSheets();
      break;
    case 3:
      showAdvancedPositionSelector();
      break;
    case 4:
      createDocumentMapping();
      break;
    case 5:
      generateDynamicRubric();
      break;
    case 6:
      lockRubric();
      break;
    case 7:
      processNext5();
      break;
    case 8:
      updateDashboard();
      break;
  }
}

// ============================================
// LICENSE VALIDATION AND MENU SETUP
// ============================================

function onOpen() {
  const ui = SpreadsheetApp.getUi();
  
  try {
    // Check if we have a stored license
    const props = PropertiesService.getDocumentProperties();
    const storedLicense = props.getProperty('LICENSE_KEY');
    
    if (!storedLicense) {
      // No license - show limited menu and prompt
      ui.createMenu("⚡⚡  RubiRecruit™  ⚡⚡")
        .addItem('🔑 Enter License Key', 'promptForLicense')
        .addItem('🔧 Contact Support', 'showSupportInfo')
        .addToUi();
      
      // Prompt for license after a short delay
      Utilities.sleep(500);
      promptForLicense();
      return;
    }
    
    // We have a license - show full menu
    ui.createMenu("⚡⚡  RubiRecruit™  ⚡⚡")
      .addSeparator()
      .addItem('🌟 Welcome + Tc & Cs', 'showWelcomeScreen')
      .addItem('🎯 Show Journey Sidebar', 'showSidebar')
      .addSeparator()
      .addSubMenu(ui.createMenu('1️⃣ Initial Setup')
        .addItem('🔑 Configure API & Folders', 'runInitialSetup')
        .addItem('✅ Check Setup Status', 'checkSetupStatus'))
      .addSubMenu(ui.createMenu('2️⃣ Create All Sheets')
        .addItem('📊 Setup All Sheets', 'setupAllSheets')
        .addItem('🔧 Verify Sheet Creation', 'verifyAllSheets'))
      .addSubMenu(ui.createMenu('3️⃣ Load Position')
        .addItem('📄 Select Position Description', 'showAdvancedPositionSelector')
        .addItem('👁️ View Current Position', 'viewCurrentPosition'))
      .addSubMenu(ui.createMenu('4️⃣ Document Mapping')
        .addItem('🔗 Create Document Mapping', 'createDocumentMapping')
        .addItem('📊 View Mapping Summary', 'viewMappingSummary'))
      .addSubMenu(ui.createMenu('5️⃣ Generate Rubric')
        .addItem('🤖 Generate AI Rubric', 'generateDynamicRubric')
        .addItem('👁️ View Current Rubric', 'viewCurrentRubric'))
      .addSubMenu(ui.createMenu('6️⃣ Lock Rubric')
        .addItem('🔒 Lock Rubric for Use', 'lockRubric')
        .addItem('🔓 Unlock for Editing', 'unlockRubric')
        .addItem('✏️ Edit Rubric', 'editRubric')
        .addItem('💾 Save Rubric Changes', 'saveRubricChanges'))
      .addSubMenu(ui.createMenu('7️⃣ Process Candidates')
        .addItem('▶️ Process Next 5', 'processNext5')
        .addItem('⏩ Process Next 10', 'processNext10')
        .addItem('🎯 Process Specific', 'processSpecificCandidates')
        .addItem('🔄 Auto-Process All', 'processAllRemainingAuto')
        .addItem('🔁 Retry Failed', 'retryFailedCandidates'))
      .addSubMenu(ui.createMenu('8️⃣ Review Results')
        .addItem('📊 Processing Status', 'checkProcessingStatus')
        .addItem('🎯 Update Dashboard', 'updateDashboard')
        .addItem('📑 Generate Report', 'generateSummaryReport')
        .addItem('📈 View Analytics', 'viewAnalytics')
        .addItem('⏳ View Unprocessed', 'viewUnprocessedCandidates')
        .addItem('❌ View Failed', 'viewFailedCandidates'))
      .addSeparator()
      .addSubMenu(ui.createMenu('9️⃣ Tools & Testing')
        .addItem('📌 Test Connection', 'testConnection')
        .addItem('🧪 Test Single Candidate', 'testSingleCandidate')
        .addItem('✓ Validate API Key', 'validateApiKey')
        .addItem('🔍 Check Permissions', 'checkPermissions')
        .addItem('📜 View Processing Log', 'viewProcessingLog')
        .addItem('💰 API Usage & Costs', 'viewAPIUsage'))
      .addSubMenu(ui.createMenu('🔟 Administration')
        .addItem('🔑 Check License', 'checkLicenseStatus')
        .addItem('🔧 Re-enter License', 'promptForLicense')
        .addItem('🗑️ Clear License', 'clearLicenseData')
        .addItem('🗑️ Clear All Data', 'clearAllData')
        .addItem('📖 User Guide', 'showUserGuide')
        .addItem('⚖️ Terms & Conditions', 'showTermsAndConditions')
        .addItem('ℹ️ About RubiRecruit', 'showAbout'))
      .addToUi();
    
    // Try to show sidebar and initialize
    try {
      Utilities.sleep(500);
      showSidebar();
      checkFirstTimeUser();
      formatAllSheets();
    } catch (e) {
      console.log('Error in initialization:', e);
    }
    
  } catch (error) {
    console.error('Critical error in onOpen:', error);
    // Emergency menu if everything fails
    ui.createMenu("⚡ RubiRecruit™ - ERROR")
      .addItem('🔧 Reset Everything', 'emergencyReset')
      .addItem('🔑 Enter License', 'promptForLicense')
      .addItem('🔧 Contact Support', 'showSupportInfo')
      .addToUi();
  }
}

// Simple license prompt function
function promptForLicense() {
  const ui = SpreadsheetApp.getUi();
  
  const response = ui.prompt(
    '🔑 License Key Required',
    'Enter your RubiRecruit license key:\n\n' +
    '(Use TEST-LICENSE-2025 for testing)',
    ui.ButtonSet.OK_CANCEL
  );
  
  if (response.getSelectedButton() === ui.Button.OK) {
    const licenseKey = response.getResponseText().trim();
    
    if (licenseKey) {
      // Try to validate the license
      if (validateEnteredLicense(licenseKey)) {
        ui.alert('Success', 'License validated! Please refresh the page to see the full menu.', ui.ButtonSet.OK);
        
        // Force refresh
        const htmlOutput = HtmlService.createHtmlOutput(
          '<script>window.top.location.reload();</script>'
        ).setWidth(1).setHeight(1);
        ui.showModalDialog(htmlOutput, 'Refreshing...');
      } else {
        ui.alert('Invalid License', 'This license key is invalid or expired.', ui.ButtonSet.OK);
      }
    }
  }
}

// Validate entered license
function validateEnteredLicense(licenseKey) {
  try {
    // Try to access the license sheet
    const licenseSheet = SpreadsheetApp.openById(LICENSE_SHEET_ID);
    const data = licenseSheet.getDataRange().getValues();
    
    // Find the license in the sheet
    for (let i = 1; i < data.length; i++) {
      if (data[i][0] === licenseKey) {  // License Key column
        const expiryDate = new Date(data[i][2]);  // Expiration Date column
        const status = data[i][3];  // Status column
        const now = new Date();
        
        if (status === 'Active' && expiryDate > now) {
          // Save the license
          PropertiesService.getDocumentProperties().setProperties({
            'LICENSE_KEY': licenseKey,
            'LICENSE_CUSTOMER': data[i][1],  // Customer Name column
            'LICENSE_EXPIRY': expiryDate.toISOString()
          });
          return true;
        }
      }
    }
    
    return false;
    
  } catch (error) {
    console.log('Error validating license:', error);
    // For testing, accept TEST-LICENSE-2025
    if (licenseKey === 'TEST-LICENSE-2025') {
      PropertiesService.getDocumentProperties().setProperties({
        'LICENSE_KEY': licenseKey,
        'LICENSE_CUSTOMER': 'Test Customer',
        'LICENSE_EXPIRY': '2025-12-31'
      });
      return true;
    }
    return false;
  }
}

// Clear license data
function clearLicenseData() {
  const ui = SpreadsheetApp.getUi();
  const response = ui.alert(
    'Clear License Data',
    'This will remove your stored license. Continue?',
    ui.ButtonSet.YES_NO
  );
  
  if (response === ui.Button.YES) {
    const props = PropertiesService.getDocumentProperties();
    props.deleteProperty('LICENSE_KEY');
    props.deleteProperty('LICENSE_CUSTOMER');
    props.deleteProperty('LICENSE_EXPIRY');
    
    ui.alert('Cleared', 'License data cleared. Please refresh the page.', ui.ButtonSet.OK);
    
    // Force refresh
    const htmlOutput = HtmlService.createHtmlOutput(
      '<script>window.top.location.reload();</script>'
    ).setWidth(1).setHeight(1);
    ui.showModalDialog(htmlOutput, 'Refreshing...');
  }
}

// Emergency reset
function emergencyReset() {
  clearLicenseData();
}

// Show support info
function showSupportInfo() {
  SpreadsheetApp.getUi().alert(
    '🔧 Support Information',
    'For license inquiries or support:\n\n' +
    'Email: <EMAIL>\n\n' +
    'License Sheet ID:\n' + LICENSE_SHEET_ID,
    SpreadsheetApp.getUi().ButtonSet.OK
  );
}

// Check license status
function checkLicenseStatus() {
  const ui = SpreadsheetApp.getUi();
  const props = PropertiesService.getDocumentProperties();
  
  const licenseKey = props.getProperty('LICENSE_KEY');
  const licenseExpiry = props.getProperty('LICENSE_EXPIRY');
  const licenseCustomer = props.getProperty('LICENSE_CUSTOMER');
  
  if (!licenseKey) {
    ui.alert(
      '🔑 No License',
      'No license key found.',
      ui.ButtonSet.OK
    );
  } else {
    const expiryDate = new Date(licenseExpiry);
    const now = new Date();
    const daysRemaining = Math.ceil((expiryDate - now) / (1000 * 60 * 60 * 24));
    
    let status = '✅ Active';
    if (daysRemaining < 0) {
      status = '❌ Expired';
    } else if (daysRemaining <= 30) {
      status = '⚠️ Expiring Soon';
    }
    
    ui.alert(
      '🔑 License Status',
      `Customer: ${licenseCustomer || 'Unknown'}\n` +
      `License Key: ${licenseKey}\n` +
      `Status: ${status}\n` +
      `Expires: ${expiryDate.toLocaleDateString()}\n` +
      `Days Remaining: ${daysRemaining}\n\n` +
      `If you're having issues, try "Clear License" and re-enter it.`,
      ui.ButtonSet.OK
    );
  }
}

// ============================================
// RUBRIC EDITING FUNCTIONS
// ============================================

function editRubric() {
  const ui = SpreadsheetApp.getUi();
  const config = getConfiguration();
  
  if (!config) {
    ui.alert('Setup Required', 'Please run Initial Setup first', ui.ButtonSet.OK);
    return;
  }
  
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const configSheet = ss.getSheetByName('Position Configuration');
  
  // Check if rubric is locked
  let isLocked = false;
  if (configSheet) {
    for (let i = 2; i <= configSheet.getLastRow(); i++) {
      if (configSheet.getRange(i, 1).getValue() === 'Rubric Status' && 
          configSheet.getRange(i, 2).getValue() === 'LOCKED') {
        isLocked = true;
        break;
      }
    }
  }
  
  if (isLocked) {
    const response = ui.alert(
      '🔓 Unlock Rubric for Editing',
      'The rubric is currently locked.\n\n' +
      'Do you want to unlock it for editing?\n\n' +
      '⚠️ Warning: Any candidates already processed will not be affected by changes.',
      ui.ButtonSet.YES_NO
    );
    
    if (response === ui.Button.YES) {
      unlockRubric();
    }
    return;
  }
  
  const rubricSheet = ss.getSheetByName('Dynamic Rubric Configuration');
  if (!rubricSheet || rubricSheet.getLastRow() <= 1) {
    ui.alert(
      'No Rubric',
      'No rubric has been generated yet.\n\n' +
      'Please generate a rubric first.',
      ui.ButtonSet.OK
    );
    return;
  }
  
  ui.alert(
    '✏️ Edit Rubric',
    'You can now edit the rubric in the "Dynamic Rubric Configuration" sheet.\n\n' +
      
      'EDITING RULES:\n' +
      '• Keep exactly 8 categories\n' +
      '• Category weights must sum to 100%\n' +
      '• Each category must have exactly 5 attributes\n' +
      '• Attribute weights within a category must sum to the category weight\n' +
      '• Category weights should be between 5% and 20%\n\n' +
      
      'When done, use:\n' +
      'Position & Rubric → Save Rubric Changes',
      ui.ButtonSet.OK
  );
  
  // Activate the rubric sheet for editing
  rubricSheet.activate();
}

function unlockRubric() {
  const ui = SpreadsheetApp.getUi();
  const config = getConfiguration();
  
  if (!config) {
    ui.alert('Setup Required', 'Please run Initial Setup first', ui.ButtonSet.OK);
    return;
  }
  
  SpreadsheetApp.getActiveSpreadsheet().toast('Unlocking rubric...', 'Processing', -1);
  
  const result = callWebApp('unlockRubric', {
    ...config,
    spreadsheetId: SpreadsheetApp.getActiveSpreadsheet().getId()
  });
  
  SpreadsheetApp.getActiveSpreadsheet().toast('', 'Complete', 1);
  
  if (result.success) {
    ui.alert(
      'Rubric Unlocked',
      '✓ Rubric has been unlocked for editing.\n\n' +
      'You can now make changes in the "Dynamic Rubric Configuration" sheet.\n\n' +
      'Remember to save changes when done.',
      ui.ButtonSet.OK
    );
    
    // Activate the rubric sheet
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const rubricSheet = ss.getSheetByName('Dynamic Rubric Configuration');
    if (rubricSheet) {
      rubricSheet.activate();
    }
    
    // Refresh sidebar
    showSidebar();
  } else {
    ui.alert('Error', result.error || 'Failed to unlock rubric', ui.ButtonSet.OK);
  }
}

function saveRubricChanges() {
  const ui = SpreadsheetApp.getUi();
  const config = getConfiguration();
  
  if (!config) {
    ui.alert('Setup Required', 'Please run Initial Setup first', ui.ButtonSet.OK);
    return;
  }
  
  const response = ui.alert(
    '💾 Save Rubric Changes',
    'This will validate and save your rubric changes.\n\n' +
    'The system will check:\n' +
    '• Exactly 8 categories\n' +
    '• Weights sum to 100%\n' +
    '• Each category has 5 attributes\n' +
    '• All weight ranges are valid\n\n' +
    'Continue?',
    ui.ButtonSet.YES_NO
  );
  
  if (response !== ui.Button.YES) return;
  
  SpreadsheetApp.getActiveSpreadsheet().toast('Validating and saving rubric...', 'Processing', -1);
  
  const result = callWebApp('updateRubricFromSheet', {
    ...config,
    spreadsheetId: SpreadsheetApp.getActiveSpreadsheet().getId()
  });
  
  SpreadsheetApp.getActiveSpreadsheet().toast('', 'Complete', 1);
  
  if (result.success) {
    ui.alert(
      'Changes Saved',
      '✓ Rubric changes have been validated and saved!\n\n' +
      `Categories: ${result.validation.categoryCount}\n` +
      `Total Weight: ${result.validation.totalWeight.toFixed(1)}%\n\n` +
      'The rubric is ready to be locked for evaluation.',
      ui.ButtonSet.OK
    );
    
    // Refresh sidebar
    showSidebar();
  } else {
    ui.alert(
      'Validation Failed',
      '❌ Rubric validation failed:\n\n' +
      result.error + '\n\n' +
      'Please correct the issues and try again.',
      ui.ButtonSet.OK
    );
  }
}

// ============================================
// DUPLICATE DETECTION IN PROCESSING
// ============================================

function processSpecificCandidates() {
  checkRubricBeforeProcessing(() => {
    const ui = SpreadsheetApp.getUi();
    
    const response = ui.prompt(
      'Process Specific Candidates',
      'Enter candidate names separated by commas:\n' +
      '(e.g., John Smith, Jane Doe, Bob Johnson)\n\n' +
      'Names will be matched against Document Mapping.',
      ui.ButtonSet.OK_CANCEL
    );
    
    if (response.getSelectedButton() === ui.Button.OK) {
      const candidateNames = response.getResponseText()
        .split(',')
        .map(name => name.trim())
        .filter(name => name.length > 0);
      
      if (candidateNames.length > 0) {
        checkForDuplicatesAndProcess(candidateNames);
      } else {
        ui.alert('Error', 'No valid candidate names provided.', ui.ButtonSet.OK);
      }
    }
  });
}

function checkForDuplicatesAndProcess(candidateNames) {
  const ui = SpreadsheetApp.getUi();
  const config = getConfiguration();
  
  if (!config) {
    ui.alert('Setup Required', 'Please run Initial Setup first', ui.ButtonSet.OK);
    return;
  }
  
  // Check for duplicates
  SpreadsheetApp.getActiveSpreadsheet().toast('Checking for duplicates...', 'Processing', -1);
  
  const duplicateCheck = callWebApp('checkForDuplicates', {
    ...config,
    spreadsheetId: SpreadsheetApp.getActiveSpreadsheet().getId(),
    candidateNames: candidateNames
  });
  
  SpreadsheetApp.getActiveSpreadsheet().toast('', 'Complete', 1);
  
  if (duplicateCheck.success && duplicateCheck.hasDuplicates) {
    const response = ui.alert(
      '⚠️ Duplicates Found',
      `The following candidates have already been processed:\n\n` +
      duplicateCheck.duplicates.join('\n') +
      `\n\nNew candidates to process:\n` +
      duplicateCheck.newCandidates.join('\n') +
      `\n\nDo you want to:\n` +
      `• YES: Process only new candidates\n` +
      `• NO: Process all (including duplicates)`,
      ui.ButtonSet.YES_NO_CANCEL
    );
    
    if (response === ui.Button.CANCEL) return;
    
    const skipDuplicates = response === ui.Button.YES;
    processCandidateList(candidateNames, skipDuplicates);
  } else {
    processCandidateList(candidateNames, true);
  }
}

function processCandidateList(candidateNames, skipDuplicates = true) {
  const ui = SpreadsheetApp.getUi();
  const config = getConfiguration();
  
  if (!config) {
    ui.alert('Setup Required', 'Please run Initial Setup first', ui.ButtonSet.OK);
    return;
  }
  
  updateStatusSheet(`Starting batch: ${candidateNames.length} candidates`);
  
  SpreadsheetApp.getActiveSpreadsheet().toast(
    `Processing ${candidateNames.length} candidates...`,
    'RubiRecruit',
    -1
  );
  
  const result = callWebApp('processSpecificCandidates', {
    ...config,
    spreadsheetId: SpreadsheetApp.getActiveSpreadsheet().getId(),
    candidateNames: candidateNames,
    skipDuplicates: skipDuplicates
  });
  
  SpreadsheetApp.getActiveSpreadsheet().toast('', 'Complete', 1);
  
  if (result.success) {
    let message = result.message + '\n\n';
    
    if (result.stats) {
      message += `✅ Requested: ${result.stats.requested}\n`;
      message += `🔍 Found: ${result.stats.found}\n`;
      message += `✅ Successful: ${result.stats.successful}\n`;
      message += `⭐️ Skipped (duplicates): ${result.stats.skipped}\n`;
      message += `❌ Failed: ${result.stats.failed}\n`;
      
      if (result.stats.skippedDuplicates && result.stats.skippedDuplicates.length > 0) {
        message += '\nSkipped duplicates:\n';
        result.stats.skippedDuplicates.forEach(name => {
          message += `• ${name}\n`;
        });
      }
      
      if (result.stats.errors && result.stats.errors.length > 0) {
        message += '\nFailed candidates:\n';
        result.stats.errors.forEach(e => {
          message += `• ${e.candidate}: ${e.error}\n`;
        });
      }
    }
    
    ui.alert('Batch Complete', message, ui.ButtonSet.OK);
    updateStatusSheet(`Batch complete: ${result.stats.successful} successful, ${result.stats.failed} failed, ${result.stats.skipped} skipped`);
    
    // Refresh sidebar
    showSidebar();
    
  } else {
    ui.alert('Error', result.error || result.message, ui.ButtonSet.OK);
    updateStatusSheet('Batch failed: ' + (result.error || 'Unknown error'));
  }
}

function testSingleCandidate() {
  checkRubricBeforeProcessing(() => {
    const ui = SpreadsheetApp.getUi();
    const config = getConfiguration();
    
    if (!config) {
      ui.alert('Setup Required', 'Please run Initial Setup first', ui.ButtonSet.OK);
      return;
    }
    
    const response = ui.prompt(
      'Test Single Candidate',
      'Enter candidate name (or part of name):\n\n' +
      'This will search the Document Mapping for matches.',
      ui.ButtonSet.OK_CANCEL
    );
    
    if (response.getSelectedButton() !== ui.Button.OK) return;
    
    const candidateName = response.getResponseText().trim();
    if (!candidateName) return;
    
    // Check if already processed
    const duplicateCheck = callWebApp('checkForDuplicates', {
      ...config,
      spreadsheetId: SpreadsheetApp.getActiveSpreadsheet().getId(),
      candidateNames: [candidateName]
    });
    
    let forceReprocess = false;
    if (duplicateCheck.success && duplicateCheck.hasDuplicates) {
      const reprocessResponse = ui.alert(
        'Already Processed',
        `${candidateName} has already been evaluated.\n\n` +
        'Do you want to reprocess this candidate?',
        ui.ButtonSet.YES_NO
      );
      
      if (reprocessResponse !== ui.Button.YES) return;
      forceReprocess = true;
    }
    
    SpreadsheetApp.getActiveSpreadsheet().toast(`Evaluating ${candidateName}...`, 'Processing', -1);
    
    try {
      const result = callWebApp('testSingleCandidate', {
        ...config,
        spreadsheetId: SpreadsheetApp.getActiveSpreadsheet().getId(),
        candidateName: candidateName,
        forceReprocess: forceReprocess
      });
      
      SpreadsheetApp.getActiveSpreadsheet().toast('Complete!', 'Success', 5);
      
      if (result.success && result.result) {
        ui.alert(
          'Test Complete',
          `Candidate: ${result.result.name}\n` +
          `Score: ${result.result.score}\n` +
          `Recommendation: ${result.result.recommendation}\n` +
          `Current Company: ${result.result.currentCompany}\n\n` +
          `Top Strengths:\n${result.result.strengths ? result.result.strengths.join('\n') : 'N/A'}\n\n` +
          `Check the evaluation sheets for full details.`,
          ui.ButtonSet.OK
        );
      } else {
        handleProcessingError(result, ui);
      }
      
    } catch (error) {
      SpreadsheetApp.getActiveSpreadsheet().toast('Failed', 'Error', 5);
      ui.alert('Error', 'Failed to test: ' + error.toString(), ui.ButtonSet.OK);
    }
  });
}

// ============================================
// SHEET FORMATTING
// ============================================

function formatAllSheets() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const sheets = ss.getSheets();
  
  sheets.forEach(sheet => {
    const sheetName = sheet.getName();
    const lastCol = sheet.getLastColumn();
    const lastRow = sheet.getLastRow();
    
    if (lastCol === 0 || lastRow === 0) return;
    
    // Set text wrapping for all cells
    if (lastRow > 0 && lastCol > 0) {
      sheet.getRange(1, 1, lastRow, lastCol).setWrap(true);
    }
    
    // Set column widths based on sheet type
    switch(sheetName) {
      case 'Processing Log':
        sheet.setColumnWidth(1, 150);
        sheet.setColumnWidth(2, 400);
        sheet.setColumnWidth(3, 100);
        sheet.setColumnWidth(4, 150);
        break;
        
      case 'Position Configuration':
        sheet.setColumnWidth(1, 200);
        sheet.setColumnWidth(2, 350);
        sheet.setColumnWidth(3, 150);
        break;
        
      case 'Dynamic Rubric Configuration':
        sheet.setColumnWidth(1, 180);
        sheet.setColumnWidth(2, 120);
        sheet.setColumnWidth(3, 200);
        sheet.setColumnWidth(4, 100);
        sheet.setColumnWidth(5, 250);
        for (let i = 6; i <= 11; i++) {
          sheet.setColumnWidth(i, 150);
        }
        break;
        
      case 'Company Metrics':
        // Updated column widths for new metrics
        sheet.setColumnWidth(1, 120);  // Date
        sheet.setColumnWidth(2, 200);  // Name
        sheet.setColumnWidth(3, 100);  // Score
        sheet.setColumnWidth(4, 200);  // Current Company
        sheet.setColumnWidth(5, 120);  // Time at Current
        sheet.setColumnWidth(6, 180);  // Advancement
        sheet.setColumnWidth(7, 120);  // Career Exp Years
        sheet.setColumnWidth(8, 120);  // Industry Exp Years
        sheet.setColumnWidth(9, 120);  // Avg Time Per Role
        sheet.setColumnWidth(10, 120); // Years Mgmt Experience
        sheet.setColumnWidth(11, 150); // Team Size Managed
        sheet.setColumnWidth(12, 150); // Budget Managed
        sheet.setColumnWidth(13, 120); // Short Stints
        sheet.setColumnWidth(14, 120); // Job Hopping
        sheet.setColumnWidth(15, 200); // Notable Companies
        sheet.setColumnWidth(16, 200); // Industries
        sheet.setColumnWidth(17, 200); // Universities
        sheet.setColumnWidth(18, 200); // Degrees
        sheet.setColumnWidth(19, 200); // Certifications
        sheet.setColumnWidth(20, 200); // Functional Expertise
        sheet.setColumnWidth(21, 300); // Technical Skills
        sheet.setColumnWidth(22, 450); // X-Factor
        sheet.setColumnWidth(23, 150); // Language Quality
        sheet.setColumnWidth(24, 150); // Documents Status
        sheet.setColumnWidth(25, 120); // Match Confidence
        break;
        
      case 'Candidate Evaluations':
        sheet.setColumnWidth(1, 120);
        sheet.setColumnWidth(2, 200);
        sheet.setColumnWidth(3, 100);
        for (let i = 4; i <= 11; i++) {
          sheet.setColumnWidth(i, 100);
        }
        sheet.setColumnWidth(12, 150);
        sheet.setColumnWidth(13, 400);
        break;
        
      case 'Detailed Evaluations':
        sheet.setColumnWidth(1, 120);
        sheet.setColumnWidth(2, 200);
        sheet.setColumnWidth(3, 180);
        sheet.setColumnWidth(4, 120);
        sheet.setColumnWidth(5, 120);
        sheet.setColumnWidth(6, 200);
        sheet.setColumnWidth(7, 100);
        sheet.setColumnWidth(8, 100);
        sheet.setColumnWidth(9, 400);
        break;
        
      case 'Combined Analysis':
        sheet.setColumnWidth(1, 200);
        sheet.setColumnWidth(2, 100);
        sheet.setColumnWidth(3, 150);
        sheet.setColumnWidth(4, 150);
        sheet.setColumnWidth(5, 200);
        sheet.setColumnWidth(6, 120);
        sheet.setColumnWidth(7, 120);
        sheet.setColumnWidth(8, 100);
        sheet.setColumnWidth(9, 400);
        sheet.setColumnWidth(10, 300);
        sheet.setColumnWidth(11, 150);
        break;
        
      case 'Document Mapping':
        sheet.setColumnWidth(1, 200);
        sheet.setColumnWidth(2, 250);
        sheet.setColumnWidth(3, 250);
        sheet.setColumnWidth(4, 150);
        sheet.setColumnWidth(5, 150);
        sheet.setColumnWidth(6, 120);
        break;
        
      case 'Processing Status':
        sheet.setColumnWidth(1, 150);
        sheet.setColumnWidth(2, 200);
        sheet.setColumnWidth(3, 120);
        sheet.setColumnWidth(4, 400);
        break;
        
      case 'API Usage Tracking':
        sheet.setColumnWidth(1, 150);
        sheet.setColumnWidth(2, 200);
        sheet.setColumnWidth(3, 180);
        sheet.setColumnWidth(4, 120);
        sheet.setColumnWidth(5, 120);
        sheet.setColumnWidth(6, 120);
        sheet.setColumnWidth(7, 120);
        sheet.setColumnWidth(8, 120);
        sheet.setColumnWidth(9, 120);
        break;
        
      default:
        // Default widths for other sheets
        if (lastCol > 0) {
          for (let i = 1; i <= lastCol; i++) {
            sheet.setColumnWidth(i, 150);
          }
        }
    }
  });
}

// ============================================
// WELCOME SCREEN & TERMS
// ============================================

function checkFirstTimeUser() {
  const props = PropertiesService.getDocumentProperties();
  const hasSeenWelcome = props.getProperty('HAS_SEEN_WELCOME');
  const hasAcceptedTerms = props.getProperty('TERMS_ACCEPTED');
  
  if (!hasSeenWelcome || !hasAcceptedTerms) {
    showWelcomeScreen();
  }
}

function showWelcomeScreen() {
  const ui = SpreadsheetApp.getUi();
  const htmlContent = `
<!DOCTYPE html>
<html>
<head>
  <base target="_top">
  <style>
    :root {
      --ruby-color: #8B1538;
      --ruby-gradient-start: #8B1538;
      --ruby-gradient-end: #A91B60;
    }
    
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      padding: 0;
      margin: 0;
      background: linear-gradient(135deg, var(--ruby-gradient-start) 0%, var(--ruby-gradient-end) 100%);
    }
    .container {
      max-width: 600px;
      margin: 20px auto;
      background: white;
      border-radius: 12px;
      box-shadow: 0 20px 60px rgba(0,0,0,0.3);
      overflow: hidden;
    }
    .header {
      background: linear-gradient(135deg, var(--ruby-gradient-start) 0%, var(--ruby-gradient-end) 100%);
      color: white;
      padding: 30px;
      text-align: center;
    }
    .logo {
      font-size: 48px;
      margin-bottom: 10px;
    }
    h1 {
      margin: 0;
      font-size: 28px;
      font-weight: 600;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    .tagline {
      margin-top: 10px;
      font-size: 16px;
      opacity: 0.95;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    .content {
      padding: 30px;
    }
    .section {
      margin-bottom: 25px;
    }
    .section h2 {
      color: #333;
      font-size: 20px;
      margin-bottom: 10px;
      display: flex;
      align-items: center;
    }
    .section h2 .icon {
      margin-right: 10px;
      font-size: 24px;
    }
    .feature-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 15px;
      margin-top: 15px;
    }
    .feature {
      padding: 12px;
      background: #f8f9fa;
      border-radius: 8px;
      border-left: 3px solid var(--ruby-color);
    }
    .feature-title {
      font-weight: 600;
      color: var(--ruby-color);
      margin-bottom: 5px;
    }
    .feature-desc {
      font-size: 13px;
      color: #666;
    }
    .terms-box {
      background: #fff9e6;
      border: 2px solid #ffc107;
      border-radius: 8px;
      padding: 15px;
      margin-top: 20px;
      max-height: 150px;
      overflow-y: auto;
    }
    .terms-box h3 {
      margin-top: 0;
      color: #ff6b00;
    }
    .terms-text {
      font-size: 12px;
      color: #666;
      line-height: 1.5;
    }
    .checkbox-container {
      margin: 20px 0;
      padding: 15px;
      background: #f0f7ff;
      border-radius: 8px;
      display: flex;
      align-items: center;
    }
    .checkbox-container input {
      width: 20px;
      height: 20px;
      margin-right: 12px;
      cursor: pointer;
    }
    .checkbox-container label {
      cursor: pointer;
      font-size: 14px;
      color: #333;
    }
    .button-container {
      display: flex;
      gap: 10px;
      justify-content: center;
      margin-top: 25px;
    }
    .btn {
      padding: 12px 30px;
      border: none;
      border-radius: 6px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    .btn-primary {
      background: linear-gradient(135deg, var(--ruby-gradient-start) 0%, var(--ruby-gradient-end) 100%);
      color: white;
    }
    .btn-primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(139, 21, 56, 0.4);
    }
    .btn-primary:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
    }
    .btn-secondary {
      background: #e9ecef;
      color: #495057;
    }
    .btn-secondary:hover {
      background: #dee2e6;
    }
    .workflow {
      background: #e8f5e9;
      border-radius: 8px;
      padding: 15px;
      margin-top: 15px;
    }
    .workflow-step {
      display: flex;
      align-items: center;
      margin: 8px 0;
    }
    .step-number {
      background: #4caf50;
      color: white;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 10px;
      font-size: 12px;
      font-weight: bold;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <div class="logo">💎</div>
      <h1>Welcome to RubiRecruit™</h1>
      <div class="tagline">AI-Powered Recruitment Excellence</div>
    </div>
    
    <div class="content">
      <div class="section">
        <h2><span class="icon">✨</span>Transform Your Hiring Process</h2>
        <p>RubiRecruit uses cutting-edge AI to create position-specific evaluation rubrics and assess candidates with unprecedented accuracy and consistency.</p>
      </div>
      
      <div class="section">
        <h2><span class="icon">🎯</span>Core Features</h2>
        <div class="feature-grid">
          <div class="feature">
            <div class="feature-title">🤖 Dynamic AI Rubrics</div>
            <div class="feature-desc">Automatically generates custom evaluation criteria for any position</div>
          </div>
          <div class="feature">
            <div class="feature-title">📊 Smart Scoring</div>
            <div class="feature-desc">Consistent, unbiased evaluation across all candidates</div>
          </div>
          <div class="feature">
            <div class="feature-title">⚡ Batch Processing</div>
            <div class="feature-desc">Evaluate multiple candidates simultaneously with auto-retry</div>
          </div>
          <div class="feature">
            <div class="feature-title">📈 Analytics Dashboard</div>
            <div class="feature-desc">Real-time insights and comprehensive reporting</div>
          </div>
        </div>
      </div>
      
<div class="section">
  <h2><span class="icon">🔄</span>Simple 8-Step Workflow</h2>
  <div class="workflow">
    <div class="workflow-step">
      <span class="step-number">1</span>
      <span>Configure API and folders</span>
    </div>
    <div class="workflow-step">
      <span class="step-number">2</span>
      <span>Create all evaluation sheets</span>
    </div>
    <div class="workflow-step">
      <span class="step-number">3</span>
      <span>Load position description</span>
    </div>
    <div class="workflow-step">
      <span class="step-number">4</span>
      <span>Map CVs and cover letters</span>
    </div>
    <div class="workflow-step">
      <span class="step-number">5</span>
      <span>Generate AI rubric</span>
    </div>
    <div class="workflow-step">
      <span class="step-number">6</span>
      <span>Lock rubric for consistency</span>
    </div>
    <div class="workflow-step">
      <span class="step-number">7</span>
      <span>Process and evaluate candidates</span>
    </div>
    <div class="workflow-step">
      <span class="step-number">8</span>
      <span>Review results and insights</span>
    </div>
  </div>
</div>
      
      <div class="terms-box">
        <h3>⚖️ Terms & Conditions</h3>
        <div class="terms-text">
          <strong>IMPORTANT - PLEASE READ CAREFULLY:</strong><br><br>
          
          <strong>1. NO WARRANTY:</strong> This software is provided "as is" without warranty of any kind.<br><br>
          
          <strong>2. LIMITATION OF LIABILITY:</strong> Developers are not liable for any damages arising from use.<br><br>
          
          <strong>3. AI DISCLAIMER:</strong> Evaluations are for decision support only. Human judgment required.<br><br>
          
          <strong>4. DATA PRIVACY:</strong> You are responsible for compliance with data protection laws.<br><br>
          
          <strong>5. API COSTS:</strong> You are responsible for all Claude API usage costs.<br><br>
          
          <strong>6. COMPLIANCE:</strong> You must comply with all employment laws and fair hiring practices.<br><br>
          
          <strong>7. LICENSE:</strong> Valid license required for continued use.<br><br>
          
          By using RubiRecruit, you acknowledge that you have read, understood, and agree to be bound by these terms.
        </div>
      </div>
      
      <div class="checkbox-container">
        <input type="checkbox" id="acceptTerms" onchange="toggleButton()">
        <label for="acceptTerms">I have read and accept the Terms & Conditions</label>
      </div>
      
      <div class="button-container">
        <button class="btn btn-secondary" onclick="google.script.host.close()">Cancel</button>
        <button class="btn btn-primary" id="getStartedBtn" onclick="acceptAndStart()" disabled>Get Started</button>
      </div>
    </div>
  </div>
  
  <script>
    function toggleButton() {
      const checkbox = document.getElementById('acceptTerms');
      const button = document.getElementById('getStartedBtn');
      button.disabled = !checkbox.checked;
    }
    
    function acceptAndStart() {
      google.script.run
        .withSuccessHandler(() => {
          google.script.host.close();
        })
        .acceptTermsAndProceed();
    }
  </script>
</body>
</html>
`;
  
  const htmlOutput = HtmlService.createHtmlOutput(htmlContent)
    .setWidth(650)
    .setHeight(750);
  
  ui.showModalDialog(htmlOutput, 'Welcome to RubiRecruit™');
}

function acceptTermsAndProceed() {
  const props = PropertiesService.getDocumentProperties();
  props.setProperties({
    'HAS_SEEN_WELCOME': 'true',
    'TERMS_ACCEPTED': 'true',
    'TERMS_ACCEPTED_DATE': new Date().toISOString()
  });
  
  // Refresh sidebar
  showSidebar();
  
  // Check if setup is needed
  const config = getConfiguration();
  if (!config) {
    runInitialSetup();
  }
}

// ============================================
// COMMUNICATION FUNCTION UPDATE
// ============================================

function callWebApp(action, additionalData = {}) {
  // Validate URL
  if (!WEB_APP_URL || WEB_APP_URL === 'https://script.google.com/macros/s/YOUR_WEB_APP_ID/exec') {
    throw new Error('Web app URL not configured. Please update WEB_APP_URL in the script.');
  }
  
  // Get stored configuration
  const config = getConfiguration();
  
  // Add license key to requests
  const props = PropertiesService.getDocumentProperties();
  const licenseKey = props.getProperty('LICENSE_KEY');
  
  // Build request payload
  const payload = {
    action: action,
    version: VERSION,
    timestamp: new Date().toISOString(),
    customerIdentifier: config ? config.customerIdentifier : 'Unknown',
    licenseKey: licenseKey,
    ...additionalData
  };
  
  // Make request
  const options = {
    method: 'post',
    contentType: 'application/json',
    payload: JSON.stringify(payload),
    muteHttpExceptions: true
  };
  
  try {
    console.log(`Calling web app action: ${action}`);
    
    const response = UrlFetchApp.fetch(WEB_APP_URL, options);
    const responseCode = response.getResponseCode();
    const responseText = response.getContentText();
    
    console.log(`Response code: ${responseCode}`);
    
    if (responseCode !== 200) {
      console.error(`Server error ${responseCode}: ${responseText}`);
      throw new Error(`Server returned error code ${responseCode}`);
    }
    
    // Check if response is HTML instead of JSON
    if (responseText.trim().startsWith('<!DOCTYPE') || responseText.trim().startsWith('<html')) {
      console.error('Received HTML instead of JSON');
      throw new Error('Web app error. Please check deployment.');
    }
    
    try {
      return JSON.parse(responseText);
    } catch (parseError) {
      console.error('Failed to parse JSON:', parseError);
      console.error('Response was:', responseText);
      throw new Error('Invalid response from web app');
    }
    
  } catch (error) {
    console.error(`Web app call failed: ${error}`);
    throw error;
  }
}

// ============================================
// QUICK SETUP FUNCTION
// ============================================

function runQuickSetup() {
  const ui = SpreadsheetApp.getUi();
  
  ui.alert(
    '⚡ Quick Setup Wizard',
    'This wizard will guide you through the complete setup process:\n\n' +
    '1. Configure API key and folders\n' +
    '2. Create all necessary sheets\n' +
    '3. Help you load your first position\n' +
    '4. Generate your first rubric\n\n' +
    'Ready to begin?',
    ui.ButtonSet.OK
  );
  
  runInitialSetup();
}

// ============================================
// ENHANCED POSITION SELECTOR
// ============================================

function showAdvancedPositionSelector() {
  const ui = SpreadsheetApp.getUi();
  const config = getConfiguration();
  
  if (!config) {
    ui.alert('Setup Required', 'Please run Initial Setup first', ui.ButtonSet.OK);
    return;
  }
  
  try {
    const folder = DriveApp.getFolderById(config.positionFolderId);
    const files = folder.getFilesByType('application/pdf');
    
    const fileList = [];
    while (files.hasNext()) {
      const file = files.next();
      fileList.push({
        name: file.getName(),
        id: file.getId(),
        dateModified: file.getLastUpdated(),
        size: file.getSize()
      });
    }
    
    if (fileList.length === 0) {
      ui.alert(
        '📂 No Position Descriptions Found',
        'No PDF files were found in your position descriptions folder.\n\n' +
        'Please add PDF position descriptions to the folder and try again.',
        ui.ButtonSet.OK
      );
      return;
    }
    
    fileList.sort((a, b) => b.dateModified - a.dateModified);
    
    const htmlContent = `
<!DOCTYPE html>
<html>
<head>
  <base target="_top">
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      padding: 20px;
      background: #f5f7fa;
    }
    h2 {
      color: #333;
      margin-bottom: 10px;
    }
    .subtitle {
      color: #666;
      margin-bottom: 20px;
      font-size: 14px;
    }
    .file-list {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      max-height: 400px;
      overflow-y: auto;
    }
    .file-item {
      padding: 15px 20px;
      border-bottom: 1px solid #eee;
      cursor: pointer;
      transition: background 0.2s;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .file-item:hover {
      background: #f0f7ff;
    }
    .file-item.selected {
      background: #e3f2fd;
      border-left: 3px solid #2196F3;
    }
    .file-name {
      font-weight: 500;
      color: #333;
      margin-bottom: 5px;
    }
    .file-info {
      font-size: 12px;
      color: #999;
    }
    .file-icon {
      font-size: 24px;
      margin-right: 15px;
      color: #ef4444;
    }
    .button-container {
      margin-top: 20px;
      text-align: right;
    }
    .btn {
      padding: 10px 24px;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 600;
      cursor: pointer;
      margin-left: 10px;
      transition: all 0.2s;
    }
    .btn-primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }
    .btn-primary:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }
    .btn-primary:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
    }
    .btn-secondary {
      background: #e9ecef;
      color: #495057;
    }
    .btn-secondary:hover {
      background: #dee2e6;
    }
    .search-box {
      margin-bottom: 15px;
    }
    .search-box input {
      width: 100%;
      padding: 10px 15px;
      border: 1px solid #ddd;
      border-radius: 6px;
      font-size: 14px;
    }
    .search-box input:focus {
      outline: none;
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
  </style>
</head>
<body>
  <h2>📄 Select Position Description</h2>
  <div class="subtitle">Choose the position you want to create an evaluation rubric for:</div>
  
  <div class="search-box">
    <input type="text" id="searchInput" placeholder="🔍 Search positions..." onkeyup="filterFiles()">
  </div>
  
  <div class="file-list" id="fileList">
    ${fileList.map((file, index) => `
      <div class="file-item" data-index="${index}" onclick="selectFile(${index})">
        <div style="display: flex; align-items: center;">
          <span class="file-icon">📄</span>
          <div>
            <div class="file-name">${file.name.replace('.pdf', '')}</div>
            <div class="file-info">
              Modified: ${new Date(file.dateModified).toLocaleDateString()} • 
              Size: ${formatFileSize(file.size)}
            </div>
          </div>
        </div>
      </div>
    `).join('')}
  </div>
  
  <div class="button-container">
    <button class="btn btn-secondary" onclick="google.script.host.close()">Cancel</button>
    <button class="btn btn-primary" id="selectBtn" onclick="loadSelected()" disabled>Load Position</button>
  </div>
  
  <script>
    const files = ${JSON.stringify(fileList)};
    let selectedIndex = -1;
    
    function selectFile(index) {
      document.querySelectorAll('.file-item').forEach(item => {
        item.classList.remove('selected');
      });
      
      document.querySelectorAll('.file-item')[index].classList.add('selected');
      selectedIndex = index;
      
      document.getElementById('selectBtn').disabled = false;
    }
    
    function filterFiles() {
      const searchTerm = document.getElementById('searchInput').value.toLowerCase();
      const fileItems = document.querySelectorAll('.file-item');
      
      fileItems.forEach((item, index) => {
        const fileName = files[index].name.toLowerCase();
        if (fileName.includes(searchTerm)) {
          item.style.display = 'flex';
        } else {
          item.style.display = 'none';
        }
      });
    }
    
    function formatFileSize(bytes) {
      if (bytes < 1024) return bytes + ' B';
      if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
      return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
    }
    
    function loadSelected() {
      if (selectedIndex >= 0) {
        document.getElementById('selectBtn').disabled = true;
        document.getElementById('selectBtn').textContent = 'Loading...';
        
        google.script.run
          .withSuccessHandler(() => {
            google.script.host.close();
          })
          .withFailureHandler((error) => {
            alert('Error: ' + error);
            document.getElementById('selectBtn').disabled = false;
            document.getElementById('selectBtn').textContent = 'Load Position';
          })
          .loadSelectedPosition(files[selectedIndex].id);
      }
    }
  </script>
</body>
</html>
`;
    
    function formatFileSize(bytes) {
      if (bytes < 1024) return bytes + ' B';
      if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
      return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
    }
    
    const htmlOutput = HtmlService.createHtmlOutput(htmlContent)
      .setWidth(600)
      .setHeight(550);
    
    ui.showModalDialog(htmlOutput, 'Position Document Selector');
    
  } catch (error) {
    ui.alert('Error', 'Failed to access position folder: ' + error.toString(), ui.ButtonSet.OK);
  }
}

function loadSelectedPosition(fileId) {
  const ui = SpreadsheetApp.getUi();
  const config = getConfiguration();
  
  SpreadsheetApp.getActiveSpreadsheet().toast(
    'Loading position description...',
    '⏳ Processing',
    -1
  );
  
  const result = callWebApp('loadPositionDescription', {
    ...config,
    spreadsheetId: SpreadsheetApp.getActiveSpreadsheet().getId(),
    positionFileId: fileId
  });
  
  SpreadsheetApp.getActiveSpreadsheet().toast('', 'Complete', 1);
  
  if (result.success) {
    ui.alert(
      '✅ Position Loaded Successfully',
      `Position: ${result.positionTitle}\n\n` +
      `Document size: ${result.textLength} characters\n\n` +
      `Next step: Generate AI Rubric\n\n` +
      `Click OK to continue, then select:\n` +
      `📋 Position & Rubric → 🤖 Generate AI Rubric`,
      ui.ButtonSet.OK
    );
    
    // Refresh sidebar
    showSidebar();
  } else {
    ui.alert('❌ Error', result.error || 'Failed to load position', ui.ButtonSet.OK);
  }
}

// ============================================
// USER GUIDE
// ============================================

function showUserGuide() {
  const ui = SpreadsheetApp.getUi();
  
  const htmlContent = `
<!DOCTYPE html>
<html>
<head>
  <base target="_top">
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      padding: 20px;
      background: #f5f7fa;
      line-height: 1.6;
    }
    h1 {
      color: #8B1538;
      border-bottom: 3px solid #8B1538;
      padding-bottom: 10px;
    }
    h2 {
      color: #8B1538;
      margin-top: 25px;
    }
    .journey-step {
      background: white;
      padding: 15px;
      margin: 15px 0;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      border-left: 4px solid #8B1538;
    }
    .step-number {
      display: inline-block;
      background: #8B1538;
      color: white;
      width: 30px;
      height: 30px;
      border-radius: 50%;
      text-align: center;
      line-height: 30px;
      margin-right: 10px;
      font-weight: bold;
    }
    .tip {
      background: #fff9e6;
      border: 1px solid #ffc107;
      padding: 12px;
      border-radius: 6px;
      margin: 10px 0;
    }
    .tip::before {
      content: "💡 ";
      font-size: 18px;
    }
    .warning {
      background: #ffebee;
      border: 1px solid #ef5350;
      padding: 12px;
      border-radius: 6px;
      margin: 10px 0;
    }
    .warning::before {
      content: "⚠️ ";
      font-size: 18px;
    }
    .success {
      background: #e8f5e9;
      border: 1px solid #4caf50;
      padding: 12px;
      border-radius: 6px;
      margin: 10px 0;
    }
    .success::before {
      content: "✅ ";
      font-size: 18px;
    }
    code {
      background: #f0f0f0;
      padding: 2px 6px;
      border-radius: 3px;
      font-family: 'Courier New', monospace;
    }
    .menu-path {
      background: #e3f2fd;
      padding: 8px 12px;
      border-radius: 4px;
      border-left: 3px solid #1976d2;
      margin: 8px 0;
      font-weight: 500;
    }
  </style>
</head>
<body>
  <h1>📖 RubiRecruit™ User Guide v7.0.0</h1>
  
  <div class="success">
    <strong>Interactive Journey:</strong> Use the sidebar on the right to track your progress through all 8 steps. The sidebar shows real-time status and guides you through each phase.
  </div>
  
  <h2>🚀 The 8-Step Journey to Recruitment Excellence</h2>
  
  <div class="journey-step">
    <span class="step-number">1</span>
    <strong>Initial Setup</strong><br>
    <div class="menu-path">Menu: 1️⃣ Initial Setup → 🔑 Configure API & Folders</div>
    Configure your Claude API key and Google Drive folders:
    <ul>
      <li>Get your API key from <code>console.anthropic.com</code></li>
      <li>Create a folder for Position Descriptions (PDFs)</li>
      <li>Create a folder for CVs and Cover Letters (PDFs)</li>
      <li>Enter your company identifier for tracking</li>
    </ul>
  </div>
  
  <div class="journey-step">
    <span class="step-number">2</span>
    <strong>Create All Sheets</strong><br>
    <div class="menu-path">Menu: 2️⃣ Create All Sheets → 📊 Setup All Sheets</div>
    Automatically creates 13 specialized sheets:
    <ul>
      <li>Position Configuration & Text storage</li>
      <li>Dynamic Rubric Configuration</li>
      <li>Evaluation tracking sheets</li>
      <li>Analytics and dashboard sheets</li>
      <li>API usage tracking</li>
    </ul>
  </div>
  
  <div class="journey-step">
    <span class="step-number">3</span>
    <strong>Load Position</strong><br>
    <div class="menu-path">Menu: 3️⃣ Load Position → 📄 Select Position Description</div>
    Select a PDF position description from your configured folder:
    <ul>
      <li>Browse all PDFs in your position folder</li>
      <li>Search and filter positions</li>
      <li>System extracts text using OCR</li>
      <li>Position becomes the basis for rubric generation</li>
    </ul>
  </div>
  
  <div class="journey-step">
    <span class="step-number">4</span>
    <strong>Document Mapping</strong><br>
    <div class="menu-path">Menu: 4️⃣ Document Mapping → 🔗 Create Document Mapping</div>
    Smart matching of CVs with cover letters:
    <ul>
      <li>Scans your CV folder for all PDFs</li>
      <li>Intelligently matches CVs with cover letters</li>
      <li>Handles name variations</li>
      <li>Creates candidate list for evaluation</li>
    </ul>
  </div>
  
  <div class="journey-step">
    <span class="step-number">5</span>
    <strong>Generate Rubric</strong><br>
    <div class="menu-path">Menu: 5️⃣ Generate Rubric → 🤖 Generate AI Rubric</div>
    AI creates a position-specific evaluation framework:
    <ul>
      <li>Analyzes position requirements</li>
      <li>Creates 8 weighted categories (5-20% each)</li>
      <li>Generates 5 attributes per category</li>
      <li>Builds detailed 0-5 scoring scales</li>
      <li>Categories total exactly 100%</li>
    </ul>
    <div class="tip">
      The rubric is completely customized for each position - technical roles get technical criteria, leadership roles get leadership criteria, etc.
    </div>
  </div>
  
  <div class="journey-step">
    <span class="step-number">6</span>
    <strong>Lock Rubric</strong><br>
    <div class="menu-path">Menu: 6️⃣ Lock Rubric → 🔒 Lock Rubric for Use</div>
    Finalize the rubric before evaluation:
    <ul>
      <li>Review the generated rubric in the Dynamic Rubric Configuration sheet</li>
      <li>Make any necessary edits (optional)</li>
      <li>Lock the rubric to ensure consistent evaluation</li>
      <li>All candidates will be evaluated using the same criteria</li>
    </ul>
    <div class="warning">
      Once locked, the rubric cannot be changed without clearing all evaluations. This ensures fairness and consistency.
    </div>
  </div>
  
  <div class="journey-step">
    <span class="step-number">7</span>
    <strong>Process Candidates</strong><br>
    <div class="menu-path">Menu: 7️⃣ Process Candidates → [Choose processing option]</div>
    Multiple processing options available:
    <ul>
      <li><strong>Process Next 5/10:</strong> Manual batch processing for control</li>
      <li><strong>Process Specific:</strong> Select specific candidates by name</li>
      <li><strong>Auto-Process All:</strong> Automatic processing with pauses</li>
      <li><strong>Retry Failed:</strong> Re-process any failed evaluations</li>
    </ul>
    <div class="tip">
      Start with "Process Next 5" to verify everything is working correctly before processing all candidates.
    </div>
  </div>
  
  <div class="journey-step">
    <span class="step-number">8</span>
    <strong>Review Results</strong><br>
    <div class="menu-path">Menu: 8️⃣ Review Results → [Various options]</div>
    Comprehensive analysis tools:
    <ul>
      <li><strong>Processing Status:</strong> Overview of progress and statistics</li>
      <li><strong>Update Dashboard:</strong> Refresh the summary dashboard</li>
      <li><strong>Generate Report:</strong> Create detailed evaluation report</li>
      <li><strong>View Analytics:</strong> Deep dive into candidate scores</li>
    </ul>
  </div>
  
  <h2>📊 Understanding the Evaluation Sheets</h2>
  
  <p><strong>Key sheets to review:</strong></p>
  <ul>
    <li><code>Summary Dashboard</code> - High-level overview and rankings</li>
    <li><code>Candidate Evaluations</code> - Individual scores by category</li>
    <li><code>Company Metrics</code> - Employment history and tenure analysis</li>
    <li><code>Detailed Evaluations</code> - Attribute-level scoring details</li>
    <li><code>Combined Analysis</code> - Strengths, weaknesses, and recommendations</li>
  </ul>
  
  <h2>💰 API Usage & Costs</h2>
  
  <div class="warning">
    <strong>Cost Awareness:</strong> Each candidate evaluation costs approximately $0.15-0.25 in API fees. Monitor your usage via Menu → 9️⃣ Tools & Testing → 💰 API Usage & Costs
  </div>
  
  <p>Current pricing (Claude 3.5 Sonnet):</p>
  <ul>
    <li>Input: $3.00 per million tokens</li>
    <li>Output: $15.00 per million tokens</li>
    <li>Rubric generation: ~$0.30-0.50 one-time</li>
    <li>Per candidate: ~$0.15-0.25</li>
  </ul>
  
  <h2>🔧 Troubleshooting</h2>
  
  <p>If you encounter issues:</p>
  <ol>
    <li>Check connection: <code>9️⃣ Tools & Testing → 📌 Test Connection</code></li>
    <li>Validate API key: <code>9️⃣ Tools & Testing → ✓ Validate API Key</code></li>
    <li>Verify permissions: <code>9️⃣ Tools & Testing → 🔍 Check Permissions</code></li>
    <li>View logs: <code>9️⃣ Tools & Testing → 📜 View Processing Log</code></li>
    <li>Check sidebar status indicators for guidance</li>
  </ol>
  
  <h2>🎯 Best Practices</h2>
  
  <div class="success">
    <strong>Recommended Workflow:</strong>
    <ol>
      <li>Always use the sidebar to track your progress</li>
      <li>Test with 5 candidates before processing all</li>
      <li>Review the rubric carefully before locking</li>
      <li>Keep position descriptions clear and detailed</li>
      <li>Ensure CVs are high-quality PDFs for best OCR results</li>
      <li>Process in batches if you have many candidates (50+)</li>
    </ol>
  </div>
  
  <h2>📞 Support</h2>
  <p>For additional support or license inquiries, contact: <strong><EMAIL></strong></p>
  
  <div class="tip">
    <strong>Pro Tip:</strong> The sidebar updates in real-time as you progress through the steps. Green checkmarks (✅) indicate completed steps, and the current step pulses to show where you are in the journey.
  </div>
</body>
</html>
`;
  
  const htmlOutput = HtmlService.createHtmlOutput(htmlContent)
    .setWidth(750)
    .setHeight(600);
  
  ui.showModalDialog(htmlOutput, 'RubiRecruit User Guide');
}

function loadPositionDescription() {
  // Redirect to new advanced selector
  showAdvancedPositionSelector();
}

// ============================================
// INITIAL SETUP
// ============================================

function runInitialSetup() {
  const ui = SpreadsheetApp.getUi();
  
  const props = PropertiesService.getDocumentProperties();
  const existingKey = props.getProperty('CLAUDE_API_KEY');
  
  if (existingKey) {
    const response = ui.alert(
      '⚙️ Setup Already Complete',
      'Settings already exist. Do you want to reconfigure?',
      ui.ButtonSet.YES_NO
    );
    if (response !== ui.Button.YES) return;
  }
  
  ui.alert(
    '🚀 Initial Setup - RubiRecruit v7.0.0',
    'Welcome to the setup wizard!\n\n' +
    'This wizard will help you configure the system.\n\n' +
    'You will need:\n' +
    '• Your Claude API key from Anthropic\n' +
    '• Folder IDs for position descriptions and CVs\n' +
    '• A company identifier (for tracking)\n\n' +
    'Let\'s get started! 🎯',
    ui.ButtonSet.OK
  );
  
  const apiKeyResponse = ui.prompt(
    '🔑 Claude API Key',
    'Enter your Claude API key from Anthropic:\n\n' +
    '📌 Get it from: console.anthropic.com\n\n' +
    'The key should start with "sk-ant-"',
    ui.ButtonSet.OK_CANCEL
  );
  
  if (apiKeyResponse.getSelectedButton() !== ui.Button.OK) return;
  const apiKey = apiKeyResponse.getResponseText().trim();
  
  if (!apiKey || apiKey.length < 20) {
    ui.alert('❌ Error', 'Invalid API key format', ui.ButtonSet.OK);
    return;
  }
  
  const positionFolderResponse = ui.prompt(
    '📁 Position Descriptions Folder',
    'Enter the Google Drive folder ID containing position descriptions:\n\n' +
    '📌 To find it:\n' +
    '1. Open the folder in Google Drive\n' +
    '2. Look at the URL: .../folders/[FOLDER_ID]\n' +
    '3. Copy the ID part only',
    ui.ButtonSet.OK_CANCEL
  );
  
  if (positionFolderResponse.getSelectedButton() !== ui.Button.OK) return;
  const positionFolderId = positionFolderResponse.getResponseText().trim();
  
  const cvFolderResponse = ui.prompt(
    '📁 CV Folder ID',
    'Enter the Google Drive folder ID containing CVs:\n\n' +
    '💡 Tip: Store both CVs and Cover letters in this folder',
    ui.ButtonSet.OK_CANCEL
  );
  
  if (cvFolderResponse.getSelectedButton() !== ui.Button.OK) return;
  const cvFolderId = cvFolderResponse.getResponseText().trim();
  
  if (!cvFolderId || cvFolderId.length < 10) {
    ui.alert('❌ Error', 'Invalid folder ID', ui.ButtonSet.OK);
    return;
  }
  
  const companyResponse = ui.prompt(
    '🏢 Company Identifier',
    'Enter a short identifier for your organization:\n' +
    '(e.g., "ABC Corp", "Smith & Associates")',
    ui.ButtonSet.OK_CANCEL
  );
  
  if (companyResponse.getSelectedButton() !== ui.Button.OK) return;
  const companyId = companyResponse.getResponseText().trim() || 'Unknown';
  
  props.setProperties({
    'CLAUDE_API_KEY': apiKey,
    'POSITION_FOLDER_ID': positionFolderId,
    'CV_FOLDER_ID': cvFolderId,
    'COMPANY_ID': companyId,
    'SETUP_DATE': new Date().toISOString(),
    'VERSION': VERSION
  });
  
  // Setup sheets
  setupAllSheets();
  
  ui.alert(
    '✅ Setup Complete!',
    'Configuration saved successfully.\n\n' +
    '📋 Next steps:\n' +
    '1. Create Document Mapping\n' +
    '2. Load a Position Description\n' +
    '3. Generate AI Rubric\n' +
    '4. Lock the rubric\n' +
    '5. Process candidates\n\n' +
    '🚀 Ready to transform your hiring process!',
    ui.ButtonSet.OK
  );
  
  // Refresh sidebar
  showSidebar();
}

// ============================================
// REMAINING REQUIRED FUNCTIONS (cleaned up)
// ============================================

function generateDynamicRubric() {
  const ui = SpreadsheetApp.getUi();
  const config = getConfiguration();
  
  if (!config) {
    ui.alert('Setup Required', 'Please run Initial Setup first', ui.ButtonSet.OK);
    return;
  }
  
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const configSheet = ss.getSheetByName('Position Configuration');
  
  if (!configSheet || configSheet.getLastRow() < 2) {
    ui.alert(
      'No Position Loaded',
      'Please load a position description first.\n\n' +
      'Go to: Position & Rubric → Load Position Description',
      ui.ButtonSet.OK
    );
    return;
  }
  
  const response = ui.alert(
    '🤖 Generate Dynamic Rubric',
    'This will use AI to create a custom evaluation rubric based on the loaded position.\n\n' +
    'The rubric will have:\n' +
    '• 8 categories tailored to the position\n' +
    '• Dynamic weights (5-20%) based on importance\n' +
    '• 5 attributes per category\n' +
    '• Detailed 0-5 scoring scales\n\n' +
    'This may take 30-60 seconds. Continue?',
    ui.ButtonSet.YES_NO
  );
  
  if (response !== ui.Button.YES) return;
  
  SpreadsheetApp.getActiveSpreadsheet().toast(
    'Generating dynamic rubric with AI...',
    '🤖 Processing',
    -1
  );
  
  const result = callWebApp('generateDynamicRubric', {
    ...config,
    spreadsheetId: SpreadsheetApp.getActiveSpreadsheet().getId()
  });
  
  SpreadsheetApp.getActiveSpreadsheet().toast('', 'Complete', 1);
  
  if (result.success) {
    let message = `✅ Dynamic rubric generated successfully!\n\n`;
    message += `Position: ${result.positionTitle}\n\n`;
    message += `Categories created:\n`;
    result.categories.forEach((cat, index) => {
      message += `${index + 1}. ${cat}\n`;
    });
    message += `\nView the rubric in the "Dynamic Rubric Configuration" sheet.\n`;
    message += `\nNext step: Lock the rubric before evaluating candidates.`;
    
    ui.alert('Rubric Generated', message, ui.ButtonSet.OK);
    
    // Refresh sidebar
    showSidebar();
  } else {
    ui.alert('Error', result.error || 'Failed to generate rubric', ui.ButtonSet.OK);
  }
}

function viewCurrentRubric() {
  const ui = SpreadsheetApp.getUi();
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const rubricSheet = ss.getSheetByName('Dynamic Rubric Configuration');
  
  if (!rubricSheet || rubricSheet.getLastRow() <= 1) {
    ui.alert(
      'No Rubric',
      'No rubric has been generated yet.\n\n' +
      'Please generate a rubric first.',
      ui.ButtonSet.OK
    );
    return;
  }
  
  const configSheet = ss.getSheetByName('Position Configuration');
  let rubricStatus = 'Unknown';
  let positionTitle = 'Unknown Position';
  
  if (configSheet && configSheet.getLastRow() > 1) {
    for (let i = 2; i <= configSheet.getLastRow(); i++) {
      const field = configSheet.getRange(i, 1).getValue();
      if (field === 'Position Title') {
        positionTitle = configSheet.getRange(i, 2).getValue();
      } else if (field === 'Rubric Status') {
        rubricStatus = configSheet.getRange(i, 2).getValue();
      }
    }
  }
  
  const data = rubricSheet.getDataRange().getValues();
  const categories = new Set();
  let attributeCount = 0;
  
  for (let i = 1; i < data.length; i++) {
    if (data[i][0]) categories.add(data[i][0]);
    if (data[i][2]) attributeCount++;
  }
  
  let message = `CURRENT RUBRIC\n`;
  message += `${'─'.repeat(30)}\n\n`;
  message += `Position: ${positionTitle}\n`;
  message += `Status: ${rubricStatus}\n\n`;
  message += `Structure:\n`;
  message += `• ${categories.size} categories\n`;
  message += `• ${attributeCount} total attributes\n`;
  message += `• ${attributeCount * 6} scoring levels (0-5 for each)\n\n`;
  
  if (rubricStatus === 'LOCKED') {
    message += `✓ Rubric is locked and ready for evaluation\n`;
  } else {
    message += `⚠️ Rubric must be locked before evaluating candidates\n`;
  }
  
  message += `\nView full details in the "Dynamic Rubric Configuration" sheet`;
  
  ui.alert('Current Rubric', message, ui.ButtonSet.OK);
}

function lockRubric() {
  const ui = SpreadsheetApp.getUi();
  const config = getConfiguration();
  
  if (!config) {
    ui.alert('Setup Required', 'Please run Initial Setup first', ui.ButtonSet.OK);
    return;
  }
  
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const rubricSheet = ss.getSheetByName('Dynamic Rubric Configuration');
  
  if (!rubricSheet || rubricSheet.getLastRow() <= 1) {
    ui.alert(
      'No Rubric',
      'No rubric has been generated yet.\n\n' +
      'Please generate a rubric first.',
      ui.ButtonSet.OK
    );
    return;
  }
  
  const configSheet = ss.getSheetByName('Position Configuration');
  let isLocked = false;
  
  if (configSheet) {
    for (let i = 2; i <= configSheet.getLastRow(); i++) {
      if (configSheet.getRange(i, 1).getValue() === 'Rubric Status' && 
          configSheet.getRange(i, 2).getValue() === 'LOCKED') {
        isLocked = true;
        break;
      }
    }
  }
  
  if (isLocked) {
    ui.alert(
      'Already Locked',
      'The rubric is already locked.\n\n' +
      'To use a different rubric, clear all data and start over.',
      ui.ButtonSet.OK
    );
    return;
  }
  
  const response = ui.alert(
    '🔒 Lock Rubric',
    'Once locked, the rubric cannot be modified.\n\n' +
    'This ensures consistent evaluation across all candidates.\n\n' +
    'Are you sure you want to lock the rubric?',
    ui.ButtonSet.YES_NO
  );
  
  if (response !== ui.Button.YES) return;
  
  SpreadsheetApp.getActiveSpreadsheet().toast('Locking rubric...', 'Processing', -1);
  
  const result = callWebApp('lockRubric', {
    ...config,
    spreadsheetId: SpreadsheetApp.getActiveSpreadsheet().getId()
  });
  
  SpreadsheetApp.getActiveSpreadsheet().toast('', 'Complete', 1);
  
  if (result.success) {
    ui.alert(
      'Rubric Locked',
      '✓ Rubric has been locked successfully!\n\n' +
      'You can now:\n' +
      '• Process candidates\n\n' +
      'All candidates will be evaluated using this rubric.',
      ui.ButtonSet.OK
    );
    
    // Refresh sidebar
    showSidebar();
  } else {
    ui.alert('Error', result.error || 'Failed to lock rubric', ui.ButtonSet.OK);
  }
}

function createDocumentMapping() {
  const ui = SpreadsheetApp.getUi();
  const config = getConfiguration();
  
  if (!config) {
    ui.alert('Setup Required', 'Please run Initial Setup first', ui.ButtonSet.OK);
    return;
  }
  
  const response = ui.alert(
    'Create Document Mapping',
    'This will scan your CV folder and create a mapping of candidates.\n\n' +
    'Features:\n' +
    '• Smart matching for name variations\n' +
    '• Handles title variations\n' +
    '• Tiered matching confidence levels\n\n' +
    'Continue?',
    ui.ButtonSet.YES_NO
  );
  
  if (response !== ui.Button.YES) return;
  
  SpreadsheetApp.getActiveSpreadsheet().toast('Creating document mapping...', 'Processing', -1);
  
  try {
    const result = callWebApp('testDocumentMatching', {
      ...config,
      spreadsheetId: SpreadsheetApp.getActiveSpreadsheet().getId()
    });
    
    SpreadsheetApp.getActiveSpreadsheet().toast('Complete!', 'Success', 5);
    
    if (result.success) {
      ui.alert(
        'Document Mapping Complete',
        `Found ${result.summary.total} candidates:\n\n` +
        `• With both CV and cover letter: ${result.summary.withBoth}\n` +
        `• CV only: ${result.summary.cvOnly}\n\n` +
        `Check the "Document Mapping" sheet for details.`,
        ui.ButtonSet.OK
      );
      
      // Refresh sidebar
      showSidebar();
    } else {
      ui.alert('Error', result.error || 'Failed to create mapping', ui.ButtonSet.OK);
    }
    
  } catch (error) {
    SpreadsheetApp.getActiveSpreadsheet().toast('Failed', 'Error', 5);
    ui.alert('Error', 'Failed to create mapping: ' + error.toString(), ui.ButtonSet.OK);
  }
}

function viewMappingSummary() {
  const ui = SpreadsheetApp.getUi();
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const mappingSheet = ss.getSheetByName('Document Mapping');
  
  if (!mappingSheet || mappingSheet.getLastRow() <= 1) {
    ui.alert(
      'No Mapping',
      'No document mapping found.\n\n' +
      'Please create document mapping first.',
      ui.ButtonSet.OK
    );
    return;
  }
  
  const data = mappingSheet.getDataRange().getValues();
  let totalCandidates = 0;
  let withBoth = 0;
  let cvOnly = 0;
  
  for (let i = 1; i < data.length; i++) {
    if (data[i][0]) {
      totalCandidates++;
      if (data[i][3] === 'Both') {
        withBoth++;
      } else {
        cvOnly++;
      }
    }
  }
  
  let message = `DOCUMENT MAPPING SUMMARY\n`;
  message += `${'─'.repeat(30)}\n\n`;
  message += `Total Candidates: ${totalCandidates}\n`;
  message += `• With CV + Cover Letter: ${withBoth}\n`;
  message += `• CV Only: ${cvOnly}\n\n`;
  message += `Coverage: ${Math.round(withBoth/totalCandidates*100)}% have both documents\n\n`;
  message += `View details in the "Document Mapping" sheet`;
  
  ui.alert('Mapping Summary', message, ui.ButtonSet.OK);
}

// Continue with remaining functions (processNext5, processNext10, etc.)
// These remain the same but without processAllLegacy

function processNext5() {
  checkRubricBeforeProcessing(() => processBatchOfSize(5));
}

function processNext10() {
  checkRubricBeforeProcessing(() => processBatchOfSize(10));
}

function checkRubricBeforeProcessing(callback) {
  const ui = SpreadsheetApp.getUi();
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const configSheet = ss.getSheetByName('Position Configuration');
  
  let rubricLocked = false;
  if (configSheet) {
    for (let i = 2; i <= configSheet.getLastRow(); i++) {
      if (configSheet.getRange(i, 1).getValue() === 'Rubric Status' && 
          configSheet.getRange(i, 2).getValue() === 'LOCKED') {
        rubricLocked = true;
        break;
      }
    }
  }
  
  if (!rubricLocked) {
    ui.alert(
      'Rubric Not Ready',
      'Please complete the following steps first:\n\n' +
      '1. Load a Position Description\n' +
      '2. Generate Dynamic Rubric\n' +
      '3. Lock the Rubric\n\n' +
      'Then you can process candidates.',
      ui.ButtonSet.OK
    );
    return;
  }
  
  callback();
}

function processAllRemainingAuto() {
  checkRubricBeforeProcessing(() => {
    const ui = SpreadsheetApp.getUi();
    const unprocessed = getUnprocessedCandidatesLocal();
    
    if (unprocessed.length === 0) {
      ui.alert('Complete', 'All candidates have been processed!', ui.ButtonSet.OK);
      return;
    }
    
    const response = ui.alert(
      'Auto Batch Processing',
      `Found ${unprocessed.length} unprocessed candidates.\n\n` +
      `This will process them automatically in batches of 5.\n` +
      `The process will pause every 5 candidates to avoid timeouts.\n\n` +
      `Estimated time: ${Math.ceil(unprocessed.length / 5) * 2} minutes\n\n` +
      `Start processing?`,
      ui.ButtonSet.YES_NO
    );
    
    if (response === ui.Button.YES) {
      processAllInBatchesAuto(unprocessed);
    }
  });
}

function retryFailedCandidates() {
  checkRubricBeforeProcessing(() => {
    const ui = SpreadsheetApp.getUi();
    const config = getConfiguration();
    
    if (!config) {
      ui.alert('Setup Required', 'Please run Initial Setup first', ui.ButtonSet.OK);
      return;
    }
    
    const result = callWebApp('getFailedCandidates', {
      ...config,
      spreadsheetId: SpreadsheetApp.getActiveSpreadsheet().getId()
    });
    
    if (!result.success || result.count === 0) {
      ui.alert('No Failed Candidates', 'No failed candidates found to retry.', ui.ButtonSet.OK);
      return;
    }
    
    const response = ui.alert(
      'Retry Failed Candidates',
      `Found ${result.count} failed candidates:\n\n` +
      result.candidates.slice(0, 10).join('\n') +
      (result.count > 10 ? `\n... and ${result.count - 10} more` : '') +
      '\n\nRetry all failed candidates?',
      ui.ButtonSet.YES_NO
    );
    
    if (response === ui.Button.YES) {
      SpreadsheetApp.getActiveSpreadsheet().toast(
        `Retrying ${result.count} failed candidates...`,
        'Processing',
        -1
      );
      
      const retryResult = callWebApp('retryFailedCandidates', {
        ...config,
        spreadsheetId: SpreadsheetApp.getActiveSpreadsheet().getId()
      });
      
      SpreadsheetApp.getActiveSpreadsheet().toast('', 'Complete', 1);
      
if (retryResult.success) {
        ui.alert(
          'Retry Complete',
          retryResult.message + '\n\n' +
          `Successful: ${retryResult.stats.successful}\n` +
          `Failed: ${retryResult.stats.failed}`,
          ui.ButtonSet.OK
        );
        
        // Refresh sidebar
        showSidebar();
      } else {
        ui.alert('Error', retryResult.error || 'Retry failed', ui.ButtonSet.OK);
      }
    }
  });
}

function processBatchOfSize(batchSize) {
  const ui = SpreadsheetApp.getUi();
  const config = getConfiguration();
  
  if (!config) {
    ui.alert('Setup Required', 'Please run Initial Setup first', ui.ButtonSet.OK);
    return;
  }
  
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  if (!ss.getSheetByName('Processing Log')) {
    ui.alert(
      'Sheets Not Setup',
      'Please run "Create All Sheets" first to create the required sheets.',
      ui.ButtonSet.OK
    );
    return;
  }
  
  const unprocessed = getUnprocessedCandidatesLocal();
  
  if (unprocessed.length === 0) {
    ui.alert('Complete', 'All candidates have been processed!', ui.ButtonSet.OK);
    return;
  }
  
  const actualBatchSize = Math.min(batchSize, unprocessed.length);
  const candidatesToProcess = unprocessed.slice(0, actualBatchSize);
  
  const response = ui.alert(
    'Process Batch',
    `Ready to process ${actualBatchSize} candidates:\n\n` +
    candidatesToProcess.map((name, i) => `${i + 1}. ${name}`).join('\n') +
    '\n\nProceed?',
    ui.ButtonSet.YES_NO
  );
  
  if (response === ui.Button.YES) {
    processBatch(batchSize);
  }
}

function processAllInBatchesAuto(candidates) {
  const ui = SpreadsheetApp.getUi();
  const config = getConfiguration();
  const batchSize = 5;
  let currentIndex = 0;
  
  function processNextBatch() {
    if (currentIndex >= candidates.length) {
      ui.alert('Complete!', '🎉 All candidates have been processed!', ui.ButtonSet.OK);
      updateStatusSheet('All candidates processed successfully');
      showSidebar();
      return;
    }
    
    const remainingCandidates = candidates.length - currentIndex;
    const currentBatchSize = Math.min(batchSize, remainingCandidates);
    const batchNumber = Math.floor(currentIndex / batchSize) + 1;
    const totalBatches = Math.ceil(candidates.length / batchSize);
    
    updateStatusSheet(`Processing batch ${batchNumber} of ${totalBatches}...`);
    
    SpreadsheetApp.getActiveSpreadsheet().toast(
      `Processing batch ${batchNumber} of ${totalBatches} (${currentBatchSize} candidates)...`,
      'Auto Processing',
      -1
    );
    
    const result = callWebApp('processBatchSize', {
      ...config,
      spreadsheetId: SpreadsheetApp.getActiveSpreadsheet().getId(),
      batchSize: currentBatchSize
    });
    
    SpreadsheetApp.getActiveSpreadsheet().toast('', 'Complete', 1);
    
    if (!result.success) {
      const response = ui.alert(
        'Batch Error',
        `Batch ${batchNumber} encountered an error:\n${result.error}\n\n` +
        `Continue with next batch?`,
        ui.ButtonSet.YES_NO
      );
      
      if (response !== ui.Button.YES) {
        updateStatusSheet('Batch processing stopped by user');
        return;
      }
    } else {
      currentIndex += result.stats.successful + result.stats.failed;
    }
    
    if (result.hasMore) {
      ui.alert(
        'Batch Progress',
        `Batch ${batchNumber} complete!\n\n` +
        `Processed: ${currentIndex} of ${candidates.length}\n` +
        `Next batch will start in 5 seconds...`,
        ui.ButtonSet.OK
      );
      
      Utilities.sleep(5000);
      processNextBatch();
    } else {
      ui.alert('Complete!', '🎉 All candidates have been processed!', ui.ButtonSet.OK);
      updateStatusSheet('All candidates processed successfully');
      showSidebar();
    }
  }
  
  processNextBatch();
}

// ============================================
// MAINTENANCE FUNCTIONS
// ============================================

function clearAllData() {
  const ui = SpreadsheetApp.getUi();
  
  const response = ui.alert(
    '🗑️ Clear All Data',
    'This will DELETE all:\n' +
    '• Evaluation data\n' +
    '• Position configuration\n' +
    '• Dynamic rubric\n' +
    '• Document mappings\n' +
    '• Processing logs\n' +
    '• API usage tracking\n\n' +
    'Configuration settings will be preserved.\n\n' +
    'Are you sure you want to continue?',
    ui.ButtonSet.YES_NO
  );
  
  if (response === ui.Button.YES) {
    const confirmResponse = ui.alert(
      '⚠️ Final Confirmation',
      'This action CANNOT be undone.\n\n' +
      'Really delete all data?',
      ui.ButtonSet.YES_NO
    );
    
    if (confirmResponse === ui.Button.YES) {
      SpreadsheetApp.getActiveSpreadsheet().toast('Clearing all data...', 'Processing', -1);
      
      try {
        const result = callWebApp('clearAllData', {
          spreadsheetId: SpreadsheetApp.getActiveSpreadsheet().getId()
        });
        
        SpreadsheetApp.getActiveSpreadsheet().toast('', 'Complete', 1);
        
        if (result.success) {
          ui.alert(
            'Data Cleared',
            result.message + '\n\n' +
            'The system is now ready for a new position and evaluation cycle.',
            ui.ButtonSet.OK
          );
          
          // Refresh sidebar
          showSidebar();
        } else {
          ui.alert('Error', result.error || 'Failed to clear data', ui.ButtonSet.OK);
        }
        
      } catch (error) {
        SpreadsheetApp.getActiveSpreadsheet().toast('', 'Error', 1);
        ui.alert('Error', 'Failed to clear data: ' + error.toString(), ui.ButtonSet.OK);
      }
    }
  }
}

function viewAPIUsage() {
  const ui = SpreadsheetApp.getUi();
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const sheet = ss.getSheetByName('API Usage Tracking');
  
  if (!sheet || sheet.getLastRow() <= 1) {
    ui.alert(
      'No API Usage Data',
      'No API usage has been tracked yet.\n\n' +
      'API usage will be tracked when you:\n' +
      '• Generate a dynamic rubric\n' +
      '• Process candidates',
      ui.ButtonSet.OK
    );
    return;
  }
  
  const dataRange = sheet.getRange(2, 4, sheet.getLastRow() - 1, 6);
  const data = dataRange.getValues();
  
  let totalInputTokens = 0;
  let totalOutputTokens = 0;
  let totalCost = 0;
  let callCount = 0;
  let rubricCalls = 0;
  let rubricCost = 0;
  
  data.forEach(row => {
    if (row[0]) {
      totalInputTokens += row[0] || 0;
      totalOutputTokens += row[1] || 0;
      totalCost += parseFloat(row[5]) || 0;
      callCount++;
      
      const callType = sheet.getRange(callCount + 1, 3).getValue();
      if (callType && callType.includes('Rubric')) {
        rubricCalls++;
        rubricCost += parseFloat(row[5]) || 0;
      }
    }
  });
  
  const avgCostPerCandidate = callCount > rubricCalls ? 
    (totalCost - rubricCost) / ((callCount - rubricCalls) / 2) : 0;
  
  let message = '💰 API USAGE & COSTS\n';
  message += `${'─'.repeat(30)}\n\n`;
  
  message += `📊 TOTALS:\n`;
  message += `• API Calls: ${callCount}\n`;
  message += `• Input Tokens: ${totalInputTokens.toLocaleString()}\n`;
  message += `• Output Tokens: ${totalOutputTokens.toLocaleString()}\n`;
  message += `• Total Tokens: ${(totalInputTokens + totalOutputTokens).toLocaleString()}\n\n`;
  
  message += `💵 COSTS:\n`;
  message += `• Total Cost: $${totalCost.toFixed(4)}\n`;
  message += `• Rubric Generation: $${rubricCost.toFixed(4)}\n`;
  message += `• Avg per Candidate: $${avgCostPerCandidate.toFixed(4)}\n\n`;
  
  message += `📈 PRICING:\n`;
  message += `• Input: $3.00 per million tokens\n`;
  message += `• Output: $15.00 per million tokens\n\n`;
  
  message += `Check "API Usage Tracking" sheet for details.`;
  
  ui.alert('API Usage Summary', message, ui.ButtonSet.OK);
}

// ============================================
// STATUS AND REPORTING FUNCTIONS
// ============================================

function checkProcessingStatus() {
  const ui = SpreadsheetApp.getUi();
  const config = getConfiguration();
  
  if (!config) {
    ui.alert('Setup Required', 'Please run Initial Setup first', ui.ButtonSet.OK);
    return;
  }
  
  const result = callWebApp('getProcessingStatus', {
    ...config,
    spreadsheetId: SpreadsheetApp.getActiveSpreadsheet().getId()
  });
  
  if (result.success && result.stats) {
    const stats = result.stats;
    
    let message = `📊 PROCESSING STATUS\n`;
    message += `${'─'.repeat(30)}\n\n`;
    
    message += `📁 Total Candidates: ${stats.total}\n`;
    message += `✅ Processed: ${stats.processed}\n`;
    message += `❌ Failed: ${stats.failed}\n`;
    message += `⏳ Remaining: ${stats.remaining}\n`;
    message += `📈 Average Score: ${stats.averageScore ? stats.averageScore.toFixed(1) : 'N/A'}\n\n`;
    
    if (stats.recommendations && Object.keys(stats.recommendations).length > 0) {
      message += `RECOMMENDATIONS:\n`;
      Object.entries(stats.recommendations).forEach(([rec, count]) => {
        const emoji = rec.includes('Strong') ? '🌟' : 
                     rec.includes('Good') ? '✅' : 
                     rec.includes('Developing') ? '🔄' : '❌';
        message += `${emoji} ${rec}: ${count}\n`;
      });
    }
    
    ui.alert('Processing Status', message, ui.ButtonSet.OK);
  } else {
    ui.alert('Error', 'Could not retrieve status', ui.ButtonSet.OK);
  }
}

function viewUnprocessedCandidates() {
  const ui = SpreadsheetApp.getUi();
  const config = getConfiguration();
  
  if (!config) {
    ui.alert('Setup Required', 'Please run Initial Setup first', ui.ButtonSet.OK);
    return;
  }
  
  const result = callWebApp('getUnprocessedCandidates', {
    ...config,
    spreadsheetId: SpreadsheetApp.getActiveSpreadsheet().getId()
  });
  
  if (result.success) {
    if (result.count === 0) {
      ui.alert('All Done!', '✅ All candidates have been processed!', ui.ButtonSet.OK);
      return;
    }
    
    let message = `📋 UNPROCESSED CANDIDATES (${result.count})\n`;
    message += `${'─'.repeat(30)}\n\n`;
    
    const displayCount = Math.min(20, result.candidates.length);
    for (let i = 0; i < displayCount; i++) {
      message += `${i + 1}. ${result.candidates[i]}\n`;
    }
    
    if (result.count > 20) {
      message += `\n... and ${result.count - 20} more`;
    }
    
    message += `\n\n💡 Tip: Use "Process Next 5" to start processing`;
    
    ui.alert('Unprocessed Candidates', message, ui.ButtonSet.OK);
  } else {
    ui.alert('Error', 'Could not retrieve unprocessed candidates', ui.ButtonSet.OK);
  }
}

function viewFailedCandidates() {
  const ui = SpreadsheetApp.getUi();
  const config = getConfiguration();
  
  if (!config) {
    ui.alert('Setup Required', 'Please run Initial Setup first', ui.ButtonSet.OK);
    return;
  }
  
  const result = callWebApp('getFailedCandidates', {
    ...config,
    spreadsheetId: SpreadsheetApp.getActiveSpreadsheet().getId()
  });
  
  if (result.success) {
    if (result.count === 0) {
      ui.alert('No Failures', '✅ No failed candidates found!', ui.ButtonSet.OK);
      return;
    }
    
    let message = `❌ FAILED CANDIDATES (${result.count})\n`;
    message += `${'─'.repeat(30)}\n\n`;
    
    result.candidates.forEach((name, i) => {
      if (i < 15) {
        message += `${i + 1}. ${name}\n`;
      }
    });
    
    if (result.count > 15) {
      message += `\n... and ${result.count - 15} more`;
    }
    
    message += `\n\n💡 Tip: Use "Retry Failed Candidates" to reprocess`;
    
    ui.alert('Failed Candidates', message, ui.ButtonSet.OK);
  } else {
    ui.alert('Error', 'Could not retrieve failed candidates', ui.ButtonSet.OK);
  }
}

function generateSummaryReport() {
  const ui = SpreadsheetApp.getUi();
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  
  let reportSheet = ss.getSheetByName('Evaluation Report');
  if (!reportSheet) {
    reportSheet = ss.insertSheet('Evaluation Report');
  }
  
  reportSheet.clear();
  
  reportSheet.getRange(1, 1).setValue('RubiRecruit Evaluation Report')
    .setFontSize(18).setFontWeight('bold');
  reportSheet.getRange(2, 1).setValue(new Date().toLocaleString())
    .setFontSize(12).setFontColor('#666666');
  
  const configSheet = ss.getSheetByName('Position Configuration');
  let positionTitle = 'Unknown Position';
  if (configSheet && configSheet.getLastRow() > 1) {
    for (let i = 2; i <= configSheet.getLastRow(); i++) {
      if (configSheet.getRange(i, 1).getValue() === 'Position Title') {
        positionTitle = configSheet.getRange(i, 2).getValue();
        break;
      }
    }
  }
  
  reportSheet.getRange(3, 1).setValue(`Position: ${positionTitle}`)
    .setFontSize(14).setFontWeight('bold');
  
  const stats = getProcessingStatisticsLocal();
  
  let row = 5;
  reportSheet.getRange(row++, 1).setValue('SUMMARY STATISTICS')
    .setFontSize(14).setFontWeight('bold');
  
  const summaryData = [
    ['Total Candidates', stats.total],
    ['Processed', stats.processed],
    ['Failed', stats.failed],
    ['Average Score', stats.avgScore],
    ['Strong Candidates (80+)', stats.strongCandidates],
    ['Good Candidates (70-79)', stats.goodCandidates],
    ['Developing Candidates (60-69)', stats.developingCandidates],
    ['Not Suitable (<60)', stats.notSuitable]
  ];
  
  reportSheet.getRange(row, 1, summaryData.length, 2).setValues(summaryData);
  row += summaryData.length + 2;
  
  reportSheet.getRange(row++, 1).setValue('TOP 10 CANDIDATES')
    .setFontSize(14).setFontWeight('bold');
  
  const topCandidates = getTopCandidatesLocal(10);
  if (topCandidates.length > 0) {
    const headers = ['Rank', 'Name', 'Score', 'Recommendation'];
    reportSheet.getRange(row++, 1, 1, headers.length).setValues([headers]).setFontWeight('bold');
    
    const candidateData = topCandidates.map((c, i) => [
      i + 1,
      c.name,
      c.score,
      c.recommendation
    ]);
    
    reportSheet.getRange(row, 1, candidateData.length, 4).setValues(candidateData);
    
    candidateData.forEach((candidate, i) => {
      const scoreCell = reportSheet.getRange(row + i, 3);
      const score = candidate[2];
      
      if (score >= 80) {
        scoreCell.setBackground('#4CAF50').setFontColor('#FFFFFF');
      } else if (score >= 70) {
        scoreCell.setBackground('#8BC34A');
      } else if (score >= 60) {
        scoreCell.setBackground('#FFC107');
      } else {
        scoreCell.setBackground('#FF9800').setFontColor('#FFFFFF');
      }
    });
  }
  
  ui.alert('Report Generated', 'Summary report has been created in the "Evaluation Report" sheet', ui.ButtonSet.OK);
}

function viewProcessingLog() {
  const ui = SpreadsheetApp.getUi();
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const logSheet = ss.getSheetByName('Processing Log');
  
  if (!logSheet) {
    ui.alert('No Log', 'Processing Log sheet not found.', ui.ButtonSet.OK);
    return;
  }
  
  const lastRow = logSheet.getLastRow();
  const startRow = Math.max(2, lastRow - 19);
  const numRows = Math.min(20, lastRow - 1);
  
  if (numRows <= 0) {
    ui.alert('Empty Log', 'No log entries found.', ui.ButtonSet.OK);
    return;
  }
  
  const logData = logSheet.getRange(startRow, 1, numRows, 3).getValues();
  
  let message = '📜 RECENT PROCESSING LOG\n';
  message += `${'─'.repeat(30)}\n\n`;
  
  logData.forEach(entry => {
    const timestamp = new Date(entry[0]).toLocaleTimeString();
    const level = entry[2];
    const icon = level === 'Error' ? '❌' : 
                level === 'Success' ? '✅' : 
                level === 'Warning' ? '⚠️' : 'ℹ️';
    
    message += `${icon} ${timestamp}: ${entry[1].substring(0, 50)}${entry[1].length > 50 ? '...' : ''}\n`;
  });
  
  ui.alert('Processing Log', message, ui.ButtonSet.OK);
}

// ============================================
// MAIN PROCESSING FUNCTION
// ============================================

function processBatch(batchSize = 5) {
  const ui = SpreadsheetApp.getUi();
  const config = getConfiguration();
  
  SpreadsheetApp.getActiveSpreadsheet().toast(
    `Processing batch of up to ${batchSize} candidates...`,
    'RubiRecruit',
    -1
  );
  
  try {
    const result = callWebApp('processBatchSize', {
      ...config,
      spreadsheetId: SpreadsheetApp.getActiveSpreadsheet().getId(),
      batchSize: batchSize
    });
    
    SpreadsheetApp.getActiveSpreadsheet().toast('', 'Complete', 1);
    
    if (result.success) {
      handleBatchResult(result, ui);
    } else {
      handleProcessingError(result, ui);
    }
    
  } catch (error) {
    SpreadsheetApp.getActiveSpreadsheet().toast('', 'Error', 1);
    ui.alert('Error', 'Failed to process batch: ' + error.toString(), ui.ButtonSet.OK);
  }
}

function handleBatchResult(result, ui) {
  if (result.stats.hasMore) {
    const continueResponse = ui.alert(
      'Batch Complete',
      `Processed ${result.stats.successful} candidates successfully.\n\n` +
      `Progress: ${result.stats.processed} of ${result.stats.total} total candidates\n` +
      `Remaining: ${result.stats.remaining} candidates\n\n` +
      `Continue with next batch of ${result.nextBatchSize} candidates?`,
      ui.ButtonSet.YES_NO
    );
    
    if (continueResponse === ui.Button.YES) {
      processBatch(result.nextBatchSize);
    } else {
      ui.alert(
        'Processing Paused',
        `You can resume processing at any time using:\n` +
        `"RubiRecruit" → "Processing" → "Process Next 5"\n\n` +
        `Progress saved: ${result.stats.processed} of ${result.stats.total} completed`,
        ui.ButtonSet.OK
      );
    }
  } else {
    ui.alert(
      'Batch Processing Complete!',
      `Successfully evaluated selected candidates.\n\n` +
      `Check the Summary Dashboard for results.\n\n` +
      `You can:\n` +
      `• Review individual scores in "Candidate Evaluations"\n` +
      `• See detailed analysis in "Detailed Evaluations"\n` +
      `• Check metrics in "Company Metrics"`,
      ui.ButtonSet.OK
    );
    
    // Refresh sidebar
    showSidebar();
  }
}

// ============================================
// SHEET SETUP
// ============================================

function setupAllSheets() {
  const ui = SpreadsheetApp.getUi();
  
  const config = getConfiguration();
  if (!config) {
    ui.alert(
      'Setup Required',
      'Please run Initial Setup first.\n\n' +
      'Go to: RubiRecruit → Initial Setup → Run Initial Setup',
      ui.ButtonSet.OK
    );
    return;
  }
  
  SpreadsheetApp.getActiveSpreadsheet().toast('Creating evaluation sheets...', 'Setup', -1);
  
  try {
    const result = callWebApp('setupSheets', {
      spreadsheetId: SpreadsheetApp.getActiveSpreadsheet().getId()
    });
    
    SpreadsheetApp.getActiveSpreadsheet().toast('Setup complete!', 'Success', 5);
    
    if (result.success) {
      // Format all sheets
      formatAllSheets();
      
      ui.alert(
        'Setup Complete',
        `Created ${result.sheets.length} sheets successfully:\n\n` +
        `• Position Configuration\n` +
        `• Dynamic Rubric Configuration\n` +
        `• Company Metrics\n` +
        `• Candidate Evaluations\n` +
        `• Detailed Evaluations\n` +
        `• Combined Analysis\n` +
        `• Summary Dashboard\n` +
        `• Processing Log\n` +
        `• Processing Status\n` +
        `• Document Mapping\n` +
        `• API Usage Tracking\n\n` +
        `Next: Create document mapping to begin!`,
        ui.ButtonSet.OK
      );
    } else {
      handleProcessingError(result, ui);
    }
    
  } catch (error) {
    SpreadsheetApp.getActiveSpreadsheet().toast('Setup failed', 'Error', 5);
    
    ui.alert(
      'Setup Failed',
      'Failed to setup sheets:\n\n' + error.toString(),
      ui.ButtonSet.OK
    );
  }
}

// ============================================
// DIAGNOSTIC FUNCTIONS
// ============================================

function testConnection() {
  const ui = SpreadsheetApp.getUi();
  
  if (!WEB_APP_URL || WEB_APP_URL === 'https://script.google.com/macros/s/YOUR_WEB_APP_ID/exec') {
    ui.alert(
      'Configuration Error',
      'Please update WEB_APP_URL in the script with your web app URL',
      ui.ButtonSet.OK
    );
    return;
  }
  
  SpreadsheetApp.getActiveSpreadsheet().toast('Testing connection...', 'Testing', -1);
  
  try {
    const result = callWebApp('test', {
      spreadsheetId: SpreadsheetApp.getActiveSpreadsheet().getId()
    });
    
    SpreadsheetApp.getActiveSpreadsheet().toast('Complete!', 'Success', 5);
    
    if (result.success) {
      ui.alert(
        'Connection Successful!',
        `Connected to RubiRecruit Service\n\n` +
        `Service Version: ${result.version}\n` +
        `Timestamp: ${result.timestamp}\n\n` +
        `Connection is working properly.`,
        ui.ButtonSet.OK
      );
    } else {
      ui.alert('Connection Failed', result.error || 'Unknown error', ui.ButtonSet.OK);
    }
    
  } catch (error) {
    SpreadsheetApp.getActiveSpreadsheet().toast('Failed', 'Error', 5);
    ui.alert('Error', 'Connection test failed: ' + error.toString(), ui.ButtonSet.OK);
  }
}

function validateApiKey() {
  const ui = SpreadsheetApp.getUi();
  const config = getConfiguration();
  
  if (!config || !config.claudeApiKey) {
    ui.alert('Setup Required', 'Please run Initial Setup first', ui.ButtonSet.OK);
    return;
  }
  
  SpreadsheetApp.getActiveSpreadsheet().toast('Validating API key...', 'Testing', -1);
  
  try {
    const result = callWebApp('validateApiKey', {
      claudeApiKey: config.claudeApiKey
    });
    
    SpreadsheetApp.getActiveSpreadsheet().toast('Complete!', 'Success', 5);
    
    if (result.success) {
      ui.alert(
        'API Key Valid',
        'Your Claude API key is working correctly!\n\n' +
        'You can proceed with generating rubrics and evaluating candidates.',
        ui.ButtonSet.OK
      );
    } else {
      ui.alert(
        'API Key Invalid',
        'Your Claude API key appears to be invalid.\n\n' +
        'Please check:\n' +
        '• The key is correct (no extra spaces)\n' +
        '• The key has not expired\n' +
        '• You have available credits\n\n' +
        'Run Initial Setup again to update the key.',
        ui.ButtonSet.OK
      );
    }
    
  } catch (error) {
    SpreadsheetApp.getActiveSpreadsheet().toast('Failed', 'Error', 5);
    ui.alert('Error', 'Validation failed: ' + error.toString(), ui.ButtonSet.OK);
  }
}

function checkPermissions() {
  const ui = SpreadsheetApp.getUi();
  
  SpreadsheetApp.getActiveSpreadsheet().toast('Checking permissions...', 'Testing', -1);
  
  try {
    const result = callWebApp('checkPermissions', {
      spreadsheetId: SpreadsheetApp.getActiveSpreadsheet().getId()
    });
    
    SpreadsheetApp.getActiveSpreadsheet().toast('Complete!', 'Success', 5);
    
    if (result.success && result.hasAccess) {
      ui.alert(
        'Permissions OK',
        'The evaluation service has access to your spreadsheet.\n\n' +
        `Service account: ${result.serviceAccount}\n\n` +
        'All permissions are properly configured.',
        ui.ButtonSet.OK
      );
    } else {
      ui.alert(
        'Permission Required',
        'The evaluation service needs access to your spreadsheet.\n\n' +
        `Please share this spreadsheet with:\n${result.serviceAccount}\n\n` +
        'Steps:\n' +
        '1. Click the Share button (top right)\n' +
        '2. Add the email above\n' +
        '3. Set permission to "Editor"\n' +
        '4. Click Send\n' +
        '5. Try this operation again',
        ui.ButtonSet.OK
      );
    }
  } catch (error) {
    SpreadsheetApp.getActiveSpreadsheet().toast('Failed', 'Error', 5);
    ui.alert('Error', 'Failed to check permissions: ' + error.toString(), ui.ButtonSet.OK);
  }
}

function checkSetupStatus() {
  const ui = SpreadsheetApp.getUi();
  const props = PropertiesService.getDocumentProperties();
  
  const apiKey = props.getProperty('CLAUDE_API_KEY');
  const positionFolderId = props.getProperty('POSITION_FOLDER_ID');
  const cvFolderId = props.getProperty('CV_FOLDER_ID');
  const companyId = props.getProperty('COMPANY_ID');
  const setupDate = props.getProperty('SETUP_DATE');
  const version = props.getProperty('VERSION');
  
  let status = 'SETUP STATUS\n\n';
  
  if (!apiKey || !cvFolderId) {
    status += '❌ Setup NOT complete\n\n';
    status += 'Missing:\n';
    if (!apiKey) status += '• Claude API Key\n';
    if (!positionFolderId) status += '• Position Folder ID\n';
    if (!cvFolderId) status += '• CV Folder ID\n';
    status += '\nPlease run Initial Setup from the menu.';
  } else {
    status += '✅ Setup is complete\n\n';
    status += 'Configuration:\n';
    status += `• Company: ${companyId}\n`;
    status += `• Position Folder: ${positionFolderId}\n`;
    status += `• CV Folder: ${cvFolderId}\n`;
    status += `• Setup Date: ${new Date(setupDate).toLocaleDateString()}\n`;
    status += `• Version: ${version || 'Unknown'}\n\n`;
    
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const requiredSheets = [
      'Processing Log',
      'Company Metrics',
      'Candidate Evaluations',
      'Document Mapping',
      'Processing Status',
      'API Usage Tracking',
      'Position Configuration',
      'Dynamic Rubric Configuration'
    ];
    
    const missingSheets = requiredSheets.filter(name => !ss.getSheetByName(name));
    
    if (missingSheets.length > 0) {
      status += '⚠️ Missing sheets:\n';
      missingSheets.forEach(name => status += `• ${name}\n`);
      status += '\nRun "Create All Sheets" to create them.';
    } else {
      status += '✅ All required sheets present\n\n';
      
      const configSheet = ss.getSheetByName('Position Configuration');
      if (configSheet && configSheet.getLastRow() > 1) {
        for (let i = 2; i <= configSheet.getLastRow(); i++) {
          if (configSheet.getRange(i, 1).getValue() === 'Rubric Status') {
            const rubricStatus = configSheet.getRange(i, 2).getValue();
            if (rubricStatus === 'LOCKED') {
              status += '✅ Rubric is locked and ready\n';
            } else if (rubricStatus === 'Generated - Not Locked') {
              status += '⚠️ Rubric generated but not locked\n';
            } else {
              status += '⚠️ No rubric generated yet\n';
            }
            break;
          }
        }
      } else {
        status += '⚠️ No position loaded yet\n';
      }
      
      status += '\nReady to process candidates!';
    }
  }
  
  ui.alert('Setup Status', status, ui.ButtonSet.OK);
}

// ============================================
// DASHBOARD UPDATE
// ============================================

function updateDashboard() {
  const ui = SpreadsheetApp.getUi();
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  
  const dashboardSheet = ss.getSheetByName('Summary Dashboard');
  if (!dashboardSheet) {
    ui.alert(
      'Dashboard Not Found',
      'Summary Dashboard sheet not found.\n' +
      'Please run "Create All Sheets" first.',
      ui.ButtonSet.OK
    );
    return;
  }
  
  SpreadsheetApp.getActiveSpreadsheet().toast('Updating dashboard...', 'Processing', 5);
  
  const response = dashboardSheet.getRange(2, 1).getValue();
  dashboardSheet.getRange(2, 1).setValue('Refreshing...');
  Utilities.sleep(100);
  dashboardSheet.getRange(2, 1).setValue(new Date().toLocaleString());
  
  ui.alert(
    'Dashboard Updated',
    'The Summary Dashboard has been refreshed.\n\n' +
    'It shows:\n' +
    '• Overall statistics\n' +
    '• All candidates ranked by score\n' +
    '• Score distribution',
    ui.ButtonSet.OK
  );
}

function verifyAllSheets() {
  const ui = SpreadsheetApp.getUi();
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  
  if (checkIfAllSheetsExist(ss)) {
    ui.alert(
      '✅ All Sheets Present',
      'All required sheets have been created successfully.',
      ui.ButtonSet.OK
    );
  } else {
    ui.alert(
      '⚠️ Missing Sheets',
      'Some sheets are missing. Run "Create All Sheets" to create them.',
      ui.ButtonSet.OK
    );
  }
}

function viewCurrentPosition() {
  const ui = SpreadsheetApp.getUi();
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const configSheet = ss.getSheetByName('Position Configuration');
  
  if (!configSheet || configSheet.getLastRow() < 2) {
    ui.alert(
      'No Position Loaded',
      'No position description has been loaded yet.',
      ui.ButtonSet.OK
    );
    return;
  }
  
  let positionTitle = '';
  let loadDate = '';
  let textLength = '';
  
  for (let i = 2; i <= configSheet.getLastRow(); i++) {
    const field = configSheet.getRange(i, 1).getValue();
    if (field === 'Position Title') {
      positionTitle = configSheet.getRange(i, 2).getValue();
    } else if (field === 'Load Date') {
      loadDate = configSheet.getRange(i, 2).getValue();
    } else if (field === 'Text Length') {
      textLength = configSheet.getRange(i, 2).getValue();
    }
  }
  
  ui.alert(
    '📄 Current Position',
    `Position: ${positionTitle}\n` +
    `Loaded: ${loadDate}\n` +
    `Size: ${textLength}`,
    ui.ButtonSet.OK
  );
}

function viewAnalytics() {
  const ui = SpreadsheetApp.getUi();
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  
  const dashboardSheet = ss.getSheetByName('Summary Dashboard');
  if (dashboardSheet) {
    dashboardSheet.activate();
    ui.alert(
      '📈 Analytics Dashboard',
      'The Summary Dashboard sheet is now active.\n\n' +
      'Review your evaluation statistics and candidate rankings.',
      ui.ButtonSet.OK
    );
  } else {
    ui.alert(
      'Dashboard Not Found',
      'Please run "Create All Sheets" first.',
      ui.ButtonSet.OK
    );
  }
}

// ============================================
// ABOUT
// ============================================

function showAbout() {
  const ui = SpreadsheetApp.getUi();
  const config = getConfiguration();
  
  let message = '🚀 RubiRecruit™ v7.0.0\n';
  message += 'AI-Powered Recruitment Excellence\n\n';
  
  message += '✨ KEY FEATURES:\n';
  message += '• Dynamic rubric generation with weighted categories\n';
  message += '• 8 position-specific evaluation categories\n';
  message += '• AI-powered semantic evaluation\n';
  message += '• Smart document matching\n';
  message += '• Batch processing with auto-retry\n';
  message += '• API cost tracking\n';
  message += '• Interactive 8-step journey sidebar\n';
  message += '• Comprehensive reporting\n\n';
  
  message += '🔄 8-STEP WORKFLOW:\n';
  message += '1. Initial Setup - Configure API & folders\n';
  message += '2. Create Sheets - Setup evaluation framework\n';
  message += '3. Load Position - Select job description\n';
  message += '4. Document Mapping - Match CVs with covers\n';
  message += '5. Generate Rubric - AI creates criteria\n';
  message += '6. Lock Rubric - Finalize for fairness\n';
  message += '7. Process Candidates - Evaluate applications\n';
  message += '8. Review Results - Analyze insights\n\n';
  
  if (config) {
    message += '⚙️ Current Configuration:\n';
    message += `• Company: ${config.customerIdentifier}\n`;
    message += `• Position Folder: ${config.positionFolderId}\n`;
    message += `• CV Folder: ${config.cvFolderId}\n`;
    message += `• Setup Date: ${new Date(config.setupDate || '').toLocaleDateString()}\n`;
  } else {
    message += 'Status: Not configured\n';
    message += 'Run Initial Setup to begin';
  }
  
  ui.alert('About RubiRecruit™', message, ui.ButtonSet.OK);
}

function showTermsAndConditions() {
  const ui = SpreadsheetApp.getUi();
  
  ui.alert(
    '⚖️ Terms & Conditions',
    `RUBIRECRUIT™ TERMS & CONDITIONS\n\n` +
    `1. NO WARRANTY: This software is provided "as is" without any warranties.\n\n` +
    `2. LIMITATION OF LIABILITY: Developers are not liable for any damages arising from use of this software.\n\n` +
    `3. AI DISCLAIMER: Evaluations are for decision support only. Human judgment required for final decisions.\n\n` +
    `4. DATA PRIVACY: You are responsible for compliance with all data protection laws.\n\n` +
    `5. API COSTS: You are responsible for all API usage costs.\n\n` +
    `6. COMPLIANCE: You must comply with all employment laws and fair hiring practices.\n\n` +
    `7. INDEMNIFICATION: You agree to hold developers harmless from any claims.\n\n` +
    `8. NO PROFESSIONAL ADVICE: This is not legal or HR advice.\n\n` +
    `9. LICENSE: Valid license required for continued use.\n\n` +
    `Terms accepted on: ${PropertiesService.getDocumentProperties().getProperty('TERMS_ACCEPTED_DATE') || 'Not yet accepted'}`,
    ui.ButtonSet.OK
  );
}

// ============================================
// HELPER FUNCTIONS
// ============================================

function getUnprocessedCandidatesLocal() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const processedNames = getProcessedCandidateNamesLocal();
  
  const mappingSheet = ss.getSheetByName('Document Mapping');
  if (!mappingSheet || mappingSheet.getLastRow() <= 1) {
    return [];
  }
  
  const mappingData = mappingSheet.getDataRange().getValues();
  const allCandidates = [];
  
  for (let i = 1; i < mappingData.length; i++) {
    const candidateName = mappingData[i][0];
    if (candidateName && !processedNames.has(candidateName.toLowerCase())) {
      allCandidates.push(candidateName);
    }
  }
  
  return allCandidates;
}

function getProcessedCandidateNamesLocal() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const evaluationsSheet = ss.getSheetByName('Candidate Evaluations');
  const processedNames = new Set();
  
  if (!evaluationsSheet || evaluationsSheet.getLastRow() <= 1) {
    return processedNames;
  }
  
  const data = evaluationsSheet.getDataRange().getValues();
  
  for (let i = 1; i < data.length; i++) {
    if (data[i][1]) {
      processedNames.add(data[i][1].toString().toLowerCase());
    }
  }
  
  return processedNames;
}

function getProcessingStatisticsLocal() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  
  const mappingSheet = ss.getSheetByName('Document Mapping');
  const total = mappingSheet ? Math.max(0, mappingSheet.getLastRow() - 1) : 0;
  
  const evalSheet = ss.getSheetByName('Candidate Evaluations');
  const processed = evalSheet ? Math.max(0, evalSheet.getLastRow() - 1) : 0;
  
  let failed = 0;
  const statusSheet = ss.getSheetByName('Processing Status');
  if (statusSheet && statusSheet.getLastRow() > 1) {
    const statusData = statusSheet.getDataRange().getValues();
    const processedNames = getProcessedCandidateNamesLocal();
    
    for (let i = 1; i < statusData.length; i++) {
      if (statusData[i][2] === 'Failed' && !processedNames.has(statusData[i][1].toLowerCase())) {
        failed++;
      }
    }
  }
  
  const stats = {
    total: total,
    processed: processed,
    failed: failed,
    avgScore: 'N/A',
    strongCandidates: 0,
    goodCandidates: 0,
    developingCandidates: 0,
    notSuitable: 0
  };
  
  if (evalSheet && evalSheet.getLastRow() > 1) {
    const data = evalSheet.getDataRange().getValues();
    const scores = [];
    
    for (let i = 1; i < data.length; i++) {
      const score = parseFloat(data[i][2]);
      if (!isNaN(score)) {
        scores.push(score);
        
        if (score >= 80) stats.strongCandidates++;
        else if (score >= 70) stats.goodCandidates++;
        else if (score >= 60) stats.developingCandidates++;
        else stats.notSuitable++;
      }
    }
    
    if (scores.length > 0) {
      stats.avgScore = (scores.reduce((a, b) => a + b, 0) / scores.length).toFixed(1);
    }
  }
  
  return stats;
}

function getTopCandidatesLocal(count) {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const evalSheet = ss.getSheetByName('Candidate Evaluations');
  
  if (!evalSheet || evalSheet.getLastRow() <= 1) {
    return [];
  }
  
  const data = evalSheet.getDataRange().getValues();
  const candidates = [];
  
  for (let i = 1; i < data.length; i++) {
    if (data[i][1] && data[i][2]) {
      candidates.push({
        name: data[i][1],
        score: parseFloat(data[i][2]),
        recommendation: data[i][11]
      });
    }
  }
  
  candidates.sort((a, b) => b.score - a.score);
  
  return candidates.slice(0, count);
}

function updateStatusSheet(status) {
  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const summarySheet = ss.getSheetByName('Summary Dashboard');
    
    if (summarySheet) {
      const now = new Date();
      let lastRow = summarySheet.getLastRow();
      
      if (lastRow < 3) lastRow = 3;
      
      summarySheet.getRange(lastRow + 1, 1).setValue('Last Update: ' + now.toLocaleString());
      summarySheet.getRange(lastRow + 2, 1).setValue('Status: ' + status);
    }
  } catch (e) {
    console.log('Could not update status sheet:', e);
  }
}

// ============================================
// CONFIGURATION MANAGEMENT
// ============================================

function getConfiguration() {
  const props = PropertiesService.getDocumentProperties();
  
  const apiKey = props.getProperty('CLAUDE_API_KEY');
  const positionFolderId = props.getProperty('POSITION_FOLDER_ID');
  const cvFolderId = props.getProperty('CV_FOLDER_ID');
  const companyId = props.getProperty('COMPANY_ID');
  const setupDate = props.getProperty('SETUP_DATE');
  
  if (!apiKey || !cvFolderId) {
    return null;
  }
  
  return {
    claudeApiKey: apiKey,
    positionFolderId: positionFolderId || cvFolderId,
    cvFolderId: cvFolderId,
    customerIdentifier: companyId || 'Unknown',
    setupDate: setupDate
  };
}

// ============================================
// ERROR HANDLING
// ============================================

function handleProcessingError(result, ui) {
  if (result.serviceAccount) {
    ui.alert(
      'Permission Required',
      'The evaluation service needs access to your resources.\n\n' +
      `Please share the following with:\n${result.serviceAccount}\n\n` +
      '• This spreadsheet (Editor access)\n' +
      '• The CV folder (Viewer access)\n' +
      '• The Position folder (Viewer access)\n\n' +
      'Then try again.',
      ui.ButtonSet.OK
    );
  } else if (result.suggestion) {
    ui.alert(
      'Error',
      result.error + '\n\n' + result.suggestion,
      ui.ButtonSet.OK
    );
  } else {
    ui.alert(
      'Error',
      result.error || result.message || 'Operation failed',
      ui.ButtonSet.OK
    );
  }
}

// ============================================
// AUTO-TRIGGERS
// ============================================

function onEdit(e) {
  // Optional: Add triggers for specific sheet edits
}

function onInstall(e) {
  onOpen(e);
}