RubiRecruit Scope brief v2 – DRAFT 

1. Objectives 

Primary: Ship simultaneous listings on Google Workspace Marketplace and Microsoft AppSource that are secure, scalable, and use native platform billing. 

Secondary: Leverage maximum platform-native capabilities (hosting, billing, authentication, LLMs) to minimize custom infrastructure. 

Quality: Harden existing codebase (95% complete), fix timeouts, improve deduplication, strengthen CV↔cover-letter matching, implement hard trial/tier limits. 

2. Product & Pricing (per-user licensing) – to be discussed 

Free trial: 3 CVs lifetime total, then hard blocked (rubric allowed, view-only results, no export). 

One-off $99: 1 position cap, up to 500 CVs for that position, 30-day window. 

Growth $39/user/mo: 3 new positions per month, 100 CVs/day limit. 

Full $69/user/mo: Unlimited positions, 300 CVs/day limit. 

Billing: 

Google: Native Workspace Marketplace billing (5-15% commission) 

Microsoft: Azure Marketplace transactable SaaS (3% commission) 

No refunds after >10 CVs processed in billing period 

Subscriptions are platform-specific (no cross-platform portability) 

3. Architecture (Platform-Native, No Timeouts) 

Client: 

Google: Sheets add-on with sidebar UI 

Microsoft: Excel task pane (Web + Desktop support required) 

Backend: - Currently using Claude. Let's discuss LLM approach 

Google: Cloud Functions + Firestore + Gemini 1.5 Pro ($0.00125/1k tokens) 

Microsoft: Azure Functions + Cosmos DB + GPT-4o ($0.005/1k tokens) 

Complete separation between platforms (no shared infrastructure) 

Data: 

User data stored in their own Drive/SharePoint 

Position-based duplicate detection (resets per new position) 

File ID as primary dedupe key 

No central data storage 

Secrets: Platform key management services; no secrets in code; PII-safe logs. 

4. Feature Hardening & Enhancements 

General code review and clean up 

Trial enforcement: Server-side tracking, hard stop at 3 evaluations lifetime, API rejection after limit. 

Duplicate detection: File-ID primary key, position-based reset, SHA-256 content hash fallback, >99.5% accuracy target. 

CV ↔ Cover-letter matching: Multi-signal pairing (ID map + email/phone/name + similarity), >98% accuracy target. 

Name extraction: Rules (header, email local-part, labels) + filename parsing. OCR deferred to v1.1. 

LLM integration: Native platform models (Gemini/GPT-4o), per-user rate limiting (20 concurrent max), automatic queueing, ~$0.05-0.08 per evaluation cost target. 

Scoring maths: Current deterministic calculation frozen with comprehensive test coverage. 

5. Security & Compliance 

Authentication: Platform-native SSO only (Google OAuth, Microsoft Graph). 

Legal: Strong disclaimers that AI evaluations are advisory only; T&Cs requiring user acknowledgment of responsibility for hiring decisions. 

Data isolation: Complete separation between customers; user data only in their cloud storage. 

Input validation, rate-limits: Per-user limits by tier, circuit breakers for API failures. 

Logging: Structured, PII-safe; audit trails available. 

6. Marketplace Packaging 

Google Workspace: 

Add-on manifest with least-privilege scopes 

OAuth verification process (4-6 weeks expected) 

Native billing integration 

Listing assets (provided separately) 

Microsoft AppSource: 

Excel task pane add-in with Microsoft Graph 

Transactable SaaS offer configuration 

Azure Marketplace billing setup 

Desktop + Web support from day 1 

7. Deliverables 

Billing & Entitlements service (platform-native billing, tier enforcement, usage tracking). 

Processing infrastructure (Cloud/Azure Functions, no timeout limits, status endpoints, automatic retry). 

Hardened evaluation engine (dedupe service, matching service, name extraction, comprehensive tests). 

Security implementation (platform key management, least-privilege scopes, audit logging). 

Google add-on production-ready with Marketplace listing configuration. 

Excel add-in production-ready (Web + Desktop) with AppSource configuration. 

Test suite (500+ test cases, load tested). 

Monitoring & operations (health checks, usage dashboards, status page, alert configuration). 

8. Acceptance Criteria 

Passes both Google and Microsoft marketplace reviews. 

Process 300+ CVs without timeouts or errors. 

Hard enforcement of all tier limits with clear upgrade paths. 

Dedupe accuracy >99.5%; CV↔cover match ≥90% on test sets. 

Native billing working correctly on both platforms. 

Excel desktop functionality verified on Windows and Mac. 

All user data remains in their cloud storage (no central storage). 

99.5% uptime in first 30 days of operation. 

9. Assumptions 

Existing codebase is 95% functionally complete (bugs need fixing, bit of clean up and restructure likely required). 

Simultaneous launch on both platforms is required. 

Users will create new sheet/workbook for each evaluation cycle. 

Platform-native LLMs acceptable (Gemini/GPT-4o vs Claude). - TBC 

Basic status page sufficient for incident communication. 

10. Ongoing Support – lets discuss 

SLOs: P0 (data loss/billing) < 4 hours, P1 (service down) < 24h, P2 < 3 days. 

Maintenance: Tuesday 2AM PST window, blue-green deployments. 

Monitoring: Uptime, error rates, LLM costs, usage by tier, billing reconciliation. 

Capacity: 10 hours/month for updates, monitoring, and minor enhancements. 

11. Open Decisions  

Decisions Requiring Developer Recommendation: 

Incident response: Recommended approach for status page, automatic degradation to read-only mode, and billing pause triggers for extended outages. 

Usage overage handling: Best practice for users hitting daily/monthly limits - hard stop vs queuing vs overage charges. 

Data retention: Approach for subscription lapses - immediate read-only, grace period length, data purge timeline. 

Rate limiting architecture: Optimal implementation for per-user queuing without cross-user blocking. 

Business Decisions Already Made: 

One-off license expires after 30 days (not configurable). 

No enterprise admin panels in v1.0. 

No BYOK option in v1.0 - TBC 

OCR can be deferred if it risks timeline. 

Platform-specific backends preferred over shared infrastructure. 

Key Technical Clarifications: 

LLM Strategy: Use platform-native models to reduce costs 3-10x and avoid cross-platform dependencies. 

Storage Strategy: Google users use Drive, Microsoft users use SharePoint/OneDrive. No cross-platform access. 

Duplicate Detection: Must reset for each new position evaluation (critical requirement). 

Quality Bar: Production-ready means <0.5% error rate, comprehensive error handling, no critical bugs. 

Desktop Excel: Must work on desktop from launch, not just web version. 










second draft 


RubiRecruit Scope brief - DRAFT 

 

1) Objectives 

Primary: Ship a Google Workspace Marketplace listing that is secure, paid, and scalable (no 6-min limits). 

Secondary: Prepare a like-for-like Excel add-in for Microsoft AppSource using the same backend pattern. 

Quality: Harden code, fix timeouts, improve dedupe, name extraction, and CV↔cover-letter matching; lock scoring maths with tests. 

2) Product & Pricing (per-user licensing) 

Free trial: 3 CVs total, then blocked (rubric allowed, no export). 

One-off $99: 1 PD cap, up to 500 CVs for that PD. 

Growth $39/user/mo: 3 new PDs per month, unlimited CVs. 

Full $69/user/mo: Unlimited PDs + CVs. 

Billing: Stripe subscriptions + webhooks → entitlements service (plan, seats, PD quota/usage, trial counts). 

3) Architecture (to beat the 6-min cap) 

Client: Thin Google Sheets add-on (and later Excel task-pane) for UI only. 

Backend: Google Cloud Run services + Cloud Tasks queue for batch processing; resumable job model with job_id; progress endpoint for UI polling. 

Data: Candidate/PD registry; Drive File ID as dedupe source of truth; optional content hash. 

Secrets: Google Secret Manager; no secrets in code; redacted logs. 

4) Feature Hardening & Enhancements 

Duplicate detection: File-ID primary key + optional hash; index stored server-side; prevent reprocessing. 

CV ↔ Cover-letter matching: Multi-signal pairing (ID map + email/phone/name + text similarity), store confidence & rationale. 

Name extraction: Rules (header, email local-part, labels) + optional OCR via Cloud Vision for image-only PDFs. 

AI calls: Centralised provider client with retries/backoff; per-plan rate/cost guardrails; BYOK optional later for Full/enterprise. 

Scoring maths: Freeze current behaviour; add unit tests + regression fixtures; expose concise “why this score” notes (no UX change in tone). 

5) Security & Compliance 

Least-privilege OAuth scopes; clear scope justification. 

Privacy & Limited Use language aligned to actual data flows. 

Input validation, rate-limits, CSRF/nonce checks, signed webhooks, key rotation. 

Logging: Structured, PII-safe; toggleable verbose debug for support. 

6) Marketplace Packaging 

Google Workspace: Add-on manifest, listing assets (copy, screenshots, support URL), OAuth verification, licensing UX (trial/upgrade/paywall states). 

Microsoft AppSource (next): Excel task-pane add-in with Microsoft SSO; identical entitlements; BYOL (Stripe) v1; transactable SaaS optional later. 

7) Deliverables 

Billing & Entitlements service (Stripe plans, webhooks, admin overrides). 

Batch processing stack (Cloud Run + Cloud Tasks, status API, retries, resumable jobs). 

Dedupe, matching, name-extraction services with tests. 

Secure config (Secret Manager, scope matrix, threat model). 

Google add-on + Store listing pack ready for review. 

Excel add-in scaffold sharing the same backend (ready for AppSource submission). 

Test suite (1k-candidate load; scoring regressions; E2E for trial/upgrade). 

Runbooks & docs (ops dashboard, incident playbook, demo video script). 

8) Acceptance Criteria 

Passes Google OAuth & Marketplace review; listing live with Stripe-backed tier gating. 

A user can click Go and process large batches (e.g., 300 CVs) unattended; no Apps Script timeouts; resumable on failure. 

Dedupe FP < 0.5%; CV↔cover match ≥ 98% on test set; name extraction ≥ target benchmark (set during testing). 

Secrets never logged; logs PII-safe; security checks pass (OWASP-style). 

Excel add-in functional with the same entitlements and SSO, ready for AppSource validation. 

9) Assumptions 

No data-residency constraint at launch (security still paramount). 

BYOK is optional (enabled for Full/enterprise later). 

Existing rubric/UX style retained; only stability/explainability added. 

10) Ongoing Support (recommended) 

SLOs: P0 < 24h, P1 < 3 days. Weekly release train. 

Observability: Dashboards for throughput, error rate, cost per candidate, OCR/LLM usage, license events. 

Backlog: BYOK, enterprise SSO, native Google/Microsoft commerce, admin seat management. 

11) Open Decisions (flag for kickoff) 

One-off licence expiry (day window). 

Trial abuse controls (per account only vs stronger checks). 

Org admin panel for seat assignment & PD usage (v1 or later). 

OCR cost ceiling per job to avoid bill shock. 

Excel platform targets at launch (Web + Windows; Mac optional). 

 

 third draft

 RubiRecruit v2.0 - Functional Requirements 

Last Updated: 1st September 

1. Core Evaluation Workflow 

1.1 Position-Based Rubric Generation 

Functionality 

Accepts PDF position descriptions from user's cloud storage 

Analyzes position using native LLM (Gemini/GPT-4o) – TBC LETS DISCUSS 

Generates exactly 8 weighted categories (5-20% each, totaling 100%) 

Creates 5 specific attributes per category 

Builds 6-level scoring rubrics (0-5) with behavioral descriptions 

Adapts to industry, seniority, and role type 

Technical Requirements 

Text extraction without OCR dependency (v1.0) - phase 2  

Maximum 10,000 characters processed from position description 

Generation time: <30 seconds  

Rubric stored in user's sheet/workbook 

User Experience – currently exist  

One-click generation from selected PDF 

Preview before locking 

Edit capability before locking 

Version tracking with timestamps   

1.2 Document Processing & Matching 

Smart Document Matching 

Primary Method: File ID tracking (prevents reprocessing) 

Secondary Methods:  

Name extraction from headers/filename 

Email/phone matching across documents 

Upload timestamp proximity 

Content similarity scoring 

Duplicate Detection (Enhanced) 

File ID as primary key 

Fuzzy name matching (handles variations) 

Resets per position (key requirement) 

Confidence scoring: 0-100% 

Name Extraction Hierarchy 

Explicit headers ("Name:", "Full Name:") 

Email parsing (<EMAIL> → John Smith) 

Filename analysis with cleanup 

First prominent text (bold/large) 

1.3 Evaluation Engine 

Processing Capabilities 

Batch Sizes: 5, 10, or all remaining 

Per-Candidate Time: <30 seconds 

Concurrent Processing: Up to 20 per user 

Text Limit: 14,000 characters per candidate 

Auto-Retry: 3 attempts with exponential backoff 

Scoring Mathematics 

For each attribute (0-5 score): 

→ Convert to percentage (score/5 × 100) 

→ Apply weight within category 

→ Sum for category score (0-100%) 

 

For overall score: 

→ Multiply each category score by category weight 

→ Sum all weighted categories (0-100) 

 

Recommendations: 

- 80-100: Strong Candidate 

- 70-79: Good Candidate   

- 60-69: Developing Candidate 

- 0-59: Poor Fit 

2. Enhanced Metrics Extraction – (Would like to explore what making this dynamic and adjustable looks like) 

2.1 Employment History Analysis 

Total career experience (years) 

Industry-specific experience 

Average tenure per role 

Management experience years 

Maximum team size managed 

Budget responsibility 

Short stint detection (<12 months) 

Job hopping risk assessment 

2.2 Qualification Profiling 

Universities attended 

Degrees earned 

Professional certifications 

Technical skills inventory 

Functional expertise areas 

Notable company experience 

Language quality (Very High to Poor) 

2.3 X-Factor Identification 

Top 3 unique achievements 

Maximum 15 words each 

Focus on quantifiable results 

Prestigious recognitions 

Exceptional performance indicators 

3. Licensing & Access Control 

3.1 Trial Enforcement 

Limit: 3 evaluations lifetime 

Tracking: Server-side counter 

Block Mechanism: API rejection after limit 

Visibility: Counter shown in UI 

Reset: Not available (lifetime limit) 

3.2 Tier Management 

Feature 

Trial 

One-off 

Growth 

Full 

Rubric Generation 

✅ 

✅ 

✅ 

✅ 

Evaluations 

3 lifetime 

500 total 

100/day 

300/day 

Positions 

1 

1 

3/month 

Unlimited 

Processing Priority 

Low 

Normal 

Normal 

High 

Support 

Community 

Email 

Email 

Priority 

3.3 License Validation 

Real-time check with marketplace 

Grace period: 7 days for payment issues 

Read-only mode when expired 

Data retention: 30 days post-expiry 

4. Platform-Specific Features 

4.1 Google Workspace Implementation 

User Interface 

Sheets sidebar (300px width) 

8-step journey tracker 

Real-time progress indicators 

Native Material Design 

Data Storage 

Position descriptions: Google Drive folder 

CVs/Covers: Separate Google Drive folder 

Results: User's spreadsheet 

Rubric: Stored in sheet 

Integration Points 

OAuth 2.0 authentication 

Drive API for file access 

Sheets API for data writing 

Workspace Marketplace billing 

4.2 Microsoft AppSource Implementation 

User Interface 

Excel task pane (320px width) 

Identical 8-step journey 

Office UI Fabric design 

Desktop + Web support 

Data Storage 

Position descriptions: SharePoint/OneDrive folder 

CVs/Covers: SharePoint/OneDrive folder 

Results: User's workbook 

Rubric:  worksheet 

Integration Points 

Microsoft Graph authentication 

SharePoint/OneDrive APIs 

Excel JavaScript API 

Azure Marketplace billing 

5. Processing Workflow 

5.1 Eight-Step Journey 

Initial Setup 

Configure folders 

Accept terms & conditions 

Validate subscription 

Create Sheets/Worksheets 

Generate 13-15 required tabs 

Set up formatting 

Initialize tracking 

Load Position 

Select PDF from folder 

Extract text 

Store in configuration 

Document Mapping 

Scan CV/cover letter folder 

Match CVs with covers 

Create candidate list 

Generate Rubric 

AI analyzes position 

Creates evaluation framework 

Allows editing 

Lock Rubric 

Finalize criteria 

Prevent modifications 

Enable evaluations 

Process Candidates 

Batch or individual processing 

Progress tracking 

Error handling 

Review Results 

Dashboard summary 

Detailed evaluations 

5.2 Batch Processing Options 

Process Next 5: Manual control, good for testing 

Process Next 10: Balanced automation 

Process Specific: Select by name 

Process All: Automatic with pauses 

Retry Failed: Reprocess errors only 

6. Security & Compliance 

6.1 Data Protection 

Encryption: TLS 1.3 transit, platform-native at rest 

Isolation: Complete separation between customers 

Access: User data only in their cloud storage 

Retention: User-controlled via file deletion 

Backup: Platform-native (Drive/SharePoint) 

6.2 Legal Compliance 

Disclaimers: AI evaluations are advisory only 

Terms: Required acceptance before use 

Privacy: No central storage of candidate data 

GDPR: User controls all data deletion 

Audit: Full activity logging available 

6.3 Access Control 

Authentication: Platform SSO only 

Authorization: Folder-level permissions 

Session: Platform-managed timeouts 

MFA: Inherited from platform 

7. Performance Requirements 

7.1 Response Times 

UI Actions: <3 seconds 

Rubric Generation: <30 seconds 

Per Candidate: <30 seconds 

Batch of 5: <3 minutes 

Dashboard Update: <5 seconds 

7.2 Scalability – TBC lets chat 

Concurrent Users: 1,000 system-wide 

Per-User Concurrency: 20 evaluations 

Daily Volume: 10,000 evaluations 

Peak Hour: 1,000 evaluations 

7.3 Reliability 

Uptime SLO: 99.5% 

Error Rate: <0.5% 

Retry Success: >95% 

Data Loss: 0% 

8. Error Handling 

8.1 Graceful Degradation 

LLM timeout: Queue for retry 

API limit: User-specific queue 

Platform outage: Read-only mode 

Billing failure: 7-day grace period 

8.2 User Notifications 

Processing status: Real-time updates 

Errors: Clear explanations 

Limits reached: Upgrade prompts 

System issues: Status page 

9. Quality Assurance Requirements 

9.1 Testing Coverage 

Unit Tests: >80% code coverage 

Integration Tests: All API endpoints 

E2E Tests: Complete user journeys 

Load Tests: 100 concurrent users 

Security: OWASP top 10 

9.2 Acceptance Metrics 

Duplicate Detection: >99.5% accuracy 

CV-Cover Matching: >90% accuracy 

Rubric Generation: 100% valid structure 

Score Calculation: 100% deterministic 

Billing Integration: 100% accurate 

10. Features Explicitly Deferred 

To v1.1 

OCR for image-based PDFs – TBC lets discuss how hard this is… 

Email notifications 

Comparative analytics 

Rubric templates library 

To v2.0 

Enterprise admin panels 

BYOK option 

API access 

ATS integrations 

Multi-language support 

11. Support Features 

11.1 In-Product Help 

Contextual tooltips 

Step-by-step guides 

Video tutorials (links) 

FAQ section 

11.2 Diagnostic Tools – use same ones ive built 

Connection tester 

Permission validator 

Usage dashboard 

Error history 

12. Operational Features 

12.1 Monitoring 

Health checks every 60 seconds 

Usage metrics per customer 

Error tracking with context 

Cost monitoring with alerts 

12.2 Administrative 

Kill switch for service 

Rate limit adjustments 

Cost threshold management 

Audit log access 

 
 fourth draft

 Key Points to be updated in the Proposal - Meeting (5th September) 

Only Google  

Payments: Google payments – Not stripe  

Which Model to use - TBC 

API Key Integration 

One of the main scope changes is that we’ll be using your dedicated API key instead of requiring users to input their own. 

This means we’ll update the App Script so that it no longer adds a user’s API key. Instead, it will call your internal API securely, locked down for internal use only. 

App Script Review & Updates 

In addition to the API key change, we will review the entire App Script to bring it up to current coding standards. 

This includes improving reliability and ensuring the logic is hardened for long-term use. 

Smart Document Matching 

The document matching feature (CV ↔ Cover Letter) is mostly working but can be inconsistent at times. 

The logic currently uses a tiered approach, but occasionally mismatches occur, e.g., the cover letter not appearing alongside the CV in the output sheet. 

We’ll refine and stabilise this so that matching works as expected. 

Enhanced Metrics Extraction 

There’s an existing “Company Metrics” tab that captures additional details (e.g., current employer, years of experience at the company, and ~15–20 other attributes). 

The idea is to explore whether we can enhance this by introducing a more user-friendly setup process—for example, a pop-up or menu early in the sheet setup where the user can select which metrics they’d like to include for scoring. 

Scoring Logic 

The core scoring mathematics is already in place. 

Our focus will be on reviewing the logic to ensure accuracy (no calculation errors) and making the process easier to configure. 

Free Trial – Upload PD, Create the Rubric and Process 3 CVs 

File Processing (CV upload) - word document, Text file and PDF 

Finalise the flow - To install the extension on other browsers  

Add dynamic FAQs 

Update the current RubiRecruit website to add pricing  

 

FLOW – Client Portal  

Step 1 – Get Started 

 

Step 2 – Only Google Sheets for RubiRecruit 

 

Step 3 – Sign in with Google account 

 

Step 4 -  

 

Step 5 – Install  

 

Step 6 -  

 

Step 7 – On Google Sheets – Extensions 

 

Step 8 – Launch  

 

Step – 8: we will not need all the menus displayed, but only accounts and billing, My workspace and any other menu as required.  

 

-> When sign in to our RubiRecruit – The display will be as such. 
Note: We will not require all the menu, we will need spreedsheet, plans and billing, settings and log out  

 

 5th draft 

 RubiRecruit v2.0 - Functional Requirements 

Last Updated: 1st September 

1. Core Evaluation Workflow 

1.1 Position-Based Rubric Generation 

Functionality 

Accepts PDF position descriptions from user's cloud storage 

Analyzes position using native LLM (Gemini/GPT-4o) – TBC LETS DISCUSS 

Generates exactly 8 weighted categories (5-20% each, totaling 100%) 

Creates 5 specific attributes per category 

Builds 6-level scoring rubrics (0-5) with behavioral descriptions 

Adapts to industry, seniority, and role type 

Technical Requirements 

Text extraction without OCR dependency (v1.0) - phase 2  

Maximum 10,000 characters processed from position description 

Generation time: <30 seconds  

Rubric stored in user's sheet/workbook 

User Experience – currently exist  

One-click generation from selected PDF 

Preview before locking 

Edit capability before locking 

Version tracking with timestamps   

1.2 Document Processing & Matching 

Smart Document Matching  - currently build  

Primary Method: File ID tracking (prevents reprocessing) 

Secondary Methods:  

Name extraction from headers/filename 

Email/phone matching across documents 

Upload timestamp proximity 

Content similarity scoring 

Duplicate Detection (Enhanced) - currently built  

File ID as primary key 

Fuzzy name matching (handles variations) 

Resets per position (key requirement) 

Confidence scoring: 0-100% 

Name Extraction Hierarchy 

Explicit headers ("Name:", "Full Name:") 

Email parsing (<EMAIL> → John Smith) 

Filename analysis with cleanup 

First prominent text (bold/large) 

1.3 Evaluation Engine 

Processing Capabilities 

Batch Sizes: 5, 10, or all remaining 

Per-Candidate Time: <30 seconds 

Concurrent Processing: Up to 20 per user 

Text Limit: 14,000 characters per candidate 

Auto-Retry: 3 attempts with exponential backoff 

Scoring Mathematics 

For each attribute (0-5 score): 

→ Convert to percentage (score/5 × 100) 

→ Apply weight within category 

→ Sum for category score (0-100%) 

 

For overall score: 

→ Multiply each category score by category weight 

→ Sum all weighted categories (0-100) 

 

Recommendations: 

- 80-100: Strong Candidate 

- 70-79: Good Candidate   

- 60-69: Developing Candidate 

- 0-59: Poor Fit 

2. Enhanced Metrics Extraction – (Would like to explore what making this dynamic and adjustable looks like) 

2.1 Employment History Analysis 

Total career experience (years) 

Industry-specific experience 

Average tenure per role 

Management experience years 

Maximum team size managed 

Budget responsibility 

Short stint detection (<12 months) 

Job hopping risk assessment 

2.2 Qualification Profiling 

Universities attended 

Degrees earned 

Professional certifications 

Technical skills inventory 

Functional expertise areas 

Notable company experience 

Language quality (Very High to Poor) 

2.3 X-Factor Identification 

Top 3 unique achievements 

Maximum 15 words each 

Focus on quantifiable results 

Prestigious recognitions 

Exceptional performance indicators 

3. Licensing & Access Control 

3.1 Trial Enforcement 

Limit: 3 evaluations lifetime 

Tracking: Server-side counter 

Block Mechanism: API rejection after limit 

Visibility: Counter shown in UI 

Reset: Not available (lifetime limit) 

3.2 Tier Management 

Feature 

Trial 

One-off 

Growth 

Full 

Rubric Generation 

✅ 

✅ 

✅ 

✅ 

Evaluations 

3 lifetime 

500 total 

100/day 

300/day 

Positions 

1 

1 

3/month 

Unlimited 

Processing Priority 

Low 

Normal 

Normal 

High 

Support 

Community 

Email 

Email 

Priority 

3.3 License Validation 

Real-time check with marketplace 

Grace period: 7 days for payment issues 

Read-only mode when expired 

Data retention: 30 days post-expiry 

4. Platform-Specific Features 

4.1 Google Workspace Implementation 

User Interface 

Sheets sidebar (300px width) 

8-step journey tracker 

Real-time progress indicators 

Native Material Design 

Data Storage 

Position descriptions: Google Drive folder 

CVs/Covers: Separate Google Drive folder 

Results: User's spreadsheet 

Rubric: Stored in sheet 

Integration Points 

OAuth 2.0 authentication 

Drive API for file access 

Sheets API for data writing 

Workspace Marketplace billing 

4.2 Microsoft AppSource Implementation 

User Interface 

Excel task pane (320px width) 

Identical 8-step journey 

Office UI Fabric design 

Desktop + Web support 

Data Storage 

Position descriptions: SharePoint/OneDrive folder 

CVs/Covers: SharePoint/OneDrive folder 

Results: User's workbook 

Rubric:  worksheet 

Integration Points 

Microsoft Graph authentication 

SharePoint/OneDrive APIs 

Excel JavaScript API 

Azure Marketplace billing 

5. Processing Workflow 

5.1 Eight-Step Journey 

Initial Setup 

Configure folders 

Accept terms & conditions 

Validate subscription 

Create Sheets/Worksheets 

Generate 13-15 required tabs 

Set up formatting 

Initialize tracking 

Load Position 

Select PDF from folder 

Extract text 

Store in configuration 

Document Mapping 

Scan CV/cover letter folder 

Match CVs with covers 

Create candidate list 

Generate Rubric 

AI analyzes position 

Creates evaluation framework 

Allows editing 

Lock Rubric 

Finalize criteria 

Prevent modifications 

Enable evaluations 

Process Candidates 

Batch or individual processing 

Progress tracking 

Error handling 

Review Results 

Dashboard summary 

Detailed evaluations 

5.2 Batch Processing Options 

Process Next 5: Manual control, good for testing 

Process Next 10: Balanced automation 

Process Specific: Select by name 

Process All: Automatic with pauses 

Retry Failed: Reprocess errors only 

6. Security & Compliance 

6.1 Data Protection 

Encryption: TLS 1.3 transit, platform-native at rest 

Isolation: Complete separation between customers 

Access: User data only in their cloud storage 

Retention: User-controlled via file deletion 

Backup: Platform-native (Drive/SharePoint) 

6.2 Legal Compliance 

Disclaimers: AI evaluations are advisory only 

Terms: Required acceptance before use 

Privacy: No central storage of candidate data 

GDPR: User controls all data deletion 

Audit: Full activity logging available 

6.3 Access Control 

Authentication: Platform SSO only 

Authorization: Folder-level permissions 

Session: Platform-managed timeouts 

MFA: Inherited from platform 

7. Performance Requirements 

7.1 Response Times 

UI Actions: <3 seconds 

Rubric Generation: <30 seconds 

Per Candidate: <30 seconds 

Batch of 5: <3 minutes 

Dashboard Update: <5 seconds 

7.2 Scalability – TBC lets chat 

Concurrent Users: 1,000 system-wide 

Per-User Concurrency: 20 evaluations 

Daily Volume: 10,000 evaluations 

Peak Hour: 1,000 evaluations 

7.3 Reliability 

Uptime SLO: 99.5% 

Error Rate: <0.5% 

Retry Success: >95% 

Data Loss: 0% 

8. Error Handling 

8.1 Graceful Degradation 

LLM timeout: Queue for retry 

API limit: User-specific queue 

Platform outage: Read-only mode 

Billing failure: 7-day grace period 

8.2 User Notifications 

Processing status: Real-time updates 

Errors: Clear explanations 

Limits reached: Upgrade prompts 

System issues: Status page 

9. Quality Assurance Requirements 

9.1 Testing Coverage 

Unit Tests: >80% code coverage 

Integration Tests: All API endpoints 

E2E Tests: Complete user journeys 

Load Tests: 100 concurrent users 

Security: OWASP top 10 – needs to be certified separately  

9.2 Acceptance Metrics 

Duplicate Detection: >99.5% accuracy 

CV-Cover Matching: >90% accuracy 

Rubric Generation: 100% valid structure 

Score Calculation: 100% deterministic 

Billing Integration: 100% accurate 

10. Features Explicitly Deferred 

To v1.1 

OCR for image-based PDFs – TBC lets discuss how hard this is… 

Email notifications 

Comparative analytics 

Rubric templates library 

To v2.0 

Enterprise admin panels 

BYOK option 

API access 

ATS integrations 

Multi-language support 

11. Support Features 

11.1 In-Product Help 

Contextual tooltips 

Step-by-step guides 

Video tutorials (links) 

FAQ section 

11.2 Diagnostic Tools – use same ones ive built – Test API keys – verify  

Connection tester 

Permission validator 

Usage dashboard 

Error history 

12. Operational Features 

12.1 Monitoring 

Health checks every 60 seconds 

Usage metrics per customer 

Error tracking with context 

Cost monitoring with alerts 

12.2 Administrative 

Kill switch for service 

Rate limit adjustments 

Cost threshold management 

Audit log access 

 

 