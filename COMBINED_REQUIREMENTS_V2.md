# RubiRecruit v2.0 - Combined Requirements Document

## 📋 Executive Summary

This document consolidates all client requirements from:
- **September 1st Meeting**: Core functional requirements and architecture
- **September 5th Meeting**: Key scope changes and implementation updates
- **v8.0.0 Features**: Enhanced capabilities and improvements
- **Previous Analysis**: Missing features and implementation gaps

**Target**: Production-ready marketplace deployment with simultaneous Google Workspace and Microsoft AppSource listings.

---

## 🎯 Core Business Objectives

### **Primary Goals**
1. **Simultaneous Marketplace Deployment**: Google Workspace Marketplace + Microsoft AppSource
2. **No Timeout Limitations**: Process 300+ CVs without 6-minute execution limits
3. **Platform-Native Billing**: Google Payments + Azure Marketplace billing
4. **Enhanced User Experience**: Modern UI with advanced AI capabilities

### **Key Scope Changes (September 5th Meeting)**
- **Google-Only Focus**: Primary focus on Google Workspace, Microsoft as secondary
- **Dedicated API Key**: Use internal API key instead of user-provided keys
- **Google Payments**: Native Google billing instead of Stripe
- **Enhanced Document Matching**: Improve CV ↔ Cover Letter matching accuracy
- **Dynamic Metrics**: User-configurable metrics selection

---

## 🏗️ Architecture Requirements

### **Platform Strategy**
| **Platform** | **Priority** | **Implementation** | **Backend** | **LLM** | **Billing** |
|-------------|--------------|-------------------|-------------|---------|-------------|
| **Google Workspace** | 🔴 Primary | Sheets add-on | Cloud Functions + Firestore | Gemini 1.5 Pro | Google Payments |
| **Microsoft AppSource** | 🟡 Secondary | Excel task pane | Azure Functions + Cosmos DB | GPT-4o | Azure Marketplace |

### **Backend Architecture (No Timeouts)**
```
Current: Client → Apps Script → 6min timeout
Target:  Client → Queue System → Cloud Functions → No timeout
```

**Required Components**:
- [ ] **Queue-Based Processing**: Cloud Tasks/Azure Service Bus
- [ ] **Resumable Jobs**: Job ID tracking with progress endpoints
- [ ] **Cloud Functions**: Google Cloud Functions + Azure Functions
- [ ] **Status Polling**: Real-time progress updates
- [ ] **Auto-Retry**: Exponential backoff for failed operations

---

## 🔧 Core Functional Requirements

### **1. Position-Based Rubric Generation**

**Functionality**:
- [ ] **PDF Processing**: Accept position descriptions from cloud storage
- [ ] **AI Analysis**: Use native LLM (Gemini/GPT-4o) - TBC
- [ ] **8 Categories**: Exactly 8 weighted categories (5-20% each, totaling 100%)
- [ ] **5 Attributes**: 5 specific attributes per category
- [ ] **6-Level Scoring**: 0-5 scale with behavioral descriptions
- [ ] **Industry Adaptation**: Adapts to industry, seniority, and role type

**Technical Requirements**:
- [ ] **Text Extraction**: Without OCR dependency (v1.0)
- [ ] **Character Limit**: Maximum 10,000 characters from position description
- [ ] **Generation Time**: <30 seconds
- [ ] **Storage**: Rubric stored in user's sheet/workbook

**User Experience**:
- [ ] **One-Click Generation**: From selected PDF
- [ ] **Preview Before Lock**: Review and edit capability
- [ ] **Version Tracking**: Timestamps and version history

### **2. Enhanced Document Processing & Matching**

**Smart Document Matching** (Currently Built):
- [ ] **File ID Tracking**: Primary method (prevents reprocessing)
- [ ] **Name Extraction**: From headers/filename
- [ ] **Email/Phone Matching**: Across documents
- [ ] **Upload Timestamp**: Proximity analysis
- [ ] **Content Similarity**: Scoring system

**Duplicate Detection** (Enhanced):
- [ ] **File ID Primary Key**: Google Drive file IDs as source of truth
- [ ] **Fuzzy Name Matching**: Handles variations
- [ ] **Position-Based Reset**: Resets per new position (critical requirement)
- [ ] **Confidence Scoring**: 0-100% accuracy
- [ ] **Target Accuracy**: >99.5% duplicate detection

**Name Extraction Hierarchy**:
- [ ] **Explicit Headers**: "Name:", "Full Name:"
- [ ] **Email Parsing**: <EMAIL> → John Smith
- [ ] **Filename Analysis**: With cleanup
- [ ] **First Prominent Text**: Bold/large text detection

### **3. Evaluation Engine**

**Processing Capabilities**:
- [ ] **Batch Sizes**: 5, 10, or all remaining
- [ ] **Per-Candidate Time**: <30 seconds
- [ ] **Concurrent Processing**: Up to 20 per user
- [ ] **Text Limit**: 14,000 characters per candidate
- [ ] **Auto-Retry**: 3 attempts with exponential backoff

**Scoring Mathematics**:
- [ ] **Attribute Scoring**: 0-5 score → percentage (score/5 × 100)
- [ ] **Category Weighting**: Apply weight within category
- [ ] **Category Score**: Sum for category score (0-100%)
- [ ] **Overall Score**: Multiply each category by weight, sum all (0-100)

**Recommendations**:
- [ ] **80-100**: Strong Candidate
- [ ] **70-79**: Good Candidate
- [ ] **60-69**: Developing Candidate
- [ ] **0-59**: Poor Fit

### **4. Enhanced Metrics Extraction (Dynamic)**

**Employment History Analysis**:
- [ ] **Total Career Experience**: Years calculation
- [ ] **Industry-Specific Experience**: Sector analysis
- [ ] **Average Tenure**: Per role calculation
- [ ] **Management Experience**: Years in management
- [ ] **Maximum Team Size**: Managed team size
- [ ] **Budget Responsibility**: Financial oversight
- [ ] **Short Stint Detection**: <12 months roles
- [ ] **Job Hopping Risk**: Assessment algorithm

**Qualification Profiling**:
- [ ] **Universities Attended**: Institution tracking
- [ ] **Degrees Earned**: Educational qualifications
- [ ] **Professional Certifications**: Industry certifications
- [ ] **Technical Skills**: Inventory and assessment
- [ ] **Functional Expertise**: Area specialization
- [ ] **Notable Company Experience**: Prestigious employers
- [ ] **Language Quality**: Very High to Poor rating

**X-Factor Identification**:
- [ ] **Top 3 Achievements**: Unique accomplishments
- [ ] **Maximum 15 Words**: Each achievement description
- [ ] **Quantifiable Results**: Focus on measurable outcomes
- [ ] **Prestigious Recognitions**: Awards and honors
- [ ] **Performance Indicators**: Exceptional metrics

**Dynamic Metrics Selection** (New Requirement):
- [ ] **User Configuration**: Pop-up/menu for metric selection
- [ ] **Customizable Scoring**: User-defined metric weights
- [ ] **Setup Process**: Early in sheet setup workflow
- [ ] **Flexible Categories**: Adaptable to different roles

### **5. Interview Questions Generation (v8.0.0 Feature)**

**AI-Generated Questions**:
- [ ] **6 Specific Questions**: Per candidate
- [ ] **Context-Aware**: Based on CV, cover letter, evaluation
- [ ] **Gap Analysis**: Target weak evaluation areas
- [ ] **Technical Focus**: Probe specific skills and claims
- [ ] **Cultural Fit**: Motivation and team dynamics
- [ ] **Personalized**: Reference actual companies/projects

**Question Types**:
- [ ] **Experience Gaps**: Probe inconsistencies
- [ ] **Role Transitions**: Explore career changes
- [ ] **Claim Validation**: Test unsubstantiated claims
- [ ] **Technical Expertise**: Role-specific skills
- [ ] **Cultural Assessment**: Team fit evaluation
- [ ] **Weakness Challenge**: Address low scores

---

## 💰 Licensing & Access Control

### **Trial Enforcement**
- [ ] **Limit**: 3 evaluations lifetime
- [ ] **Tracking**: Server-side counter
- [ ] **Block Mechanism**: API rejection after limit
- [ ] **Visibility**: Counter shown in UI
- [ ] **Reset**: Not available (lifetime limit)

### **Tier Management**
| **Feature** | **Trial** | **One-off** | **Growth** | **Full** |
|-------------|-----------|-------------|------------|----------|
| **Rubric Generation** | ✅ | ✅ | ✅ | ✅ |
| **Evaluations** | 3 lifetime | 500 total | 100/day | 300/day |
| **Positions** | 1 | 1 | 3/month | Unlimited |
| **Processing Priority** | Low | Normal | Normal | High |
| **Support** | Community | Email | Email | Priority |

### **License Validation**
- [ ] **Real-Time Check**: With marketplace
- [ ] **Grace Period**: 7 days for payment issues
- [ ] **Read-Only Mode**: When expired
- [ ] **Data Retention**: 30 days post-expiry

---

## 🖥️ Platform-Specific Features

### **Google Workspace Implementation (Primary)**

**User Interface**:
- [ ] **Sheets Sidebar**: 300px width
- [ ] **8-Step Journey**: Tracker with progress indicators
- [ ] **Real-Time Updates**: Progress indicators
- [ ] **Material Design**: Native Google design system

**Data Storage**:
- [ ] **Position Descriptions**: Google Drive folder
- [ ] **CVs/Covers**: Separate Google Drive folder
- [ ] **Results**: User's spreadsheet
- [ ] **Rubric**: Stored in sheet

**Integration Points**:
- [ ] **OAuth 2.0**: Authentication
- [ ] **Drive API**: File access
- [ ] **Sheets API**: Data writing
- [ ] **Google Payments**: Native billing

### **Microsoft AppSource Implementation (Secondary)**

**User Interface**:
- [ ] **Excel Task Pane**: 320px width
- [ ] **Identical Journey**: 8-step process
- [ ] **Office UI Fabric**: Microsoft design system
- [ ] **Desktop + Web**: Support both platforms

**Data Storage**:
- [ ] **Position Descriptions**: SharePoint/OneDrive folder
- [ ] **CVs/Covers**: SharePoint/OneDrive folder
- [ ] **Results**: User's workbook
- [ ] **Rubric**: Worksheet storage

**Integration Points**:
- [ ] **Microsoft Graph**: Authentication
- [ ] **SharePoint/OneDrive APIs**: File access
- [ ] **Excel JavaScript API**: Data writing
- [ ] **Azure Marketplace**: Billing

---

## 🔄 Processing Workflow

### **Eight-Step Journey**

1. **Initial Setup**
   - [ ] Configure folders
   - [ ] Accept terms & conditions
   - [ ] Validate subscription
   - [ ] Create Sheets/Worksheets
   - [ ] Generate 13-15 required tabs
   - [ ] Set up formatting
   - [ ] Initialize tracking

2. **Load Position**
   - [ ] Select PDF from folder
   - [ ] Extract text
   - [ ] Store in configuration

3. **Document Mapping**
   - [ ] Scan CV/cover letter folder
   - [ ] Match CVs with covers
   - [ ] Create candidate list

4. **Generate Rubric**
   - [ ] AI analyzes position
   - [ ] Creates evaluation framework
   - [ ] Allows editing

5. **Lock Rubric**
   - [ ] Finalize criteria
   - [ ] Prevent modifications
   - [ ] Enable evaluations

6. **Process Candidates**
   - [ ] Batch or individual processing
   - [ ] Progress tracking
   - [ ] Error handling

7. **Review Results**
   - [ ] Dashboard summary
   - [ ] Detailed evaluations

8. **Export/Share**
   - [ ] Results export
   - [ ] Sharing capabilities

### **Batch Processing Options**
- [ ] **Process Next 5**: Manual control, good for testing
- [ ] **Process Next 10**: Balanced automation
- [ ] **Process Specific**: Select by name
- [ ] **Process All**: Automatic with pauses
- [ ] **Retry Failed**: Reprocess errors only

---

## 🔐 Security & Compliance

### **Data Protection**
- [ ] **Encryption**: TLS 1.3 transit, platform-native at rest
- [ ] **Isolation**: Complete separation between customers
- [ ] **Access**: User data only in their cloud storage
- [ ] **Retention**: User-controlled via file deletion
- [ ] **Backup**: Platform-native (Drive/SharePoint)

### **Legal Compliance**
- [ ] **Disclaimers**: AI evaluations are advisory only
- [ ] **Terms**: Required acceptance before use
- [ ] **Privacy**: No central storage of candidate data
- [ ] **GDPR**: User controls all data deletion
- [ ] **Audit**: Full activity logging available

### **Access Control**
- [ ] **Authentication**: Platform SSO only
- [ ] **Authorization**: Folder-level permissions
- [ ] **Session**: Platform-managed timeouts
- [ ] **MFA**: Inherited from platform

---

## 📊 Performance Requirements

### **Response Times**
- [ ] **UI Actions**: <3 seconds
- [ ] **Rubric Generation**: <30 seconds
- [ ] **Per Candidate**: <30 seconds
- [ ] **Batch of 5**: <3 minutes
- [ ] **Dashboard Update**: <5 seconds

### **Scalability**
- [ ] **Concurrent Users**: 1,000 system-wide
- [ ] **Per-User Concurrency**: 20 evaluations
- [ ] **Daily Volume**: 10,000 evaluations
- [ ] **Peak Hour**: 1,000 evaluations

### **Reliability**
- [ ] **Uptime SLO**: 99.5%
- [ ] **Error Rate**: <0.5%
- [ ] **Retry Success**: >95%
- [ ] **Data Loss**: 0%

---

## 🛠️ Key Implementation Changes (September 5th)

### **API Key Integration**
- [ ] **Dedicated API Key**: Use internal API key instead of user-provided
- [ ] **Secure Internal API**: Locked down for internal use only
- [ ] **No User Input**: Remove API key input from UI
- [ ] **Backend Management**: Handle API keys server-side

### **App Script Review & Updates**
- [ ] **Code Standards**: Bring up to current coding standards
- [ ] **Reliability Improvements**: Harden logic for long-term use
- [ ] **Error Handling**: Comprehensive error management
- [ ] **Performance Optimization**: Reduce execution time

### **Smart Document Matching Improvements**
- [ ] **Stabilize Logic**: Fix inconsistent matching
- [ ] **Tiered Approach**: Refine existing tiered matching
- [ ] **Mismatch Resolution**: Fix cover letter not appearing with CV
- [ ] **Accuracy Target**: >98% matching accuracy

### **Enhanced Metrics Extraction**
- [ ] **User-Friendly Setup**: Pop-up/menu for metric selection
- [ ] **Configurable Metrics**: User-defined metric weights
- [ ] **Early Setup**: Integrate into sheet setup process
- [ ] **Flexible Categories**: Adaptable to different roles

### **File Processing Expansion**
- [ ] **Word Documents**: Support .docx files
- [ ] **Text Files**: Support .txt files
- [ ] **PDF Files**: Existing support
- [ ] **Format Detection**: Automatic file type recognition

---

## 🎯 Client Portal Flow

### **Installation Process**
1. **Get Started**: Initial landing page
2. **Google Sheets Only**: Platform selection
3. **Sign in with Google**: OAuth authentication
4. **Installation**: Add-on installation
5. **Launch**: Access from Extensions menu

### **User Interface Requirements**
- [ ] **Simplified Menu**: Only essential options
- [ ] **Required Menus**: Spreadsheet, Plans & Billing, Settings, Logout
- [ ] **Clean Interface**: Remove unnecessary menu items
- [ ] **Intuitive Navigation**: Easy access to core features

---

## 📋 Quality Assurance Requirements

### **Testing Coverage**
- [ ] **Unit Tests**: >80% code coverage
- [ ] **Integration Tests**: All API endpoints
- [ ] **E2E Tests**: Complete user journeys
- [ ] **Load Tests**: 100 concurrent users
- [ ] **Security Tests**: OWASP top 10

### **Acceptance Metrics**
- [ ] **Duplicate Detection**: >99.5% accuracy
- [ ] **CV-Cover Matching**: >98% accuracy
- [ ] **Rubric Generation**: 100% valid structure
- [ ] **Score Calculation**: 100% deterministic
- [ ] **Billing Integration**: 100% accurate

---

## 🚀 Implementation Priority Matrix

| **Feature Category** | **Priority** | **Complexity** | **Timeline** | **Dependencies** |
|---------------------|--------------|----------------|--------------|------------------|
| **Timeout Elimination** | 🔴 Critical | High | 4-6 weeks | Cloud infrastructure |
| **API Key Integration** | 🔴 Critical | Medium | 2-3 weeks | Backend changes |
| **Google Payments** | 🔴 Critical | High | 4-6 weeks | Marketplace APIs |
| **Document Matching** | 🟠 High | Medium | 2-3 weeks | Algorithm refinement |
| **Dynamic Metrics** | 🟠 High | Medium | 3-4 weeks | UI development |
| **Interview Questions** | 🟡 Medium | Low | 1-2 weeks | AI integration |
| **Multi-Platform** | 🟡 Medium | Very High | 8-12 weeks | Platform-specific dev |
| **Enhanced UI** | 🟢 Low | Low | 2-3 weeks | Design system |

---

## 📈 Success Metrics

### **Technical Metrics**
- [ ] Process 300+ CVs without timeouts
- [ ] Duplicate detection >99.5% accuracy
- [ ] CV-Cover matching >98% accuracy
- [ ] Uptime SLO 99.5%
- [ ] Error rate <0.5%

### **Business Metrics**
- [ ] Successful marketplace listings
- [ ] Native billing integration
- [ ] Multi-platform user acquisition
- [ ] Subscription conversion rates
- [ ] Customer satisfaction scores

### **User Experience Metrics**
- [ ] Modern, intuitive interface
- [ ] Real-time progress tracking
- [ ] Comprehensive candidate insights
- [ ] Seamless file management
- [ ] Professional branding

---

## 🔮 Future Enhancements (Deferred)

### **v1.1 Features**
- [ ] OCR for image-based PDFs
- [ ] Email notifications
- [ ] Comparative analytics
- [ ] Rubric templates library

### **v2.0 Features**
- [ ] Enterprise admin panels
- [ ] BYOK (Bring Your Own Key) option
- [ ] API access for third-party integrations
- [ ] ATS (Applicant Tracking System) integrations
- [ ] Multi-language support

---

## 📋 Next Steps

### **Immediate (This Week)**
1. **Fix 6-minute timeout** with batch processing
2. **Implement API key integration** changes
3. **Start document matching** improvements
4. **Plan dynamic metrics** implementation

### **Short-term (Next Month)**
1. **Complete timeout elimination** system
2. **Implement Google Payments** integration
3. **Deploy enhanced document matching**
4. **Add interview questions** generation

### **Medium-term (Next Quarter)**
1. **Complete multi-platform** implementation
2. **Deploy to both marketplaces**
3. **Implement comprehensive monitoring**
4. **Add advanced analytics**

---

**Document Version**: 2.0  
**Last Updated**: December 2024  
**Next Review**: Weekly during implementation phase  
**Status**: Ready for development planning
