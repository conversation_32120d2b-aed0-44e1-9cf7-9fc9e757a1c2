# RubiRecruit Excel Add-in - AI Coding Instructions

## Project Overview
RubiRecruit is an Office Add-in for Excel that uses Claude AI to evaluate job candidates against position-specific rubrics. It processes CVs/cover letters and generates comprehensive evaluations directly in Excel spreadsheets.

## Architecture & Key Components
- **Excel Add-in Pattern**: Built on Office.js, runs as taskpane add-in in Excel desktop
- **Service Architecture**: Modular services in `src/services/`:
  - `excelService.ts`: Excel sheet management, data writing, formatting (1100+ lines)
  - `claudeService.ts`: Claude API integration for candidate evaluation
  - `localFileService.ts`: Local file selection/processing via browser file picker
  - `authService.ts`: MSAL auth (currently disabled for local mode)
  - `pdfService.ts`: PDF text extraction using pdf.js
- **Entry Points**: 
  - `src/taskpane/taskpane.ts`: Main UI logic and orchestration
  - `src/commands/commands.ts`: Ribbon command handlers

## Critical Developer Workflows
```bash
# Development server with auto-reload
npm run dev-server   # Webpack dev server on https://localhost:3000

# Start debugging in Excel desktop
npm run start -- desktop --app excel

# Proxy server for Claude API (required for CORS)
npm run proxy       # Runs proxy-server.js on port 3001

# Parallel dev setup (recommended)
npm run dev         # Runs both proxy and start commands
```

## Project-Specific Patterns
- **Local File Mode**: Currently hardcoded in `config.ts` with `USE_LOCAL_FILES: true`
- **Ruby Color Theme**: Consistent `#8B1538` color used across all Excel sheets
- **Sheet Structure**: Creates 11 predefined sheets including `_Storage` for state persistence
- **Error Handling**: Wrapped Excel.run() calls with try-catch, uses debugLog/debugError helpers
- **API Proxy**: All Claude API calls route through local Express proxy to handle CORS

## Key Integration Points
- **Claude API**: Messages API with `claude-3-5-sonnet-20241022` model via proxy
- **Excel Context**: All Excel operations wrapped in `Excel.run(async (context) => {...})`
- **File Processing**: Browser FileReader API → ArrayBuffer → Base64 for PDF/text content
- **State Management**: Global variables in taskpane.ts (`claudeApiKey`, `currentRubric`, `processingQueue`)

## Non-Standard Approaches
- **No Tests**: Project has no test files or testing infrastructure
- **Hardcoded Paths**: Local file paths in config.ts (Windows-specific)
- **Manual File Pickers**: HTML input elements for file selection (not Office file picker)
- **Inline Styling**: Heavy use of direct Excel range formatting vs CSS classes
- **Storage Sheet**: Uses hidden `_Storage` worksheet for persisting data between sessions

## Build Configuration
- **Webpack**: Custom config with Office Add-in plugins, TypeScript via Babel
- **HTTPS**: Dev certs from `office-addin-dev-certs` for localhost development
- **Manifest**: XML-based Office manifest at root (`manifest.xml`)
- **Entry Points**: Multiple webpack entries for taskpane, commands, polyfills
