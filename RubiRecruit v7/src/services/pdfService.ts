class PDFService {
  async extractTextFromBase64(base64Content: string): Promise<string> {
    console.log('[PDFService] Processing PDF base64, length:', base64Content.length);
    
    // Clean the base64 string (remove data URL prefix if present)
    const cleanBase64 = base64Content.replace(/^data:.*?,/, '');
    
    // Store in sessionStorage for persistence during session
    try {
      if (typeof(Storage) !== "undefined") {
        // Store the base64 for later use
        sessionStorage.setItem('currentPDFBase64', cleanBase64);
        sessionStorage.setItem('currentPDFSize', String(cleanBase64.length));
        console.log('[PDFService] Stored PDF base64 in session storage');
        
        // Also store in Excel for persistence
        const { default: excelService } = await import('./excelService');
        await excelService.setStoredValue('POSITION_PDF_BASE64', cleanBase64);
        console.log('[PDFService] Stored PDF base64 in Excel storage');
      }
    } catch (e) {
      console.error('[PDFService] Error storing PDF content:', e);
    }
    
    // Return the base64 for processing
    return cleanBase64;
  }
  
  async extractTextFromFile(file: File): Promise<string> {
    console.log('[PDFService] Extracting text from file:', file.name);
    
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = async () => {
        try {
          const arrayBuffer = reader.result as ArrayBuffer;
          const bytes = new Uint8Array(arrayBuffer);
          let binary = '';
          bytes.forEach(byte => binary += String.fromCharCode(byte));
          const base64 = btoa(binary);
          
          const result = await this.extractTextFromBase64(base64);
          resolve(result);
        } catch (error) {
          reject(error);
        }
      };
      
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsArrayBuffer(file);
    });
  }
  
  async getStoredPDFContent(): Promise<string | null> {
    try {
      // First check sessionStorage
      if (typeof(Storage) !== "undefined") {
        const stored = sessionStorage.getItem('currentPDFBase64');
        if (stored) {
          console.log('[PDFService] Retrieved PDF from sessionStorage, length:', stored.length);
          return stored;
        }
      }
      
      // Then check Excel storage
      const { default: excelService } = await import('./excelService');
      const excelStored = await excelService.getStoredValue('POSITION_PDF_BASE64');
      if (excelStored) {
        console.log('[PDFService] Retrieved PDF from Excel storage, length:', excelStored.length);
        // Also restore to sessionStorage for faster access
        if (typeof(Storage) !== "undefined") {
          sessionStorage.setItem('currentPDFBase64', excelStored);
        }
        return excelStored;
      }
      
      console.log('[PDFService] No stored PDF content found');
      return null;
    } catch (e) {
      console.error('[PDFService] Error retrieving PDF content:', e);
      return null;
    }
  }
  
  clearStoredPDF(): void {
    try {
      if (typeof(Storage) !== "undefined") {
        sessionStorage.removeItem('currentPDFBase64');
        sessionStorage.removeItem('currentPDFSize');
        console.log('[PDFService] Cleared stored PDF content from sessionStorage');
      }
    } catch (e) {
      console.error('[PDFService] Could not clear PDF content:', e);
    }
  }
}

export default new PDFService();