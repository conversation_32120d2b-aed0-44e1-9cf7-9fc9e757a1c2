import { Client } from '@microsoft/microsoft-graph-client';
import authService from './authService';
import { Config } from '../config/config';

class SharePointService {
  private graphClient: Client | null = null;
  
  async initialize(): Promise<void> {
    const token = await authService.getToken();
    
    this.graphClient = Client.init({
      authProvider: (done) => {
        done(null, token);
      }
    });
  }
  
  async getSiteId(): Promise<string> {
    if (!this.graphClient) await this.initialize();
    
    const siteUrl = new URL(Config.SHAREPOINT_SITE_URL);
    const hostname = siteUrl.hostname;
    const sitePath = siteUrl.pathname;
    
    const site = await this.graphClient!
      .api(`/sites/${hostname}:${sitePath}`)
      .get();
    
    return site.id;
  }
  
  async getDocumentLibraryId(libraryName: string): Promise<string> {
    const siteId = await this.getSiteId();
    
    const drives = await this.graphClient!
      .api(`/sites/${siteId}/drives`)
      .get();
    
    const library = drives.value.find((drive: any) => 
      drive.name === libraryName
    );
    
    if (!library) {
      throw new Error(`Document library ${libraryName} not found`);
    }
    
    return library.id;
  }
  
  async listFiles(libraryName: string): Promise<any[]> {
    const driveId = await this.getDocumentLibraryId(libraryName);
    
    const files = await this.graphClient!
      .api(`/drives/${driveId}/root/children`)
      .filter("file ne null")
      .get();
    
    return files.value;
  }
  
  async downloadFile(driveId: string, itemId: string): Promise<ArrayBuffer> {
    const downloadUrl = await this.graphClient!
      .api(`/drives/${driveId}/items/${itemId}`)
      .get();
    
    const response = await fetch(downloadUrl['@microsoft.graph.downloadUrl']);
    return response.arrayBuffer();
  }
  
  async uploadFile(
    libraryName: string, 
    fileName: string, 
    content: ArrayBuffer
  ): Promise<any> {
    const driveId = await this.getDocumentLibraryId(libraryName);
    
    const uploadedFile = await this.graphClient!
      .api(`/drives/${driveId}/root:/${fileName}:/content`)
      .put(content);
    
    return uploadedFile;
  }
  
  async getFileContent(driveId: string, itemId: string): Promise<string> {
    // For PDFs, we'll need to use Azure Cognitive Services
    // For now, return placeholder
    const arrayBuffer = await this.downloadFile(driveId, itemId);
    
    // This is where you'd integrate with Azure Cognitive Services
    // for OCR/text extraction
    return "PDF content would be extracted here";
  }
}

export default new SharePointService();