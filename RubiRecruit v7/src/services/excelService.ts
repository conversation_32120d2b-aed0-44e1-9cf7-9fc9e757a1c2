/* global Excel */

interface EvaluationResult {
  candidate_name: string;
  total_score: number;
  recommendation: string;
  categories: any;
  strengths: string[];
  development_areas: string[];
  overall_assessment: string;
  metrics?: any;
  hasCV?: boolean;
  hasCoverLetter?: boolean;
  documentsStatus?: string;
  matchConfidence?: string;
  matchMethod?: string;
  interview_questions?: string[];
  cvRawText?: string;
  actualTokensUsed?: {
    input: number;
    output: number;
    total: number;
  };
}

class ExcelService {
  private readonly RUBY_COLOR = '#8B1538';
  
  async createAllSheets(): Promise<void> {
    await Excel.run(async (context) => {
      const workbook = context.workbook;
      const sheets = workbook.worksheets;
      
      // Define sheets in the exact order requested
      const requiredSheets = [
        { name: 'Processing Log', color: this.RUBY_COLOR },
        { name: 'Processed Candidates', color: this.RUBY_COLOR },
        { name: 'Document Mapping', color: this.RUBY_COLOR },
        { name: 'Position Configuration', color: this.RUBY_COLOR },
        { name: 'PD Raw Text', color: this.RUBY_COLOR },  // Changed from CV Raw Text
        { name: 'Dynamic Rubric Configuration', color: this.RUBY_COLOR },
        { name: 'Company Metrics', color: this.RUBY_COLOR },
        { name: 'Candidate Evaluations', color: this.RUBY_COLOR },
        { name: 'Detailed Evaluations', color: this.RUBY_COLOR },
        { name: 'Combined Analysis', color: this.RUBY_COLOR },
        { name: 'Summary Dashboard', color: this.RUBY_COLOR },
        { name: 'API Usage Tracking', color: this.RUBY_COLOR },  // Renamed for clarity
        { name: '_Storage', color: this.RUBY_COLOR }
      ];
      
      // First, create all sheets if they don't exist
      for (const sheetConfig of requiredSheets) {
        try {
          let sheet = sheets.getItemOrNullObject(sheetConfig.name);
          await context.sync();
          
          if (sheet.isNullObject) {
            sheet = sheets.add();
            sheet.name = sheetConfig.name;
            sheet.tabColor = sheetConfig.color;
          } else {
            // Update color if sheet exists
            sheet.tabColor = sheetConfig.color;
          }
        } catch (error) {
          console.error(`Error creating sheet ${sheetConfig.name}:`, error);
        }
      }
      
      await context.sync();
      
      // Now reorder sheets to match the specified order
      for (let i = 0; i < requiredSheets.length; i++) {
        const sheet = sheets.getItem(requiredSheets[i].name);
        sheet.position = i;
      }
      
      await context.sync();
      
      // Initialize all sheets
      await this.initializeStorageSheet();
      await this.initializeAllSheetHeaders();
      await this.initializeProcessedCandidatesSheet();
      await this.initializePDRawTextSheet();
      
      console.log('All sheets created and ordered successfully');
    });
  }
  
  private async initializeProcessedCandidatesSheet(): Promise<void> {
    await Excel.run(async (context) => {
      const sheet = context.workbook.worksheets.getItem('Processed Candidates');
      
      const usedRange = sheet.getUsedRange();
      if (usedRange) {
        usedRange.load('values');
        await context.sync();
        
        // Only initialize if empty or just has headers
        if (!usedRange.values || usedRange.values.length <= 1) {
          const headers = ['Candidate Name', 'CV File ID', 'Cover File ID', 'Processed Date', 'Score', 'Status'];
          const headerRange = sheet.getRange('A1:F1');
          headerRange.values = [headers];
          headerRange.format.font.bold = true;
          headerRange.format.fill.color = this.RUBY_COLOR;
          headerRange.format.font.color = '#FFFFFF';
        }
      } else {
        // Sheet is completely empty, add headers
        const headers = ['Candidate Name', 'CV File ID', 'Cover File ID', 'Processed Date', 'Score', 'Status'];
        const headerRange = sheet.getRange('A1:F1');
        headerRange.values = [headers];
        headerRange.format.font.bold = true;
        headerRange.format.fill.color = this.RUBY_COLOR;
        headerRange.format.font.color = '#FFFFFF';
      }
      
      // Set column widths
      sheet.getRange('A:A').format.columnWidth = 200;
      sheet.getRange('B:C').format.columnWidth = 150;
      sheet.getRange('D:D').format.columnWidth = 120;
      sheet.getRange('E:E').format.columnWidth = 80;
      sheet.getRange('F:F').format.columnWidth = 100;
      
      await context.sync();
    });
  }
  
  private async initializePDRawTextSheet(): Promise<void> {
    await Excel.run(async (context) => {
      const sheet = context.workbook.worksheets.getItem('PD Raw Text');
      
      const headers = ['Date Loaded', 'Position File Name', 'PD Text/Base64', 'Text Length', 'Extraction Method'];
      const headerRange = sheet.getRange('A1:E1');
      headerRange.values = [headers];
      headerRange.format.font.bold = true;
      headerRange.format.fill.color = this.RUBY_COLOR;
      headerRange.format.font.color = '#FFFFFF';
      
      // Set column widths
      sheet.getRange('A:A').format.columnWidth = 120;
      sheet.getRange('B:B').format.columnWidth = 300;
      sheet.getRange('C:C').format.columnWidth = 800;
      sheet.getRange('D:D').format.columnWidth = 100;
      sheet.getRange('E:E').format.columnWidth = 120;
      
      await context.sync();
    });
  }
  
  async addPDRawText(fileName: string, pdText: string, isBase64: boolean = true): Promise<void> {
    await Excel.run(async (context) => {
      const sheet = context.workbook.worksheets.getItem('PD Raw Text');
      
      // Clear existing data (except headers)
      const usedRange = sheet.getUsedRange();
      if (usedRange) {
        usedRange.load('rowCount');
        await context.sync();
        
        if (usedRange.rowCount > 1) {
          const clearRange = sheet.getRangeByIndexes(1, 0, usedRange.rowCount - 1, 5);
          clearRange.clear();
        }
      }
      
      const newRow = [
        new Date().toLocaleDateString(),
        fileName,
        pdText.substring(0, 32767), // Excel cell limit
        pdText.length,
        isBase64 ? 'PDF Base64' : 'Text Extract'
      ];
      
      const dataRange = sheet.getRange('A2:E2');
      dataRange.values = [newRow];
      
      // Apply text wrapping to the text column
      sheet.getCell(1, 2).format.wrapText = true;
      
      await context.sync();
    });
  }
  
  async checkForDuplicates(candidateName: string): Promise<boolean> {
    return await Excel.run(async (context) => {
      try {
        // Check in Processed Candidates sheet
        const processedSheet = context.workbook.worksheets.getItem('Processed Candidates');
        const processedRange = processedSheet.getUsedRange();
        
        if (processedRange) {
          processedRange.load('values');
          await context.sync();
          
          if (processedRange.values && processedRange.values.length > 1) {
            // Normalize the candidate name for comparison
            const normalizedName = this.normalizeName(candidateName);
            
            for (let i = 1; i < processedRange.values.length; i++) {
              const existingName = processedRange.values[i][0];
              if (existingName && this.normalizeName(existingName.toString()) === normalizedName) {
                console.log(`Duplicate found: ${candidateName} already processed`);
                return true;
              }
            }
          }
        }
        
        // Also check in Candidate Evaluations sheet as backup
        const evalSheet = context.workbook.worksheets.getItem('Candidate Evaluations');
        const evalRange = evalSheet.getUsedRange();
        
        if (evalRange) {
          evalRange.load('values');
          await context.sync();
          
          if (evalRange.values && evalRange.values.length > 1) {
            const normalizedName = this.normalizeName(candidateName);
            
            for (let i = 1; i < evalRange.values.length; i++) {
              const existingName = evalRange.values[i][1]; // Candidate name is in column B
              if (existingName && this.normalizeName(existingName.toString()) === normalizedName) {
                console.log(`Duplicate found in evaluations: ${candidateName}`);
                return true;
              }
            }
          }
        }
        
        return false;
        
      } catch (error) {
        console.error('Error checking for duplicates:', error);
        return false;
      }
    });
  }
  
  private normalizeName(name: string): string {
    return name
      .toLowerCase()
      .trim()
      .replace(/\s+/g, ' ')
      .replace(/[^a-z0-9\s]/g, '')
      .split(' ')
      .sort()
      .join(' ');
  }
  
  async recordProcessedCandidate(candidateName: string, cvFileId?: string, coverFileId?: string, score?: number): Promise<void> {
    await Excel.run(async (context) => {
      const sheet = context.workbook.worksheets.getItem('Processed Candidates');
      
      // Find the next available row
      const usedRange = sheet.getUsedRange();
      let nextRow = 2; // Start from row 2 (after headers)
      
      if (usedRange) {
        usedRange.load('rowCount');
        await context.sync();
        nextRow = usedRange.rowCount + 1;
      }
      
      const newRow = [
        candidateName,
        cvFileId || '',
        coverFileId || '',
        new Date().toISOString(),
        score || 0,
        score && score > 0 ? 'Processed' : 'Failed'
      ];
      
      // Add the new row at the next available position
      const dataRange = sheet.getCell(nextRow - 1, 0).getAbsoluteResizedRange(1, 6);
      dataRange.values = [newRow];
      
      // Apply color coding based on status
      const statusCell = sheet.getCell(nextRow - 1, 5);
      if (score && score > 0) {
        statusCell.format.fill.color = '#E8F5E9';
        statusCell.format.font.color = '#2E7D32';
      } else {
        statusCell.format.fill.color = '#FFEBEE';
        statusCell.format.font.color = '#C62828';
      }
      
      await context.sync();
      console.log(`Recorded processed candidate: ${candidateName}`);
    });
  }
  
  private async initializeStorageSheet(): Promise<void> {
    await Excel.run(async (context) => {
      const sheet = context.workbook.worksheets.getItem('_Storage');
      sheet.visibility = Excel.SheetVisibility.hidden;
      
      const headers = [['Key', 'Value', 'Type', 'Updated']];
      const headerRange = sheet.getRange('A1:D1');
      headerRange.values = headers;
      headerRange.format.font.bold = true;
      
      const defaultKeys = [
        ['CLAUDE_API_KEY', '', 'string', new Date().toISOString()],
        ['POSITION_TEXT', '', 'text', new Date().toISOString()],
        ['POSITION_FILE_NAME', '', 'string', new Date().toISOString()],
        ['POSITION_FILE_ID', '', 'string', new Date().toISOString()],
        ['CURRENT_RUBRIC', '', 'json', new Date().toISOString()],
        ['POSITION_PDF_BASE64', '', 'text', new Date().toISOString()]
      ];
      
      const existingData = sheet.getUsedRange();
      if (existingData) {
        existingData.load('rowCount');
        await context.sync();
        
        if (existingData.rowCount <= 1) {
          const dataRange = sheet.getRange('A2:D7');
          dataRange.values = defaultKeys;
        }
      } else {
        const dataRange = sheet.getRange('A2:D7');
        dataRange.values = defaultKeys;
      }
      
      await context.sync();
    });
  }
  
  async initializeAllSheetHeaders(): Promise<void> {
    await this.setupProcessingLogSheet();
    await this.setupDocumentMappingSheet();
    await this.setupPositionConfigSheet();
    await this.setupDynamicRubricSheet();
    await this.setupCompanyMetricsSheet();
    await this.setupCandidateEvaluationsSheet();
    await this.setupDetailedEvaluationsSheet();
    await this.setupCombinedAnalysisSheet();
    await this.setupSummaryDashboardSheet();
    await this.setupAPIUsageSheet();
  }
  
  async setupProcessingLogSheet(): Promise<void> {
    await Excel.run(async (context) => {
      const sheet = context.workbook.worksheets.getItem('Processing Log');
      const headers = ['Timestamp', 'Message', 'Level', 'Details'];
      
      const headerRange = sheet.getRange('A1:D1');
      headerRange.values = [headers];
      headerRange.format.fill.color = this.RUBY_COLOR;
      headerRange.format.font.color = 'white';
      headerRange.format.font.bold = true;
      
      sheet.getRange('A:A').format.columnWidth = 150;
      sheet.getRange('B:B').format.columnWidth = 400;
      sheet.getRange('C:C').format.columnWidth = 80;
      sheet.getRange('D:D').format.columnWidth = 300;
      
      await context.sync();
    });
  }
  
  async setupPositionConfigSheet(): Promise<void> {
    await Excel.run(async (context) => {
      const sheet = context.workbook.worksheets.getItem('Position Configuration');
      const range = sheet.getRange('A1:C1');
      
      range.values = [['Field', 'Value', 'Status']];
      range.format.fill.color = this.RUBY_COLOR;
      range.format.font.color = 'white';
      range.format.font.bold = true;
      
      sheet.getRange('A:A').format.columnWidth = 150;
      sheet.getRange('B:B').format.columnWidth = 350;
      sheet.getRange('C:C').format.columnWidth = 100;
      
      await context.sync();
    });
  }
  
  async setupDynamicRubricSheet(): Promise<void> {
    await Excel.run(async (context) => {
      const sheet = context.workbook.worksheets.getItem('Dynamic Rubric Configuration');
      const headers = [
        'Category',
        'Category Weight (%)',
        'Attribute',
        'Attribute Weight (%)',
        'Description',
        'Score 0',
        'Score 1',
        'Score 2',
        'Score 3',
        'Score 4',
        'Score 5'
      ];
      
      const headerRange = sheet.getRange('A1:K1');
      headerRange.values = [headers];
      headerRange.format.fill.color = this.RUBY_COLOR;
      headerRange.format.font.color = 'white';
      headerRange.format.font.bold = true;
      
      sheet.getRange('A:A').format.columnWidth = 150;
      sheet.getRange('B:B').format.columnWidth = 120;
      sheet.getRange('C:C').format.columnWidth = 200;
      sheet.getRange('D:D').format.columnWidth = 120;
      sheet.getRange('E:E').format.columnWidth = 250;
      sheet.getRange('F:K').format.columnWidth = 150;
      
      await context.sync();
    });
  }
  
  async setupDocumentMappingSheet(): Promise<void> {
    await Excel.run(async (context) => {
      const sheet = context.workbook.worksheets.getItem('Document Mapping');
      const headers = [
        'Candidate Name',
        'CV File',
        'Cover Letter File',
        'Documents Status',
        'Match Method',
        'Match Confidence'
      ];
      
      const headerRange = sheet.getRange('A1:F1');
      headerRange.values = [headers];
      headerRange.format.fill.color = this.RUBY_COLOR;
      headerRange.format.font.color = 'white';
      headerRange.format.font.bold = true;
      
      sheet.getRange('A:A').format.columnWidth = 200;
      sheet.getRange('B:C').format.columnWidth = 250;
      sheet.getRange('D:F').format.columnWidth = 120;
      
      await context.sync();
    });
  }
  
  async setupCandidateEvaluationsSheet(): Promise<void> {
    await Excel.run(async (context) => {
      const sheet = context.workbook.worksheets.getItem('Candidate Evaluations');
      const headers = [
        'Date',
        'Candidate Name',
        'Total Score',
        'Category 1',
        'Category 2',
        'Category 3',
        'Category 4',
        'Category 5',
        'Category 6',
        'Category 7',
        'Category 8',
        'Recommendation',
        'Overall Assessment'
      ];
      
      const headerRange = sheet.getRange('A1:M1');
      headerRange.values = [headers];
      headerRange.format.fill.color = this.RUBY_COLOR;
      headerRange.format.font.color = 'white';
      headerRange.format.font.bold = true;
      
      sheet.getRange('A:A').format.columnWidth = 100;
      sheet.getRange('B:B').format.columnWidth = 200;
      sheet.getRange('C:K').format.columnWidth = 80;
      sheet.getRange('L:L').format.columnWidth = 150;
      sheet.getRange('M:M').format.columnWidth = 400;
      
      await context.sync();
    });
  }
  
  async setupCompanyMetricsSheet(): Promise<void> {
    await Excel.run(async (context) => {
      const sheet = context.workbook.worksheets.getItem('Company Metrics');
      const headers = [
        'Evaluation Date',
        'Candidate Name',
        'Overall Score',
        'Current Company',
        'Time at Current (years)',
        'Advancement at Current',
        'Total Career Experience (years)',
        'Industry Experience (years)',
        'Avg Time per Role (years)',
        'Years Managerial Experience',
        'Max Team Size Managed',
        'Budget Managed',
        'Short Stints (<1yr)',
        'Job Hopping Flag',
        'Notable Companies',
        'Industries Worked',
        'Universities',
        'Degrees',
        'Certifications',
        'Functional Expertise',
        'Technical Skills',
        'X-Factor',
        'Language Quality',
        'Documents Status',
        'Match Confidence'
      ];
      
      const headerRange = sheet.getRange('A1:Y1');
      headerRange.values = [headers];
      headerRange.format.fill.color = this.RUBY_COLOR;
      headerRange.format.font.color = 'white';
      headerRange.format.font.bold = true;
      
      for (let i = 0; i < 25; i++) {
        const column = sheet.getCell(0, i).getEntireColumn();
        column.format.columnWidth = 120;
      }
      
      await context.sync();
    });
  }
  
  async setupDetailedEvaluationsSheet(): Promise<void> {
    await Excel.run(async (context) => {
      const sheet = context.workbook.worksheets.getItem('Detailed Evaluations');
      const headers = [
        'Date',
        'Candidate',
        'Category',
        'Category Weight (%)',
        'Category Score',
        'Attribute',
        'Attribute Weight',
        'Score (0-5)',
        'Evidence'
      ];
      
      const headerRange = sheet.getRange('A1:I1');
      headerRange.values = [headers];
      headerRange.format.fill.color = this.RUBY_COLOR;
      headerRange.format.font.color = 'white';
      headerRange.format.font.bold = true;
      
      sheet.getRange('A:A').format.columnWidth = 100;
      sheet.getRange('B:B').format.columnWidth = 200;
      sheet.getRange('C:C').format.columnWidth = 150;
      sheet.getRange('D:E').format.columnWidth = 100;
      sheet.getRange('F:F').format.columnWidth = 200;
      sheet.getRange('G:H').format.columnWidth = 100;
      sheet.getRange('I:I').format.columnWidth = 400;
      
      await context.sync();
    });
  }
  
  async setupCombinedAnalysisSheet(): Promise<void> {
    await Excel.run(async (context) => {
      const sheet = context.workbook.worksheets.getItem('Combined Analysis');
      const headers = [
        'Candidate',
        'Total Score',
        'Recommendation',
        'Documents Status',
        'Current Company',
        'Industry Experience (years)',
        'Job Hopping Risk',
        'Top Strengths',
        'Development Areas',
        'Next Steps',
        'Interview Questions'
      ];
      
      const headerRange = sheet.getRange('A1:K1');
      headerRange.values = [headers];
      headerRange.format.fill.color = this.RUBY_COLOR;
      headerRange.format.font.color = 'white';
      headerRange.format.font.bold = true;
      
      sheet.getRange('A:A').format.columnWidth = 200;
      sheet.getRange('B:B').format.columnWidth = 100;
      sheet.getRange('C:C').format.columnWidth = 150;
      sheet.getRange('D:D').format.columnWidth = 150;
      sheet.getRange('E:E').format.columnWidth = 200;
      sheet.getRange('F:F').format.columnWidth = 120;
      sheet.getRange('G:G').format.columnWidth = 120;
      sheet.getRange('H:H').format.columnWidth = 400;
      sheet.getRange('I:I').format.columnWidth = 400;
      sheet.getRange('J:J').format.columnWidth = 300;
      sheet.getRange('K:K').format.columnWidth = 600;
      
      await context.sync();
    });
  }
  
  async setupSummaryDashboardSheet(): Promise<void> {
    await Excel.run(async (context) => {
      const sheet = context.workbook.worksheets.getItem('Summary Dashboard');
      
      const titleRange = sheet.getRange('A1');
      titleRange.values = [['RubiRecruit v7.0.0 - Summary Dashboard']];
      titleRange.format.font.size = 18;
      titleRange.format.font.bold = true;
      titleRange.format.font.color = this.RUBY_COLOR;
      
      const dateRange = sheet.getRange('A2');
      dateRange.values = [[`Last Updated: ${new Date().toLocaleString()}`]];
      dateRange.format.font.size = 12;
      dateRange.format.font.color = '#666666';
      
      await context.sync();
    });
  }
  
  async setupAPIUsageSheet(): Promise<void> {
    await Excel.run(async (context) => {
      const sheet = context.workbook.worksheets.getItem('API Usage Tracking');
      const headers = [
        'Timestamp',
        'Candidate',
        'API Call Type',
        'Actual Input Tokens',
        'Actual Output Tokens',
        'Total Tokens',
        'Model',
        'Input Cost ($)',
        'Output Cost ($)',
        'Total Cost ($)',
        'Request ID'
      ];
      
      const headerRange = sheet.getRange('A1:K1');
      headerRange.values = [headers];
      headerRange.format.fill.color = this.RUBY_COLOR;
      headerRange.format.font.color = 'white';
      headerRange.format.font.bold = true;
      
      sheet.getRange('A:A').format.columnWidth = 150;
      sheet.getRange('B:B').format.columnWidth = 200;
      sheet.getRange('C:C').format.columnWidth = 180;
      sheet.getRange('D:E').format.columnWidth = 120;
      sheet.getRange('F:F').format.columnWidth = 120;
      sheet.getRange('G:G').format.columnWidth = 150;
      sheet.getRange('H:J').format.columnWidth = 100;
      sheet.getRange('K:K').format.columnWidth = 200;
      
      await context.sync();
    });
  }
  
  async addCandidateEvaluation(result: EvaluationResult): Promise<void> {
    await Excel.run(async (context) => {
      const sheet = context.workbook.worksheets.getItem('Candidate Evaluations');
      
      const usedRange = sheet.getUsedRange();
      let nextRow = 2;
      
      if (usedRange) {
        usedRange.load('rowCount');
        await context.sync();
        nextRow = usedRange.rowCount + 1;
      }
      
      const categoryNames = Object.keys(result.categories || {});
      const row = [
        new Date().toLocaleDateString(),
        result.candidate_name,
        result.total_score ? result.total_score.toFixed(1) : '0.0'
      ];
      
      // Add 8 category scores
      for (let i = 0; i < 8; i++) {
        if (categoryNames[i] && result.categories[categoryNames[i]]) {
          const category = result.categories[categoryNames[i]];
          const categoryScore = category.category_score || 0;
          row.push(categoryScore.toFixed(1));
        } else {
          row.push('0.0');
        }
      }
      
      row.push(result.recommendation || 'Unable to Evaluate');
      row.push(result.overall_assessment || 'No assessment available');
      
      const dataRange = sheet.getCell(nextRow - 1, 0).getAbsoluteResizedRange(1, row.length);
      dataRange.values = [row];
      
      // Apply color coding based on score
      const scoreCell = sheet.getCell(nextRow - 1, 2);
      const score = result.total_score || 0;
      
      if (score >= 80) {
        scoreCell.format.fill.color = '#4CAF50';
        scoreCell.format.font.color = '#FFFFFF';
      } else if (score >= 70) {
        scoreCell.format.fill.color = '#8BC34A';
      } else if (score >= 60) {
        scoreCell.format.fill.color = '#FFC107';
      } else {
        scoreCell.format.fill.color = '#FF9800';
        scoreCell.format.font.color = '#FFFFFF';
      }
      
      // Apply color coding to category scores
      for (let i = 3; i <= 10; i++) {
        const catScoreCell = sheet.getCell(nextRow - 1, i);
        const catScore = parseFloat(row[i]) || 0;
        
        if (catScore >= 80) {
          catScoreCell.format.fill.color = '#4CAF50';
          catScoreCell.format.font.color = '#FFFFFF';
        } else if (catScore >= 70) {
          catScoreCell.format.fill.color = '#8BC34A';
        } else if (catScore >= 60) {
          catScoreCell.format.fill.color = '#FFC107';
        } else {
          catScoreCell.format.fill.color = '#FF9800';
          catScoreCell.format.font.color = '#FFFFFF';
        }
      }
      
      // Color code recommendation
      const recCell = sheet.getCell(nextRow - 1, 11);
      if (result.recommendation === 'Strong Candidate') {
        recCell.format.fill.color = '#4CAF50';
        recCell.format.font.color = '#FFFFFF';
      } else if (result.recommendation === 'Good Candidate') {
        recCell.format.fill.color = '#8BC34A';
      } else if (result.recommendation === 'Developing Candidate') {
        recCell.format.fill.color = '#FFC107';
      } else {
        recCell.format.fill.color = '#FF9800';
        recCell.format.font.color = '#FFFFFF';
      }
      
      await context.sync();
    });
  }
  
  async addCombinedAnalysis(result: EvaluationResult): Promise<void> {
    await Excel.run(async (context) => {
      const sheet = context.workbook.worksheets.getItem('Combined Analysis');
      const metrics = result.metrics || {};
      
      const usedRange = sheet.getUsedRange();
      let nextRow = 2;
      
      if (usedRange) {
        usedRange.load('rowCount');
        await context.sync();
        nextRow = usedRange.rowCount + 1;
      }
      
      const row = [
        result.candidate_name,
        result.total_score ? result.total_score.toFixed(1) : '0.0',
        result.recommendation || 'Unable to Evaluate',
        result.documentsStatus || 'Unknown',
        metrics.current_company || 'Not found',
        metrics.years_industry_experience || 0,
        metrics.job_hopping_flag ? 'High' : 'Low',
        result.strengths ? result.strengths.join('; ') : 'None identified',
        result.development_areas ? result.development_areas.join('; ') : 'None identified',
        this.getNextSteps(result.total_score),
        result.interview_questions ? result.interview_questions.join('\n') : 'No questions generated'
      ];
      
      const dataRange = sheet.getCell(nextRow - 1, 0).getAbsoluteResizedRange(1, row.length);
      dataRange.values = [row];
      
      // Apply color coding to score
      const scoreCell = sheet.getCell(nextRow - 1, 1);
      const score = typeof result.total_score === 'number' ? result.total_score : parseFloat(result.total_score || '0');
      
      if (score >= 80) {
        scoreCell.format.fill.color = '#4CAF50';
        scoreCell.format.font.color = '#FFFFFF';
      } else if (score >= 70) {
        scoreCell.format.fill.color = '#8BC34A';
      } else if (score >= 60) {
        scoreCell.format.fill.color = '#FFC107';
      } else {
        scoreCell.format.fill.color = '#FF9800';
        scoreCell.format.font.color = '#FFFFFF';
      }
      
      // Color code job hopping risk
      const jobHopCell = sheet.getCell(nextRow - 1, 6);
      if (metrics.job_hopping_flag) {
        jobHopCell.format.fill.color = '#FFCDD2';
      } else {
        jobHopCell.format.fill.color = '#C8E6C9';
      }
      
      // Make interview questions cell wrap text
      const questionsCell = sheet.getCell(nextRow - 1, 10);
      questionsCell.format.wrapText = true;
      
      await context.sync();
    });
  }
  
  async updateAPIUsage(
    candidateName: string,
    callType: string,
    actualInputTokens: number,
    actualOutputTokens: number,
    model: string = 'claude-3-5-sonnet-20241022'
  ): Promise<void> {
    await Excel.run(async (context) => {
      const sheet = context.workbook.worksheets.getItem('API Usage Tracking');
      
      const usedRange = sheet.getUsedRange();
      let nextRow = 2;
      
      if (usedRange) {
        usedRange.load('rowCount');
        await context.sync();
        nextRow = usedRange.rowCount + 1;
      }
      
      // Actual pricing for Claude 3.5 Sonnet
      const INPUT_COST_PER_MILLION = 3.00;
      const OUTPUT_COST_PER_MILLION = 15.00;
      
      const inputCost = (actualInputTokens / 1000000) * INPUT_COST_PER_MILLION;
      const outputCost = (actualOutputTokens / 1000000) * OUTPUT_COST_PER_MILLION;
      const totalCost = inputCost + outputCost;
      
      const row = [
        new Date().toISOString(),
        candidateName,
        callType,
        actualInputTokens,
        actualOutputTokens,
        actualInputTokens + actualOutputTokens,
        model,
        inputCost.toFixed(6),
        outputCost.toFixed(6),
        totalCost.toFixed(6),
        `${Date.now()}-${Math.random().toString(36).substring(2, 11)}` // Request ID
      ];
      
      const dataRange = sheet.getCell(nextRow - 1, 0).getAbsoluteResizedRange(1, row.length);
      dataRange.values = [row];
      
      // Color code based on cost
      const totalCostCell = sheet.getCell(nextRow - 1, 9);
      if (totalCost > 0.5) {
        totalCostCell.format.fill.color = '#FFCDD2'; // Red for high cost
      } else if (totalCost > 0.25) {
        totalCostCell.format.fill.color = '#FFF9C4'; // Yellow for medium cost
      } else {
        totalCostCell.format.fill.color = '#C8E6C9'; // Green for low cost
      }
      
      await context.sync();
    });
  }
  
  async addDetailedEvaluations(result: EvaluationResult): Promise<void> {
    await Excel.run(async (context) => {
      const sheet = context.workbook.worksheets.getItem('Detailed Evaluations');
      
      const usedRange = sheet.getUsedRange();
      let nextRow = 2;
      
      if (usedRange) {
        usedRange.load('rowCount');
        await context.sync();
        nextRow = usedRange.rowCount + 1;
      }
      
      const rows: any[][] = [];
      const evalDate = new Date().toLocaleDateString();
      
      if (result.categories) {
        Object.entries(result.categories).forEach(([categoryName, categoryData]: [string, any]) => {
          const categoryWeight = categoryData.weight || 0;
          const categoryScore = categoryData.category_score || 0;
          
          if (Array.isArray(categoryData.attributes) && categoryData.attributes.length > 0) {
            categoryData.attributes.forEach((attr: any) => {
              rows.push([
                evalDate,
                result.candidate_name,
                categoryName,
                categoryWeight,
                categoryScore.toFixed(1),
                attr.name || 'Unknown',
                attr.weight || 0,
                attr.score !== undefined ? attr.score : 0,
                attr.evidence || 'No evidence provided'
              ]);
            });
          }
        });
      }
      
      if (rows.length > 0) {
        const dataRange = sheet.getCell(nextRow - 1, 0).getAbsoluteResizedRange(rows.length, 9);
        dataRange.values = rows;
        
        // Apply color coding to score columns
        for (let i = 0; i < rows.length; i++) {
          const scoreCell = sheet.getCell(nextRow - 1 + i, 7);
          const score = parseFloat(rows[i][7]) || 0;
          
          if (score >= 4) {
            scoreCell.format.fill.color = '#4CAF50';
            scoreCell.format.font.color = '#FFFFFF';
          } else if (score >= 3) {
            scoreCell.format.fill.color = '#8BC34A';
          } else if (score >= 2) {
            scoreCell.format.fill.color = '#FFC107';
          } else if (score >= 1) {
            scoreCell.format.fill.color = '#FF9800';
            scoreCell.format.font.color = '#FFFFFF';
          } else {
            scoreCell.format.fill.color = '#F44336';
            scoreCell.format.font.color = '#FFFFFF';
          }
        }
      }
      
      await context.sync();
    });
  }
  
  async addCompanyMetrics(result: EvaluationResult): Promise<void> {
    await Excel.run(async (context) => {
      const sheet = context.workbook.worksheets.getItem('Company Metrics');
      const metrics = result.metrics || {};
      
      const usedRange = sheet.getUsedRange();
      let nextRow = 2;
      
      if (usedRange) {
        usedRange.load('rowCount');
        await context.sync();
        nextRow = usedRange.rowCount + 1;
      }
      
      const getArrayValue = (field: any): string => {
        if (Array.isArray(field) && field.length > 0) {
          return field.join('; ');
        } else if (typeof field === 'string' && field.length > 0) {
          return field;
        }
        return '';
      };
      
      const getNumericValue = (field: any, defaultValue: any = 0): any => {
        if (typeof field === 'number') return field;
        if (typeof field === 'string' && !isNaN(parseFloat(field))) return parseFloat(field);
        return defaultValue;
      };
      
      const row = [
        new Date().toLocaleDateString(),
        result.candidate_name,
        result.total_score ? result.total_score.toFixed(1) : '0.0',
        metrics.current_company || metrics.current_employer || 'Not found',
        getNumericValue(metrics.time_at_current_company_years || metrics.current_company_tenure, 0),
        metrics.advancement_current_company || metrics.advancement || 'None evident',
        getNumericValue(metrics.total_career_experience_years || metrics.total_experience, 0),
        getNumericValue(metrics.years_industry_experience || metrics.industry_experience, 0),
        getNumericValue(metrics.avg_time_per_role_years || metrics.avg_tenure, 0),
        getNumericValue(metrics.years_managerial_experience || metrics.management_experience, 0),
        getNumericValue(metrics.max_team_size_managed || metrics.team_size, 0),
        metrics.budget_managed || metrics.budget_responsibility || 'N/A',
        getNumericValue(metrics.short_stints_count || metrics.short_tenures, 0),
        metrics.job_hopping_flag !== undefined ? (metrics.job_hopping_flag ? 'Yes' : 'No') : 'Unknown',
        getArrayValue(metrics.notable_companies || metrics.companies),
        getArrayValue(metrics.industries_worked || metrics.industries),
        getArrayValue(metrics.universities || metrics.education_institutions),
        getArrayValue(metrics.degrees || metrics.education_degrees),
        getArrayValue(metrics.certifications || metrics.professional_certifications),
        getArrayValue(metrics.functional_expertise || metrics.expertise),
        getArrayValue(metrics.technical_skills || metrics.skills),
        getArrayValue(metrics.x_factor || metrics.unique_factors) || 'None identified',
        metrics.language_quality || metrics.communication_quality || 'Unknown',
        result.documentsStatus || 'Unknown',
        result.matchConfidence || 'N/A'
      ];
      
      const dataRange = sheet.getCell(nextRow - 1, 0).getAbsoluteResizedRange(1, row.length);
      dataRange.values = [row];
      
      // Apply color coding
      const scoreCell = sheet.getCell(nextRow - 1, 2);
      const score = typeof result.total_score === 'number' ? result.total_score : parseFloat(result.total_score || '0');
      
      if (score >= 80) {
        scoreCell.format.fill.color = '#4CAF50';
        scoreCell.format.font.color = '#FFFFFF';
      } else if (score >= 70) {
        scoreCell.format.fill.color = '#8BC34A';
      } else if (score >= 60) {
        scoreCell.format.fill.color = '#FFC107';
      } else {
        scoreCell.format.fill.color = '#FF9800';
        scoreCell.format.font.color = '#FFFFFF';
      }
      
      // Color code job hopping
      const jobHopCell = sheet.getCell(nextRow - 1, 13);
      if (metrics.job_hopping_flag) {
        jobHopCell.format.fill.color = '#FFCDD2';
      } else {
        jobHopCell.format.fill.color = '#C8E6C9';
      }
      
      // Color code language quality
      const langCell = sheet.getCell(nextRow - 1, 22);
      switch(metrics.language_quality) {
        case 'Very high':
          langCell.format.fill.color = '#1B5E20';
          langCell.format.font.color = '#FFFFFF';
          break;
        case 'High':
        case 'Professional':
          langCell.format.fill.color = '#4CAF50';
          langCell.format.font.color = '#FFFFFF';
          break;
        case 'Good':
        case 'Medium':
          langCell.format.fill.color = '#FFC107';
          break;
        case 'Average':
        case 'Low':
          langCell.format.fill.color = '#FF9800';
          langCell.format.font.color = '#FFFFFF';
          break;
        case 'Poor':
          langCell.format.fill.color = '#B71C1C';
          langCell.format.font.color = '#FFFFFF';
          break;
      }
      
      await context.sync();
    });
  }
  
  private getNextSteps(score: number): string {
    if (score >= 80) {
      return 'Schedule first interview immediately; Fast-track through process';
    } else if (score >= 70) {
      return 'Schedule first interview; Standard process';
    } else if (score >= 60) {
      return 'Consider for interview if strong in key areas; Additional screening may be needed';
    } else {
      return 'Not recommended for interview at this time';
    }
  }
  
  async writeDynamicRubric(rubric: any): Promise<void> {
    await Excel.run(async (context) => {
      const sheet = context.workbook.worksheets.getItem('Dynamic Rubric Configuration');
      
      const usedRange = sheet.getUsedRange();
      if (usedRange) {
        usedRange.load('rowCount');
        await context.sync();
        
        if (usedRange.rowCount > 1) {
          const clearRange = sheet.getCell(1, 0).getAbsoluteResizedRange(usedRange.rowCount - 1, 11);
          clearRange.clear();
        }
      }
      
      const rows: any[][] = [];
      const categoryRows: number[] = [];
      
      Object.entries(rubric).forEach(([categoryName, category]: [string, any]) => {
        category.attributes.forEach((attr: any, index: number) => {
          if (index === 0) {
            categoryRows.push(rows.length + 1);
          }
          rows.push([
            index === 0 ? categoryName : '',
            index === 0 ? category.weight.toFixed(1) : '',
            attr.name,
            attr.weight.toFixed(2),
            attr.description,
            attr.rubric['0'] || '',
            attr.rubric['1'] || '',
            attr.rubric['2'] || '',
            attr.rubric['3'] || '',
            attr.rubric['4'] || '',
            attr.rubric['5'] || ''
          ]);
        });
      });
      
      if (rows.length > 0) {
        const dataRange = sheet.getCell(1, 0).getAbsoluteResizedRange(rows.length, 11);
        dataRange.values = rows;
        
        categoryRows.forEach(rowIndex => {
          const rowRange = sheet.getCell(rowIndex, 0).getAbsoluteResizedRange(1, 11);
          rowRange.format.fill.color = '#E8F5E9';
          rowRange.format.font.bold = true;
        });
      }
      
      sheet.getUsedRange()?.format.autofitColumns();
      await context.sync();
    });
  }
  
  async updateProcessingLog(message: string, level: string = 'Info'): Promise<void> {
    await Excel.run(async (context) => {
      const sheet = context.workbook.worksheets.getItem('Processing Log');
      
      const usedRange = sheet.getUsedRange();
      let nextRow = 2;
      
      if (usedRange) {
        usedRange.load('rowCount');
        await context.sync();
        nextRow = usedRange.rowCount + 1;
      }
      
      const row = [
        new Date().toLocaleString(),
        message,
        level,
        ''
      ];
      
      const dataRange = sheet.getCell(nextRow - 1, 0).getAbsoluteResizedRange(1, 4);
      dataRange.values = [row];
      
      // Apply color based on level
      if (level === 'Error') {
        dataRange.format.font.color = '#D32F2F';
      } else if (level === 'Warning') {
        dataRange.format.font.color = '#F57C00';
      } else if (level === 'Success') {
        dataRange.format.font.color = '#388E3C';
      }
      
      await context.sync();
    });
  }
  
  async updateSummaryDashboard(): Promise<void> {
    await Excel.run(async (context) => {
      const dashboardSheet = context.workbook.worksheets.getItem('Summary Dashboard');
      const evalSheet = context.workbook.worksheets.getItem('Candidate Evaluations');
      
      const clearRange = dashboardSheet.getRange('A:J');
      clearRange.clear();
      
      // Title
      const titleRange = dashboardSheet.getRange('A1');
      titleRange.values = [['RubiRecruit v7.0.0 - Summary Dashboard']];
      titleRange.format.font.size = 18;
      titleRange.format.font.bold = true;
      titleRange.format.font.color = this.RUBY_COLOR;
      
      const dateRange = dashboardSheet.getRange('A2');
      dateRange.values = [[`Last Updated: ${new Date().toLocaleString()}`]];
      dateRange.format.font.size = 12;
      dateRange.format.font.color = '#666666';
      
      // Get statistics
      const evalUsedRange = evalSheet.getUsedRange();
      if (evalUsedRange) {
        evalUsedRange.load('values, rowCount');
        await context.sync();
        
        if (evalUsedRange.rowCount > 1) {
          const data = evalUsedRange.values;
          const candidates = [];
          let totalScore = 0;
          let countByRecommendation: { [key: string]: number } = {};
          
          for (let i = 1; i < data.length; i++) {
            const name = data[i][1];
            const score = parseFloat(data[i][2] as string) || 0;
            const recommendation = data[i][11] as string;
            
            if (name) {
              candidates.push({ name, score, recommendation });
              totalScore += score;
              countByRecommendation[recommendation] = (countByRecommendation[recommendation] || 0) + 1;
            }
          }
          
          // Sort candidates by score
          candidates.sort((a, b) => b.score - a.score);
          
          // Write statistics
          let row = 4;
          const statsTitle = dashboardSheet.getRange(`A${row}`);
          statsTitle.values = [['OVERALL STATISTICS']];
          statsTitle.format.font.size = 14;
          statsTitle.format.font.bold = true;
          statsTitle.format.fill.color = this.RUBY_COLOR;
          statsTitle.format.font.color = '#FFFFFF';
          
          row++;
          const statsData = [
            ['Total Candidates', candidates.length],
            ['Average Score', candidates.length > 0 ? (totalScore / candidates.length).toFixed(1) : 'N/A']
          ];
          
          const statsRange = dashboardSheet.getRange(`A${row}:B${row + statsData.length - 1}`);
          statsRange.values = statsData;
          row += statsData.length + 2;
          
          // Recommendations distribution
          if (Object.keys(countByRecommendation).length > 0) {
            const recTitle = dashboardSheet.getRange(`A${row}`);
            recTitle.values = [['RECOMMENDATIONS DISTRIBUTION']];
            recTitle.format.font.size = 14;
            recTitle.format.font.bold = true;
            recTitle.format.fill.color = this.RUBY_COLOR;
            recTitle.format.font.color = '#FFFFFF';
            
            row++;
            Object.entries(countByRecommendation).forEach(([rec, count]) => {
              const recRange = dashboardSheet.getRange(`A${row}:B${row}`);
              recRange.values = [[rec, count]];
              
              if (rec === 'Strong Candidate') {
                recRange.format.fill.color = '#4CAF50';
                recRange.format.font.color = '#FFFFFF';
              } else if (rec === 'Good Candidate') {
                recRange.format.fill.color = '#8BC34A';
              } else if (rec === 'Developing Candidate') {
                recRange.format.fill.color = '#FFC107';
              } else {
                recRange.format.fill.color = '#FF9800';
                recRange.format.font.color = '#FFFFFF';
              }
              row++;
            });
            row += 2;
          }
          
          // Top candidates
          if (candidates.length > 0) {
            const topTitle = dashboardSheet.getRange(`A${row}`);
            topTitle.values = [['TOP CANDIDATES']];
            topTitle.format.font.size = 14;
            topTitle.format.font.bold = true;
            topTitle.format.fill.color = this.RUBY_COLOR;
            topTitle.format.font.color = '#FFFFFF';
            
            row++;
            const headerRange = dashboardSheet.getRange(`A${row}:D${row}`);
            headerRange.values = [['Rank', 'Name', 'Score', 'Recommendation']];
            headerRange.format.font.bold = true;
            headerRange.format.fill.color = '#E3F2FD';
            row++;
            
            const topCandidates = candidates.slice(0, 10);
            const topData = topCandidates.map((c, index) => [
              index + 1,
              c.name,
              c.score.toFixed(1),
              c.recommendation
            ]);
            
            if (topData.length > 0) {
              const topRange = dashboardSheet.getRange(`A${row}:D${row + topData.length - 1}`);
              topRange.values = topData;
              
              // Apply color coding to scores
              for (let i = 0; i < topData.length; i++) {
                const scoreCell = dashboardSheet.getRange(`C${row + i}`);
                const score = parseFloat(topData[i][2] as string);
                
                if (score >= 80) {
                  scoreCell.format.fill.color = '#4CAF50';
                  scoreCell.format.font.color = '#FFFFFF';
                } else if (score >= 70) {
                  scoreCell.format.fill.color = '#8BC34A';
                } else if (score >= 60) {
                  scoreCell.format.fill.color = '#FFC107';
                } else {
                  scoreCell.format.fill.color = '#FF9800';
                  scoreCell.format.font.color = '#FFFFFF';
                }
              }
            }
          }
        }
      }
      
      dashboardSheet.getUsedRange()?.format.autofitColumns();
      await context.sync();
    });
  }
  
  async mapDocuments(): Promise<void> {
    await Excel.run(async (context) => {
      const sheet = context.workbook.worksheets.getItem('Document Mapping');
      
      const usedRange = sheet.getUsedRange();
      if (usedRange) {
        usedRange.load('rowCount');
        await context.sync();
        if (usedRange.rowCount > 1) {
          const clearRange = sheet.getCell(1, 0).getAbsoluteResizedRange(usedRange.rowCount - 1, 6);
          clearRange.clear();
        }
      }
      
      await context.sync();
    });
  }
  
  async getStoredValue(key: string): Promise<string | null> {
    return await Excel.run(async (context) => {
      try {
        // First try sessionStorage for large data like PDFs
        if (typeof(Storage) !== "undefined") {
          const sessionValue = sessionStorage.getItem(key);
          if (sessionValue) {
            console.log(`Retrieved ${key} from sessionStorage, length: ${sessionValue.length}`);
            return sessionValue;
          }
        }
        
        // Then try the storage sheet
        const sheet = context.workbook.worksheets.getItem('_Storage');
        const dataRange = sheet.getUsedRange();
        
        if (!dataRange) {
          console.log(`No data in storage sheet for ${key}`);
          return null;
        }
        
        dataRange.load(['values', 'rowCount']);
        await context.sync();
        
        if (dataRange.rowCount > 1) {
          const data = dataRange.values as any[][];
          for (let i = 1; i < data.length; i++) {
            if (data[i][0] === key) {
              const value = data[i][1];
              console.log(`Retrieved ${key} from sheet, length: ${value ? value.toString().length : 0}`);
              return value ? value.toString() : null;
            }
          }
        }
        
        console.log(`Key ${key} not found in storage`);
        return null;
        
      } catch (error) {
        console.error(`Error getting stored value for ${key}:`, error);
        
        // Fallback to localStorage
        if (typeof(Storage) !== "undefined") {
          const localValue = localStorage.getItem(key);
          if (localValue) {
            console.log(`Retrieved ${key} from localStorage (fallback)`);
            return localValue;
          }
        }
        
        return null;
      }
    });
  }
  
  async setStoredValue(key: string, value: string): Promise<void> {
    await Excel.run(async (context) => {
      try {
        // For large data (PDFs), use sessionStorage
        if (key === 'POSITION_TEXT' || key === 'POSITION_PDF_BASE64') {
          if (typeof(Storage) !== "undefined") {
            sessionStorage.setItem(key, value);
            console.log(`Stored ${key} in sessionStorage, length: ${value.length}`);
            
            // Also store a marker in the sheet
            const sheet = context.workbook.worksheets.getItem('_Storage');
            const dataRange = sheet.getUsedRange();
            
            if (dataRange) {
              dataRange.load(['values', 'rowCount']);
              await context.sync();
              
              let found = false;
              const data = dataRange.values as any[][];
              
              for (let i = 1; i < data.length; i++) {
                if (data[i][0] === key) {
                  const valueCell = sheet.getCell(i, 1);
                  valueCell.values = [['[Stored in sessionStorage]']];
                  const dateCell = sheet.getCell(i, 3);
                  dateCell.values = [[new Date().toISOString()]];
                  found = true;
                  break;
                }
              }
              
              if (!found) {
                const newRow = sheet.getCell(dataRange.rowCount, 0).getAbsoluteResizedRange(1, 4);
                newRow.values = [[key, '[Stored in sessionStorage]', 'text', new Date().toISOString()]];
              }
              
              await context.sync();
            }
            
            return;
          }
        }
        
        // For smaller data, use the sheet
        const sheet = context.workbook.worksheets.getItem('_Storage');
        const dataRange = sheet.getUsedRange();
        
        if (!dataRange) {
          const range = sheet.getRange('A2:D2');
          range.values = [[key, value, 'string', new Date().toISOString()]];
          await context.sync();
          return;
        }
        
        dataRange.load(['values', 'rowCount']);
        await context.sync();
        
        let found = false;
        const data = dataRange.values as any[][];
        
        for (let i = 1; i < data.length; i++) {
          if (data[i][0] === key) {
            const valueCell = sheet.getCell(i, 1);
            valueCell.values = [[value]];
            const dateCell = sheet.getCell(i, 3);
            dateCell.values = [[new Date().toISOString()]];
            found = true;
            break;
          }
        }
        
        if (!found) {
          const newRow = sheet.getCell(dataRange.rowCount, 0).getAbsoluteResizedRange(1, 4);
          newRow.values = [[key, value, 'string', new Date().toISOString()]];
        }
        
        await context.sync();
        console.log(`Stored ${key} in sheet`);
        
      } catch (error) {
        console.error(`Error setting stored value for ${key}:`, error);
        
        // Fallback to localStorage
        if (typeof(Storage) !== "undefined") {
          localStorage.setItem(key, value);
          console.log(`Stored ${key} in localStorage (fallback)`);
        }
      }
    });
  }
  
}

export default new ExcelService();