class LocalFileService {
  private positionFiles: Map<string, File> = new Map();
  private cvFiles: Map<string, File> = new Map();
  private fileContents: Map<string, string> = new Map();
  private documentMapping: Map<string, {cv: File | null, coverLetter: File | null}> = new Map();
  
  async selectPositionFile(): Promise<{id: string, name: string, content: string}> {
    console.log('[LocalFileService] Selecting position file...');
    
    return new Promise((resolve, reject) => {
      const input = document.getElementById('positionFilePicker') as HTMLInputElement;
      if (!input) {
        reject('File picker not found in DOM');
        return;
      }
      
      input.onchange = async (event: any) => {
        const file = event.target.files[0];
        if (!file) {
          reject('No file selected');
          return;
        }
        
        console.log('[LocalFileService] File selected:', file.name, 'Size:', file.size);
        
        try {
          const arrayBuffer = await this.readFileAsArrayBuffer(file);
          const base64 = this.arrayBufferToBase64(arrayBuffer);
          
          const fileId = 'local_position_' + Date.now();
          
          this.positionFiles.set(fileId, file);
          this.fileContents.set(fileId, base64);
          
          console.log('[LocalFileService] File processed, base64 length:', base64.length);
          
          input.value = '';
          
          resolve({
            id: fileId,
            name: file.name,
            content: base64
          });
        } catch (error) {
          console.error('[LocalFileService] Error processing file:', error);
          reject(error);
        }
      };
      
      input.click();
    });
  }
  
  async selectCVFiles(): Promise<Array<{id: string, name: string, type: string}>> {
    console.log('[LocalFileService] Selecting CV files...');
    
    return new Promise((resolve, reject) => {
      const input = document.getElementById('cvFilesPicker') as HTMLInputElement;
      if (!input) {
        reject('File picker not found in DOM');
        return;
      }
      
      input.onchange = async (event: any) => {
        const files = Array.from(event.target.files || []);
        if (!files.length) {
          reject('No files selected');
          return;
        }
        
        console.log('[LocalFileService] Files selected:', files.length);
        
        const fileList = [];
        for (let i = 0; i < files.length; i++) {
          const file = files[i] as File;
          
          try {
            const arrayBuffer = await this.readFileAsArrayBuffer(file);
            const base64 = this.arrayBufferToBase64(arrayBuffer);
            
            const fileId = `local_cv_${Date.now()}_${i}`;
            this.cvFiles.set(fileId, file);
            this.fileContents.set(fileId, base64);
            
            fileList.push({
              id: fileId,
              name: file.name,
              type: this.detectDocumentType(file.name)
            });
            
            console.log('[LocalFileService] Processed file:', file.name);
          } catch (error) {
            console.error('[LocalFileService] Error processing file:', file.name, error);
          }
        }
        
        input.value = '';
        
        // Perform document matching after loading files
        this.performDocumentMatching();
        
        resolve(fileList);
      };
      
      input.click();
    });
  }
  
  private performDocumentMatching(): void {
    console.log('[LocalFileService] Starting document matching...');
    this.documentMapping.clear();
    
    const cvs: File[] = [];
    const coverLetters: File[] = [];
    
    // Separate CVs and cover letters
    this.cvFiles.forEach((file, id) => {
      const type = this.detectDocumentType(file.name);
      if (type === 'cv') {
        cvs.push(file);
      } else {
        coverLetters.push(file);
      }
    });
    
    console.log(`[LocalFileService] Found ${cvs.length} CVs and ${coverLetters.length} cover letters`);
    
    // Match CVs with cover letters
    cvs.forEach(cv => {
      const candidateName = this.extractCandidateName(cv.name);
      const matchedCover = this.findBestMatch(candidateName, coverLetters);
      
      this.documentMapping.set(candidateName, {
        cv: cv,
        coverLetter: matchedCover
      });
      
      if (matchedCover) {
        // Remove matched cover letter from pool
        const index = coverLetters.indexOf(matchedCover);
        if (index > -1) {
          coverLetters.splice(index, 1);
        }
      }
    });
    
    console.log(`[LocalFileService] Matched ${this.documentMapping.size} candidates`);
  }
  
  private findBestMatch(candidateName: string, coverLetters: File[]): File | null {
    let bestMatch: File | null = null;
    let bestScore = 0;
    
    coverLetters.forEach(cover => {
      const coverName = this.extractCandidateName(cover.name);
      const score = this.calculateSimilarity(candidateName, coverName);
      
      if (score > bestScore && score > 0.7) { // 70% similarity threshold
        bestScore = score;
        bestMatch = cover;
      }
    });
    
    return bestMatch;
  }
  
  private calculateSimilarity(str1: string, str2: string): number {
    const s1 = str1.toLowerCase().replace(/[^a-z0-9]/g, '');
    const s2 = str2.toLowerCase().replace(/[^a-z0-9]/g, '');
    
    if (s1 === s2) return 1;
    if (s1.length === 0 || s2.length === 0) return 0;
    
    const longer = s1.length > s2.length ? s1 : s2;
    const shorter = s1.length > s2.length ? s2 : s1;
    
    const editDistance = this.getEditDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }
  
  private getEditDistance(s1: string, s2: string): number {
    const costs: number[] = [];
    for (let i = 0; i <= s1.length; i++) {
      let lastValue = i;
      for (let j = 0; j <= s2.length; j++) {
        if (i === 0) {
          costs[j] = j;
        } else if (j > 0) {
          let newValue = costs[j - 1];
          if (s1.charAt(i - 1) !== s2.charAt(j - 1)) {
            newValue = Math.min(Math.min(newValue, lastValue), costs[j]) + 1;
          }
          costs[j - 1] = lastValue;
          lastValue = newValue;
        }
      }
      if (i > 0) costs[s2.length] = lastValue;
    }
    return costs[s2.length];
  }
  
  private extractCandidateName(fileName: string): string {
    let name = fileName.replace(/\.[^/.]+$/, '');
    name = name.replace(/[\-_]?(cv|resume|cover[\s_-]?letter|cl|curriculum[\s_-]?vitae|motivation)/gi, '');
    name = name.replace(/[\-_]?(senior|junior|manager|coordinator|specialist|analyst|developer|engineer|consultant|administrator|director|executive|assistant|associate)/gi, '');
    name = name.replace(/[\d]{2,4}[\-\/]?[\d]{0,2}[\-\/]?[\d]{0,4}/g, '');
    name = name.replace(/v?\d+(\.\d+)?/gi, '');
    name = name.replace(/[\-_]+/g, ' ');
    name = name.trim();
    name = name.split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
    
    return name || 'Unknown Candidate';
  }
  
  private readFileAsArrayBuffer(file: File): Promise<ArrayBuffer> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as ArrayBuffer);
      reader.onerror = () => reject(reader.error);
      reader.readAsArrayBuffer(file);
    });
  }
  
  private arrayBufferToBase64(arrayBuffer: ArrayBuffer): string {
    const bytes = new Uint8Array(arrayBuffer);
    let binary = '';
    bytes.forEach(byte => binary += String.fromCharCode(byte));
    return btoa(binary);
  }
  
  async getFileContent(fileId: string): Promise<string> {
    const cached = this.fileContents.get(fileId);
    if (cached) {
      return cached;
    }
    
    const file = this.positionFiles.get(fileId) || this.cvFiles.get(fileId);
    if (!file) {
      throw new Error('File not found: ' + fileId);
    }
    
    const arrayBuffer = await this.readFileAsArrayBuffer(file);
    const base64 = this.arrayBufferToBase64(arrayBuffer);
    this.fileContents.set(fileId, base64);
    
    return base64;
  }
  
  async getFileByName(fileName: string): Promise<{id: string, content: string} | null> {
    for (const [id, file] of this.cvFiles.entries()) {
      if (file.name === fileName) {
        const content = await this.getFileContent(id);
        return { id, content };
      }
    }
    
    for (const [id, file] of this.positionFiles.entries()) {
      if (file.name === fileName) {
        const content = await this.getFileContent(id);
        return { id, content };
      }
    }
    
    return null;
  }
  
  private detectDocumentType(fileName: string): string {
    const lowerName = fileName.toLowerCase();
    if (lowerName.includes('cover') || 
        lowerName.includes('cl_') || 
        lowerName.includes('letter') || 
        lowerName.includes('motivation')) {
      return 'cover';
    }
    return 'cv';
  }
  
  getAllCVFiles(): Array<{id: string, name: string, file: File}> {
    return Array.from(this.cvFiles.entries()).map(([id, file]) => ({
      id,
      name: file.name,
      file
    }));
  }
  
  getDocumentMapping(): Map<string, {cv: File | null, coverLetter: File | null}> {
    return this.documentMapping;
  }
  
  clearAll(): void {
    this.positionFiles.clear();
    this.cvFiles.clear();
    this.fileContents.clear();
    this.documentMapping.clear();
    console.log('[LocalFileService] All files cleared');
  }
  
  getStoredFilesCount(): {positions: number, cvs: number} {
    return {
      positions: this.positionFiles.size,
      cvs: this.cvFiles.size
    };
  }
}

export default new LocalFileService();