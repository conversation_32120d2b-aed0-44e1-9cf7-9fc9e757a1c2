import { PublicClientApplication, InteractionRequiredAuthError, AccountInfo, AuthenticationResult } from '@azure/msal-browser';
import { Config } from '../config/config';

class AuthService {
  private msalInstance: PublicClientApplication | null = null;
  private initialized: boolean = false;
  private currentAccount: AccountInfo | null = null;
  private tokenCache: Map<string, { token: string; expiry: number }> = new Map();
  private useLocalFiles: boolean = true; // LOCAL MODE FLAG
  
  constructor() {
    // Skip MSAL initialization in local mode
    if (!this.useLocalFiles) {
      const msalConfig = {
        auth: {
          clientId: Config.AZURE_CLIENT_ID,
          authority: `https://login.microsoftonline.com/${Config.AZURE_TENANT_ID}`,
          redirectUri: Config.AZURE_REDIRECT_URI,
          postLogoutRedirectUri: Config.AZURE_REDIRECT_URI,
          navigateToLoginRequestUrl: false
        },
        cache: {
          cacheLocation: 'localStorage',
          storeAuthStateInCookie: true,
          secureCookies: false
        }
      };
      
      this.msalInstance = new PublicClientApplication(msalConfig);
    }
  }
  
  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }
    
    if (this.useLocalFiles) {
      console.log('Running in LOCAL FILE MODE - skipping MSAL initialization');
      this.initialized = true;
      return;
    }
    
    // Original MSAL initialization code here (if needed later)
    if (this.msalInstance) {
      try {
        await this.msalInstance.initialize();
        const response = await this.msalInstance.handleRedirectPromise();
        if (response && response.account) {
          this.currentAccount = response.account;
        }
        const accounts = this.msalInstance.getAllAccounts();
        if (accounts.length > 0) {
          this.currentAccount = accounts[0];
          this.msalInstance.setActiveAccount(this.currentAccount);
        }
        this.initialized = true;
        console.log('Auth service initialized successfully');
      } catch (error) {
        console.error('Failed to initialize auth service:', error);
        throw error;
      }
    }
  }
  
  async login(): Promise<string> {
    if (this.useLocalFiles) {
      console.log('Local file mode - no login required');
      return 'local-mode-token';
    }
    
    // Original login code here (if needed)
    throw new Error('OneDrive login not available in local mode');
  }
  
  async getToken(forceRefresh: boolean = false): Promise<string> {
    if (this.useLocalFiles) {
      return 'local-mode-token';
    }
    
    // Original token code here (if needed)
    throw new Error('OneDrive tokens not available in local mode');
  }
  
  async logout(): Promise<void> {
    this.currentAccount = null;
    this.tokenCache.clear();
  }
  
  getCurrentAccount(): AccountInfo | null {
    if (this.useLocalFiles) {
      return {
        homeAccountId: 'local',
        environment: 'local',
        tenantId: 'local',
        username: 'Local File Mode',
        localAccountId: 'local',
        name: 'Local User',
        idTokenClaims: {}
      } as AccountInfo;
    }
    return this.currentAccount;
  }
  
  isAuthenticated(): boolean {
    if (this.useLocalFiles) {
      return true; // Always "authenticated" in local mode
    }
    return this.msalInstance ? this.msalInstance.getAllAccounts().length > 0 : false;
  }
  
  getAllAccounts(): AccountInfo[] {
    if (this.useLocalFiles) {
      return [this.getCurrentAccount()!];
    }
    return this.msalInstance ? this.msalInstance.getAllAccounts() : [];
  }
  
  isLocalMode(): boolean {
    return this.useLocalFiles;
  }
  
  setLocalMode(useLocal: boolean): void {
    this.useLocalFiles = useLocal;
    console.log(`File mode set to: ${useLocal ? 'LOCAL' : 'ONEDRIVE'}`);
  }
}

export default new AuthService();