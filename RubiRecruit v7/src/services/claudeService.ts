import { Config } from '../config/config';

interface RubricCategory {
  weight: number;
  importance: string;
  attributes: RubricAttribute[];
}

interface RubricAttribute {
  name: string;
  weight: number;
  weight_in_category: number;
  description: string;
  rubric: {
    '0': string;
    '1': string;
    '2': string;
    '3': string;
    '4': string;
    '5': string;
  };
}

interface Rubric {
  [categoryName: string]: RubricCategory;
}

interface EvaluationResult {
  candidate_name: string;
  total_score: number;
  recommendation: string;
  categories: {
    [categoryName: string]: {
      category_score: number;
      weight: number;
      attributes: Array<{
        name: string;
        score: number;
        weight: number;
        evidence: string;
      }>;
    };
  };
  strengths: string[];
  development_areas: string[];
  overall_assessment: string;
  metrics?: any;
  interview_questions?: string[];
  actualTokensUsed?: {
    input: number;
    output: number;
    total: number;
  };
}

class ClaudeService {
  private apiKey: string = '';
  private readonly apiUrl = 'http://localhost:3001/api/claude';
  private lastApiCall: number = 0;
  private tokenUsage: { timestamp: number; tokens: number }[] = [];
  private readonly RATE_LIMIT_TOKENS = 75000;
  private readonly RATE_LIMIT_WINDOW = 60000;
  private readonly MIN_DELAY_BETWEEN_CALLS = 3000;
  
  setApiKey(key: string) {
    this.apiKey = key;
    console.log('[ClaudeService] API key set:', key.substring(0, 10) + '...');
  }
  
  private async waitForRateLimit(estimatedTokens: number): Promise<void> {
    const now = Date.now();
    this.tokenUsage = this.tokenUsage.filter(usage => 
      now - usage.timestamp < this.RATE_LIMIT_WINDOW
    );
    
    const currentUsage = this.tokenUsage.reduce((sum, usage) => sum + usage.tokens, 0);
    console.log(`[ClaudeService] Current token usage: ${currentUsage}/${this.RATE_LIMIT_TOKENS} in last minute`);
    
    if (currentUsage + estimatedTokens > this.RATE_LIMIT_TOKENS) {
      const oldestUsage = this.tokenUsage[0];
      if (oldestUsage) {
        const waitTime = this.RATE_LIMIT_WINDOW - (now - oldestUsage.timestamp) + 5000;
        console.log(`[ClaudeService] Rate limit would be exceeded. Waiting ${waitTime}ms...`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
        return this.waitForRateLimit(estimatedTokens);
      }
    }
    
    const timeSinceLastCall = now - this.lastApiCall;
    if (timeSinceLastCall < this.MIN_DELAY_BETWEEN_CALLS) {
      const waitTime = this.MIN_DELAY_BETWEEN_CALLS - timeSinceLastCall;
      console.log(`[ClaudeService] Waiting ${waitTime}ms for minimum delay between calls...`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }
  
  private recordTokenUsage(inputTokens: number, outputTokens: number): void {
    const totalTokens = inputTokens + outputTokens;
    this.tokenUsage.push({
      timestamp: Date.now(),
      tokens: totalTokens
    });
    this.lastApiCall = Date.now();
    console.log(`[ClaudeService] Recorded token usage - Input: ${inputTokens}, Output: ${outputTokens}, Total: ${totalTokens}`);
  }
  
  private estimateTokens(text: string): number {
    return Math.ceil(text.length / 3.5);
  }
  
  async testProxyConnection(): Promise<boolean> {
    try {
      const response = await fetch('http://localhost:3001/health');
      const data = await response.json();
      console.log('[ClaudeService] Proxy health check:', data);
      return data.status === 'OK';
    } catch (error) {
      console.error('[ClaudeService] Proxy not running:', error);
      return false;
    }
  }
  
  async generateRubric(positionText: string, positionTitle: string = 'Unknown Position'): Promise<Rubric> {
    console.log('[ClaudeService.generateRubric] Starting');
    console.log('[ClaudeService] Position title:', positionTitle);
    
    if (!this.apiKey) {
      throw new Error('API key not configured');
    }
    
    const proxyAvailable = await this.testProxyConnection();
    if (!proxyAvailable) {
      throw new Error('Proxy server not running. Run "npm run proxy" in a separate terminal.');
    }
    
    let messageContent: any;
    const storedPDFBase64 = sessionStorage.getItem('currentPDFBase64');
    
    if (storedPDFBase64) {
      console.log('[ClaudeService] Using stored PDF base64');
      const estimatedTokens = this.estimateTokens(storedPDFBase64) + 3000;
      await this.waitForRateLimit(estimatedTokens);
      
      messageContent = [
        {
          type: "document",
          source: {
            type: "base64",
            media_type: "application/pdf",
            data: storedPDFBase64
          }
        },
        {
          type: "text",
          text: `You are a world-leading expert HR consultant creating a comprehensive evaluation rubric for candidate assessment.

POSITION: ${positionTitle}

Analyze the PDF document above and create a detailed candidate evaluation rubric with EXACTLY 8 categories that are most critical for success in this specific role.

CRITICAL REQUIREMENTS:
1. EXACTLY 8 categories - each must be highly relevant to THIS position
2. Each category should have a weight between 10% and 20% based on importance
3. Categories MUST sum to exactly 100%
4. EXACTLY 5 attributes per category
5. Attribute weights within each category should vary based on importance
6. Attributes within a category must sum to the category's total weight

For each attribute's rubric scores (0-5), create SPECIFIC, MEASURABLE descriptions:
- Score 0: Complete absence (e.g., "No mention of project management experience or methodologies")
- Score 1: Minimal presence (e.g., "Less than 1 year experience, basic awareness of PM concepts")
- Score 2: Below expectations (e.g., "1-2 years experience, limited to small projects under 5 team members")
- Score 3: Meets requirements (e.g., "3-5 years experience, managed projects with 5-10 team members")
- Score 4: Exceeds expectations (e.g., "5-8 years experience, led complex projects with 10+ team members")
- Score 5: Outstanding (e.g., "8+ years experience, proven track record of enterprise-level project leadership")

Return ONLY valid JSON in this EXACT format:
{
  "Category Name 1": {
    "weight": 15.0,
    "importance": "critical",
    "attributes": [
      {
        "name": "Specific Attribute Name",
        "weight": 3.0,
        "weight_in_category": 0.20,
        "description": "Clear description of what this measures",
        "rubric": {
          "0": "Specific measurable criteria for no evidence",
          "1": "Specific measurable criteria for minimal evidence",
          "2": "Specific measurable criteria for below expectations",
          "3": "Specific measurable criteria for meets requirements",
          "4": "Specific measurable criteria for exceeds expectations",
          "5": "Specific measurable criteria for exceptional performance"
        }
      }
    ]
  }
}`
        }
      ];
    } else {
      const truncatedText = positionText.substring(0, 8000);
      const estimatedTokens = this.estimateTokens(truncatedText) + 3000;
      await this.waitForRateLimit(estimatedTokens);
      
      const prompt = `Create a candidate evaluation rubric for position: ${positionTitle}

Position text: ${truncatedText}

Create EXACTLY 8 categories with 5 attributes each. Categories must sum to 100% weight.
Each rubric score (0-5) must have SPECIFIC, MEASURABLE criteria, not generic descriptions.`;
      
      messageContent = prompt;
    }
    
    try {
      console.log('[ClaudeService] Making API request through proxy...');
      
      const response = await fetch(this.apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': this.apiKey
        },
        body: JSON.stringify({
          model: Config.CLAUDE_MODEL,
          max_tokens: 8000,
          temperature: 0.3,
          messages: [{
            role: 'user',
            content: messageContent
          }]
        })
      });
      
      if (!response.ok) {
        let errorMessage = `API error: ${response.status}`;
        try {
          const errorData = await response.json();
          errorMessage = errorData.error?.message || JSON.stringify(errorData);
        } catch (e) {
          // If we can't parse the error, use the status
        }
        
        if (response.status === 429) {
          throw new Error('Rate limit exceeded. Please wait a minute and try again.');
        }
        throw new Error(errorMessage);
      }
      
      const data = await response.json();
      
      // Record actual token usage
      if (data.usage) {
        this.recordTokenUsage(data.usage.input_tokens, data.usage.output_tokens);
        console.log(`[ClaudeService] Actual tokens used - Input: ${data.usage.input_tokens}, Output: ${data.usage.output_tokens}`);
      }
      
      let responseText = data.content[0].text;
      responseText = responseText.replace(/```json\n?/gi, '').replace(/```\n?/gi, '').trim();
      
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in Claude response');
      }
      
      const rubric = JSON.parse(jsonMatch[0]) as Rubric;
      
      // Validate and normalize weights to exactly 100%
      const categories = Object.keys(rubric);
      let totalWeight = 0;
      categories.forEach(cat => {
        totalWeight += rubric[cat].weight;
      });
      
      // Normalize to exactly 100%
      if (Math.abs(totalWeight - 100) > 0.1) {
        categories.forEach(cat => {
          rubric[cat].weight = (rubric[cat].weight / totalWeight) * 100;
        });
      }
      
      // Ensure attribute weights are correct
      categories.forEach(cat => {
        const catWeight = rubric[cat].weight;
        const attributes = rubric[cat].attributes;
        
        if (!attributes || attributes.length !== 5) {
          throw new Error(`Category "${cat}" must have exactly 5 attributes`);
        }
        
        const totalAttrWeight = attributes.reduce((sum, attr) => sum + (attr.weight || 1), 0);
        
        attributes.forEach(attr => {
          attr.weight = (attr.weight || 1) * catWeight / totalAttrWeight;
          attr.weight_in_category = attr.weight / catWeight;
        });
      });
      
      console.log('[ClaudeService] Rubric generation complete with categories:', Object.keys(rubric));
      return rubric;
      
    } catch (error: any) {
      console.error('[ClaudeService] Error generating rubric:', error);
      throw error;
    }
  }
  
  async evaluateCandidateWithPDF(
    cvBase64: string, 
    coverBase64: string | null, 
    candidateName: string, 
    rubric: Rubric
  ): Promise<EvaluationResult> {
    console.log('[ClaudeService.evaluateCandidateWithPDF] Starting evaluation for:', candidateName);
    console.log('[ClaudeService] Rubric categories:', Object.keys(rubric));
    console.log('[ClaudeService] CV size:', cvBase64.length, 'bytes');
    
    if (!this.apiKey) {
      throw new Error('API key not configured');
    }
    
    // For very large PDFs, we need to be more conservative
    const cvSize = cvBase64.length;
    const isLargePDF = cvSize > 500000; // >500KB base64
    const isVeryLargePDF = cvSize > 1000000; // >1MB base64
    
    if (isVeryLargePDF) {
      console.warn(`[ClaudeService] Very large PDF detected (${Math.round(cvSize/1024)}KB), using simplified evaluation`);
    }
    
    const estimatedTokens = this.estimateTokens(cvBase64) + (coverBase64 ? this.estimateTokens(coverBase64) : 0) + 5000;
    await this.waitForRateLimit(estimatedTokens);
    
    const messageContent: any[] = [];
    
    // Add CV as PDF document
    messageContent.push({
      type: "document",
      source: {
        type: "base64",
        media_type: "application/pdf",
        data: cvBase64
      }
    });
    
    // Add cover letter if available
    if (coverBase64) {
      messageContent.push({
        type: "document",
        source: {
          type: "base64",
          media_type: "application/pdf",
          data: coverBase64
        }
      });
    }
    
    // For large PDFs, use a simplified prompt
    if (isVeryLargePDF) {
      // Simplified evaluation for very large PDFs
      messageContent.push({
        type: "text",
        text: `CRITICAL: You MUST respond with ONLY valid JSON. No other text allowed.

Evaluate ${candidateName} on these 8 categories, scoring each attribute 0-5:

${Object.entries(rubric).map(([cat, data]) => 
  `${cat}: ${data.attributes.map(a => a.name).join(', ')}`
).join('\n')}

For each attribute: find evidence in CV and score 0-5. If no evidence, score 0.

Return this EXACT JSON structure with NO other text:
{
  "candidate_name": "${candidateName}",
  "categories": {
    ${Object.entries(rubric).map(([cat, catData]) => `"${cat}": {
      "category_score": 0,
      "weight": ${catData.weight},
      "attributes": [
        ${catData.attributes.map(attr => `{
          "name": "${attr.name}",
          "score": 0,
          "weight": ${attr.weight.toFixed(2)},
          "evidence": "Evidence or 'No evidence found'"
        }`).join(',\n        ')}
      ]
    }`).join(',\n    ')}
  },
  "total_score": 0,
  "recommendation": "Poor fit",
  "strengths": ["Strength 1", "Strength 2", "Strength 3"],
  "development_areas": ["Area 1", "Area 2", "Area 3"],
  "overall_assessment": "Brief assessment",
  "interview_questions": [
    "Question about their experience at [COMPANY]",
    "Question about [SKILL] they mentioned",
    "Question about career transition",
    "Question about leadership experience",
    "Question about technical skills",
    "Question about career goals"
  ],
  "metrics": {
    "current_company": "Unknown",
    "time_at_current_company_years": 0,
    "advancement_current_company": "Unknown",
    "total_career_experience_years": 0,
    "years_industry_experience": 0,
    "relevant_industry": "Unknown",
    "avg_time_per_role_years": 0,
    "years_managerial_experience": 0,
    "max_team_size_managed": 0,
    "budget_managed": "N/A",
    "short_stints_count": 0,
    "job_hopping_flag": false,
    "notable_companies": [],
    "industries_worked": [],
    "universities": [],
    "degrees": [],
    "certifications": [],
    "functional_expertise": [],
    "technical_skills": [],
    "x_factor": [],
    "language_quality": "Unknown"
  }
}`
      });
      
    } else {
      // Standard evaluation for normal-sized PDFs
      // Create detailed rubric text for ALL categories
      const rubricText = Object.entries(rubric).slice(0, 4).map(([categoryName, category]) => {
        const attributesText = category.attributes.map((attr, idx) => 
          `    ${idx + 1}. ${attr.name}: ${attr.description}`
        ).join('\n');
        
        return `${categoryName} (${category.weight.toFixed(1)}% weight):
${attributesText}`;
      }).join('\n\n');
      
      // Add comprehensive evaluation instructions
      messageContent.push({
        type: "text",
        text: `CRITICAL INSTRUCTION: You MUST respond with ONLY valid JSON. Do NOT include any explanatory text, error messages, or anything except the JSON structure below.

Evaluate candidate ${candidateName} using these categories:

${rubricText}

${coverBase64 ? 'Documents: CV and Cover Letter' : 'Document: CV only'}

Score each attribute 0-5 based on evidence in the CV. Include specific evidence for each score.

Generate 6 PERSONALIZED interview questions referencing specific details from their CV (companies, projects, skills mentioned).

Return EXACTLY this JSON structure with NO other text:
{
  "candidate_name": "${candidateName}",
  "categories": {
    ${Object.keys(rubric).map(cat => `"${cat}": {
      "category_score": <0-100>,
      "weight": ${rubric[cat].weight.toFixed(1)},
      "attributes": [
        ${rubric[cat].attributes.map(attr => `{
          "name": "${attr.name}",
          "score": <0-5>,
          "weight": ${attr.weight.toFixed(2)},
          "evidence": "<specific evidence from CV or 'No evidence found'>"
        }`).join(',\n        ')}
      ]
    }`).join(',\n    ')}
  },
  "total_score": <0-100>,
  "recommendation": "Strong Candidate|Good Candidate|Developing Candidate|Poor fit",
  "strengths": ["strength 1", "strength 2", "strength 3"],
  "development_areas": ["area 1", "area 2", "area 3"],
  "overall_assessment": "2-3 sentence summary",
  "interview_questions": [
    "Question referencing their experience at [COMPANY from CV]",
    "Question about [SPECIFIC PROJECT/SKILL from CV]",
    "Question about career transition or gap",
    "Question about leadership/teamwork example",
    "Question about technical claim or certification",
    "Question about career goals based on trajectory"
  ],
  "metrics": {
    "current_company": "Name or Unknown",
    "time_at_current_company_years": 0,
    "advancement_current_company": "Details or Unknown",
    "total_career_experience_years": 0,
    "years_industry_experience": 0,
    "relevant_industry": "Industry",
    "avg_time_per_role_years": 0,
    "years_managerial_experience": 0,
    "max_team_size_managed": 0,
    "budget_managed": "Amount or N/A",
    "short_stints_count": 0,
    "job_hopping_flag": false,
    "notable_companies": [],
    "industries_worked": [],
    "universities": [],
    "degrees": [],
    "certifications": [],
    "functional_expertise": [],
    "technical_skills": [],
    "x_factor": [],
    "language_quality": "Professional|Good|Average|Poor"
  }
}`
      });
    }
    
    try {
      console.log('[ClaudeService] Sending evaluation request to proxy...');
      
      // Adjust max_tokens based on PDF size
      const maxTokens = isVeryLargePDF ? 4000 : isLargePDF ? 6000 : 8000;
      
      const response = await fetch(this.apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': this.apiKey
        },
        body: JSON.stringify({
          model: Config.CLAUDE_MODEL,
          max_tokens: maxTokens,
          temperature: 0.1, // Lower temperature for more consistent JSON
          messages: [{
            role: 'user',
            content: messageContent
          }]
        })
      });
      
      if (!response.ok) {
        let errorMessage = `API error: ${response.status}`;
        try {
          const errorData = await response.json();
          errorMessage = errorData.error?.message || JSON.stringify(errorData);
          console.error('[ClaudeService] API Error Response:', errorData);
        } catch (e) {
          console.error('[ClaudeService] Could not parse error response');
        }
        
        if (response.status === 429) {
          throw new Error('Rate limit exceeded. Please wait before retry.');
        }
        throw new Error(errorMessage);
      }
      
      const data = await response.json();
      
      if (!data.content || !data.content[0] || !data.content[0].text) {
        console.error('[ClaudeService] Unexpected response structure:', data);
        throw new Error('Invalid response from Claude API');
      }
      
      // Record actual token usage
      let actualTokensUsed = {
        input: 0,
        output: 0,
        total: 0
      };
      
      if (data.usage) {
        actualTokensUsed = {
          input: data.usage.input_tokens,
          output: data.usage.output_tokens,
          total: data.usage.input_tokens + data.usage.output_tokens
        };
        this.recordTokenUsage(data.usage.input_tokens, data.usage.output_tokens);
        console.log(`[ClaudeService] Actual tokens for ${candidateName} - Input: ${data.usage.input_tokens}, Output: ${data.usage.output_tokens}`);
      }
      
      let responseText = data.content[0].text;
      
      // Check for conversational error messages
      if (responseText.includes('Content length exceeds') || 
          responseText.includes('Content too long') ||
          responseText.includes('Would you like me to') ||
          responseText.includes('error') && !responseText.includes('{')) {
        console.error('[ClaudeService] Claude returned conversational error:', responseText);
        throw new Error('PDF too large for evaluation - Claude unable to process');
      }
      
      // Check if response is too short (indicates an error or cutoff)
      if (responseText.length < 500) {
        console.error('[ClaudeService] Response too short, likely an error:', responseText);
        throw new Error('Claude response was incomplete or errored');
      }
      
      // Clean up the response
      responseText = responseText.replace(/```json\n?/gi, '').replace(/```\n?/gi, '').trim();
      
      // Remove any text before the JSON
      const jsonStartIndex = responseText.indexOf('{');
      if (jsonStartIndex > 0) {
        responseText = responseText.substring(jsonStartIndex);
      }
      
      // Remove any text after the JSON
      let braceCount = 0;
      let jsonEndIndex = -1;
      for (let i = 0; i < responseText.length; i++) {
        if (responseText[i] === '{') braceCount++;
        if (responseText[i] === '}') braceCount--;
        if (braceCount === 0) {
          jsonEndIndex = i;
          break;
        }
      }
      
      if (jsonEndIndex > 0 && jsonEndIndex < responseText.length - 1) {
        responseText = responseText.substring(0, jsonEndIndex + 1);
      }
      
      let evaluation: EvaluationResult;
      try {
        evaluation = JSON.parse(responseText) as EvaluationResult;
      } catch (parseError) {
        console.error('[ClaudeService] JSON parse error:', parseError);
        console.error('[ClaudeService] Response text:', responseText.substring(0, 500));
        throw new Error('Failed to parse evaluation JSON - response may be incomplete');
      }
      
      // Validate the evaluation structure
      if (!evaluation.categories || typeof evaluation.categories !== 'object') {
        throw new Error('Invalid evaluation structure - missing categories');
      }
      
      // Add actual token usage to evaluation
      evaluation.actualTokensUsed = actualTokensUsed;
      
      // Calculate total score with proper math
      let calculatedTotal = 0;
      Object.entries(evaluation.categories).forEach(([catName, catData]) => {
        // Calculate category score from attributes
        let categoryScore = 0;
        if (catData.attributes && catData.attributes.length > 0) {
          const totalAttrScore = catData.attributes.reduce((sum, attr) => sum + (attr.score || 0), 0);
          categoryScore = (totalAttrScore / 25) * 100; // 5 attributes * max score 5 = 25
        }
        catData.category_score = categoryScore;
        
        // Calculate weighted contribution to total
        const weightedContribution = categoryScore * (catData.weight / 100);
        calculatedTotal += weightedContribution;
      });
      
      evaluation.total_score = Math.round(calculatedTotal);
      
      // Ensure recommendation based on corrected thresholds
      evaluation.recommendation = this.getRecommendation(evaluation.total_score);
      
      // Validate interview questions are personalized
      if (!evaluation.interview_questions || evaluation.interview_questions.length === 0) {
        console.warn('[ClaudeService] No interview questions generated');
        evaluation.interview_questions = this.generateDefaultQuestions(candidateName);
      }
      
      // Ensure metrics exist
      if (!evaluation.metrics) {
        evaluation.metrics = this.getDefaultMetrics();
      }
      
      console.log('[ClaudeService] Evaluation complete. Categories evaluated:', Object.keys(evaluation.categories).length);
      console.log('[ClaudeService] Total score:', evaluation.total_score);
      console.log('[ClaudeService] Actual tokens used:', actualTokensUsed);
      
      return evaluation;
      
    } catch (error: any) {
      console.error('[ClaudeService] Error evaluating candidate:', error);
      console.error('[ClaudeService] Error stack:', error.stack);
      
      // Don't retry on rate limit - throw to outer handler
      if (error.message && error.message.includes('Rate limit')) {
        throw error;
      }
      
      // Return a proper default evaluation with all categories
      const defaultEval: EvaluationResult = {
        candidate_name: candidateName,
        total_score: 0,
        recommendation: 'Unable to evaluate',
        categories: {},
        strengths: ['Evaluation could not be completed'],
        development_areas: ['Evaluation could not be completed'],
        overall_assessment: `Evaluation failed: ${error.message}`,
        metrics: this.getDefaultMetrics(),
        interview_questions: this.generateDefaultQuestions(candidateName),
        actualTokensUsed: { input: 0, output: 0, total: 0 }
      };
      
      // Initialize all categories from rubric with zero scores
      Object.entries(rubric).forEach(([catName, catData]) => {
        defaultEval.categories[catName] = {
          category_score: 0,
          weight: catData.weight,
          attributes: catData.attributes.map(attr => ({
            name: attr.name,
            score: 0,
            weight: attr.weight,
            evidence: 'Evaluation failed - unable to process CV'
          }))
        };
      });
      
      return defaultEval;
    }
  }
  
  async extractMetrics(text: string, candidateName: string): Promise<any> {
    console.log('[ClaudeService.extractMetrics] Starting for:', candidateName);
    return this.getDefaultMetrics();
  }
  
  private getRecommendation(score: number): string {
    if (score >= 80) return 'Strong Candidate';
    if (score >= 70) return 'Good Candidate';
    if (score >= 60) return 'Developing Candidate';
    return 'Poor fit';
  }
  
  private generateDefaultQuestions(candidateName: string): string[] {
    return [
      `Can you walk me through your most significant achievement in your current role?`,
      `What specific experience do you have that directly relates to this position?`,
      `Describe a time when you had to overcome a significant challenge at work.`,
      `How do you stay current with industry trends and developments?`,
      `What are your career goals for the next 3-5 years?`,
      `Why are you interested in leaving your current position?`
    ];
  }
  
  private getDefaultMetrics(): any {
    return {
      current_company: "Unknown",
      time_at_current_company_years: 0,
      advancement_current_company: "Unknown",
      total_career_experience_years: 0,
      years_industry_experience: 0,
      relevant_industry: "Unknown",
      avg_time_per_role_years: 0,
      years_managerial_experience: 0,
      max_team_size_managed: 0,
      budget_managed: "N/A",
      short_stints_count: 0,
      job_hopping_flag: false,
      notable_companies: [],
      industries_worked: [],
      universities: [],
      degrees: [],
      certifications: [],
      functional_expertise: [],
      technical_skills: [],
      x_factor: [],
      language_quality: "Unknown"
    };
  }
}

export default new ClaudeService();