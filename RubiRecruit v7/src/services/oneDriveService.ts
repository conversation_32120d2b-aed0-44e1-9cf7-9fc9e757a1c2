import { Client } from '@microsoft/microsoft-graph-client';
import authService from './authService';
import { Config } from '../config/config';

interface DriveItem {
  id: string;
  name: string;
  size?: number;
  lastModifiedDateTime?: string;
  webUrl?: string;
  '@microsoft.graph.downloadUrl'?: string;
  file?: any;
  folder?: any;
}

class OneDriveService {
  private graphClient: Client | null = null;
  private driveId: string | null = null;
  
  async initialize(): Promise<void> {
    try {
      console.log('Initializing OneDrive service...');
      const token = await authService.getToken();
      
      if (!token) {
        throw new Error('No access token available');
      }
      
      this.graphClient = Client.init({
        authProvider: async (done) => {
          done(null, token);
        },
        defaultVersion: Config.GRAPH_API_VERSION
      });
      
      // Get drive ID for the user
      const drive = await this.graphClient.api('/me/drive').get();
      this.driveId = drive.id;
      
      console.log('OneDrive service initialized successfully');
      console.log('Drive ID:', this.driveId);
    } catch (error) {
      console.error('Failed to initialize OneDrive service:', error);
      throw error;
    }
  }
  
  async ensureInitialized(): Promise<void> {
    if (!this.graphClient) {
      await this.initialize();
    }
  }
  
  async listFiles(folderPath: string): Promise<DriveItem[]> {
    await this.ensureInitialized();
    
    try {
      console.log(`Listing files in folder: ${folderPath}`);
      
      // Encode the folder path properly
      const encodedPath = encodeURIComponent(folderPath);
      const apiPath = `/me/drive/root:/${encodedPath}:/children`;
      
      console.log(`API path: ${apiPath}`);
      
      const response = await this.graphClient!
        .api(apiPath)
        .select('id,name,size,lastModifiedDateTime,webUrl,file,folder,@microsoft.graph.downloadUrl')
        .top(999)
        .get();
      
      // Filter to only files (exclude folders)
      const files = response.value.filter((item: DriveItem) => item.file);
      
      console.log(`Found ${files.length} files in ${folderPath}`);
      return files;
    } catch (error: any) {
      console.error(`Error listing files in ${folderPath}:`, error);
      
      if (error.statusCode === 404) {
        throw new Error(`Folder not found: ${folderPath}. Please ensure the folder exists in your OneDrive.`);
      } else if (error.statusCode === 401) {
        throw new Error('Authentication failed. Please login again.');
      } else if (error.statusCode === 403) {
        throw new Error('Permission denied. Please check your app permissions.');
      }
      
      throw error;
    }
  }
  
  async getFile(fileId: string): Promise<DriveItem> {
    await this.ensureInitialized();
    
    try {
      console.log(`Getting file metadata for ID: ${fileId}`);
      
      const file = await this.graphClient!
        .api(`/me/drive/items/${fileId}`)
        .select('id,name,size,lastModifiedDateTime,@microsoft.graph.downloadUrl')
        .get();
      
      return file;
    } catch (error) {
      console.error(`Error getting file ${fileId}:`, error);
      throw error;
    }
  }
  
  async downloadFile(fileId: string): Promise<ArrayBuffer> {
    await this.ensureInitialized();
    
    try {
      console.log(`Downloading file with ID: ${fileId}`);
      
      // Get the download URL
      const file = await this.getFile(fileId);
      const downloadUrl = file['@microsoft.graph.downloadUrl'];
      
      if (!downloadUrl) {
        throw new Error('No download URL available for file');
      }
      
      console.log('Download URL obtained, fetching content...');
      
      // Download the file content
      const response = await fetch(downloadUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/octet-stream'
        }
      });
      
      if (!response.ok) {
        throw new Error(`Failed to download file: ${response.statusText}`);
      }
      
      const arrayBuffer = await response.arrayBuffer();
      console.log(`Downloaded ${arrayBuffer.byteLength} bytes`);
      
      return arrayBuffer;
    } catch (error) {
      console.error(`Error downloading file ${fileId}:`, error);
      throw error;
    }
  }
  
  async downloadFileAsText(fileId: string): Promise<string> {
    try {
      console.log(`Downloading file as text: ${fileId}`);
      const arrayBuffer = await this.downloadFile(fileId);
      const decoder = new TextDecoder('utf-8');
      return decoder.decode(arrayBuffer);
    } catch (error) {
      console.error(`Error downloading file as text:`, error);
      throw error;
    }
  }
  
  async downloadFileAsBase64(fileId: string): Promise<string> {
    try {
      console.log(`Downloading file as base64: ${fileId}`);
      const arrayBuffer = await this.downloadFile(fileId);
      const uint8Array = new Uint8Array(arrayBuffer);
      let binary = '';
      uint8Array.forEach(byte => {
        binary += String.fromCharCode(byte);
      });
      return btoa(binary);
    } catch (error) {
      console.error(`Error downloading file as base64:`, error);
      throw error;
    }
  }
  
  async uploadFile(
    folderPath: string,
    fileName: string,
    content: ArrayBuffer | Blob | string
  ): Promise<DriveItem> {
    await this.ensureInitialized();
    
    try {
      console.log(`Uploading file: ${fileName} to ${folderPath}`);
      
      let uploadContent: ArrayBuffer;
      
      if (typeof content === 'string') {
        const encoder = new TextEncoder();
        uploadContent = encoder.encode(content).buffer;
      } else if (content instanceof Blob) {
        uploadContent = await content.arrayBuffer();
      } else {
        uploadContent = content;
      }
      
      const encodedPath = encodeURIComponent(folderPath);
      const encodedFileName = encodeURIComponent(fileName);
      const apiPath = `/me/drive/root:/${encodedPath}/${encodedFileName}:/content`;
      
      console.log(`Upload API path: ${apiPath}`);
      console.log(`Content size: ${uploadContent.byteLength} bytes`);
      
      // For small files (< 4MB), use simple upload
      if (uploadContent.byteLength < 4 * 1024 * 1024) {
        const uploadedFile = await this.graphClient!
          .api(apiPath)
          .put(uploadContent);
        
        console.log(`Uploaded ${fileName} successfully`);
        return uploadedFile;
      } else {
        // For larger files, use upload session
        return await this.uploadLargeFile(folderPath, fileName, uploadContent);
      }
    } catch (error) {
      console.error(`Error uploading file ${fileName}:`, error);
      throw error;
    }
  }
  
  private async uploadLargeFile(
    folderPath: string,
    fileName: string,
    content: ArrayBuffer
  ): Promise<DriveItem> {
    try {
      console.log(`Starting large file upload for ${fileName}`);
      
      const encodedPath = encodeURIComponent(folderPath);
      const encodedFileName = encodeURIComponent(fileName);
      const apiPath = `/me/drive/root:/${encodedPath}/${encodedFileName}:/createUploadSession`;
      
      // Create upload session
      const uploadSession = await this.graphClient!
        .api(apiPath)
        .post({
          item: {
            "@microsoft.graph.conflictBehavior": "rename"
          }
        });
      
      const uploadUrl = uploadSession.uploadUrl;
      const fileSize = content.byteLength;
      const chunkSize = 5 * 1024 * 1024; // 5MB chunks
      
      let start = 0;
      let end = Math.min(chunkSize, fileSize);
      let uploadedFile: any = null;
      
      while (start < fileSize) {
        const chunk = content.slice(start, end);
        
        console.log(`Uploading chunk: ${start}-${end-1}/${fileSize}`);
        
        const response = await fetch(uploadUrl, {
          method: 'PUT',
          headers: {
            'Content-Length': chunk.byteLength.toString(),
            'Content-Range': `bytes ${start}-${end - 1}/${fileSize}`
          },
          body: chunk
        });
        
        if (!response.ok && response.status !== 201 && response.status !== 202) {
          throw new Error(`Upload failed: ${response.statusText}`);
        }
        
        // If this is the last chunk, we get the file metadata back
        if (response.status === 201 || response.status === 200) {
          uploadedFile = await response.json();
        }
        
        start = end;
        end = Math.min(start + chunkSize, fileSize);
        
        console.log(`Uploaded ${Math.round((start / fileSize) * 100)}%`);
      }
      
      console.log(`Large file ${fileName} uploaded successfully`);
      return uploadedFile;
    } catch (error) {
      console.error('Large file upload error:', error);
      throw error;
    }
  }
  
  async createFolder(parentPath: string, folderName: string): Promise<DriveItem> {
    await this.ensureInitialized();
    
    try {
      console.log(`Creating folder: ${folderName} in ${parentPath}`);
      
      const encodedPath = encodeURIComponent(parentPath);
      const apiPath = `/me/drive/root:/${encodedPath}:/children`;
      
      const newFolder = await this.graphClient!
        .api(apiPath)
        .post({
          name: folderName,
          folder: {},
          '@microsoft.graph.conflictBehavior': 'rename'
        });
      
      console.log(`Created folder: ${folderName}`);
      return newFolder;
    } catch (error) {
      console.error(`Error creating folder ${folderName}:`, error);
      throw error;
    }
  }
  
  async deleteFile(fileId: string): Promise<void> {
    await this.ensureInitialized();
    
    try {
      console.log(`Deleting file with ID: ${fileId}`);
      
      await this.graphClient!
        .api(`/me/drive/items/${fileId}`)
        .delete();
      
      console.log(`Deleted file ${fileId}`);
    } catch (error) {
      console.error(`Error deleting file ${fileId}:`, error);
      throw error;
    }
  }
  
  async searchFiles(query: string, folderPath?: string): Promise<DriveItem[]> {
    await this.ensureInitialized();
    
    try {
      console.log(`Searching for: ${query}${folderPath ? ` in ${folderPath}` : ''}`);
      
      let searchApi = `/me/drive/root/search(q='${encodeURIComponent(query)}')`;
      
      if (folderPath) {
        const encodedPath = encodeURIComponent(folderPath);
        searchApi = `/me/drive/root:/${encodedPath}:/search(q='${encodeURIComponent(query)}')`;
      }
      
      const response = await this.graphClient!
        .api(searchApi)
        .select('id,name,size,lastModifiedDateTime,parentReference,file,folder')
        .top(50)
        .get();
      
      // Filter to only files
      const files = response.value.filter((item: DriveItem) => item.file);
      
      console.log(`Search found ${files.length} files matching "${query}"`);
      return files;
    } catch (error) {
      console.error(`Error searching for ${query}:`, error);
      throw error;
    }
  }
  
  async getFileMetadata(fileId: string): Promise<any> {
    await this.ensureInitialized();
    
    try {
      console.log(`Getting metadata for file: ${fileId}`);
      
      const metadata = await this.graphClient!
        .api(`/me/drive/items/${fileId}`)
        .expand('permissions')
        .get();
      
      return metadata;
    } catch (error) {
      console.error(`Error getting metadata for ${fileId}:`, error);
      throw error;
    }
  }
  
  async testConnection(): Promise<boolean> {
    try {
      console.log('Testing OneDrive connection...');
      await this.ensureInitialized();
      
      // Try to access root folder
      const root = await this.graphClient!
        .api('/me/drive/root')
        .get();
      
      console.log('OneDrive connection successful');
      console.log('Drive name:', root.name);
      console.log('Drive type:', root.driveType);
      console.log('Owner:', root.owner?.user?.displayName);
      
      return true;
    } catch (error) {
      console.error('OneDrive connection failed:', error);
      return false;
    }
  }
  
  async getFolderContents(folderPath: string): Promise<{files: DriveItem[], folders: DriveItem[]}> {
    await this.ensureInitialized();
    
    try {
      console.log(`Getting contents of folder: ${folderPath}`);
      
      const encodedPath = encodeURIComponent(folderPath);
      const apiPath = `/me/drive/root:/${encodedPath}:/children`;
      
      const response = await this.graphClient!
        .api(apiPath)
        .select('id,name,size,lastModifiedDateTime,webUrl,file,folder')
        .top(999)
        .get();
      
      const files = response.value.filter((item: DriveItem) => item.file);
      const folders = response.value.filter((item: DriveItem) => item.folder);
      
      console.log(`Found ${files.length} files and ${folders.length} folders`);
      
      return { files, folders };
    } catch (error) {
      console.error(`Error getting folder contents:`, error);
      throw error;
    }
  }
}

export default new OneDriveService();