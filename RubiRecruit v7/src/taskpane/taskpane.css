* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --ruby-primary: #8B1538;
    --ruby-secondary: #A91B60;
    --ruby-light: #E91E63;
    --success-green: #34A853;
    --warning-yellow: #FBBC04;
    --error-red: #EA4335;
    --info-blue: #4285F4;
    --bg-primary: #FFFFFF;
    --bg-secondary: #F8F9FA;
    --bg-tertiary: #E8F0FE;
    --text-primary: #202124;
    --text-secondary: #5F6368;
    --text-tertiary: #80868B;
    --border-color: #DADCE0;
    --shadow-sm: 0 1px 2px 0 rgba(60,64,67,0.3), 0 1px 3px 1px rgba(60,64,67,0.15);
    --shadow-md: 0 1px 3px 0 rgba(60,64,67,0.3), 0 4px 8px 3px rgba(60,64,67,0.15);
    --shadow-lg: 0 2px 6px 2px rgba(60,64,67,0.15);
}

body {
    font-family: 'Google Sans', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background: var(--bg-secondary);
    color: var(--text-primary);
    line-height: 1.5;
}

#container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Header Styles */
header {
    background: linear-gradient(135deg, var(--ruby-primary) 0%, var(--ruby-secondary) 100%);
    color: white;
    padding: 16px 20px;
    box-shadow: var(--shadow-sm);
}

.header-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    position: relative;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
}

.diamond {
    font-size: 24px;
    animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.8; transform: scale(1.1); }
}

header h1 {
    font-size: 22px;
    font-weight: 500;
    letter-spacing: -0.5px;
}

.tagline {
    font-size: 12px;
    opacity: 0.95;
    font-weight: 300;
}

.version-badge {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.2);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 500;
}

/* Main Content */
main {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
    overflow-x: hidden;
}

/* Card Styles */
.checklist-card,
.next-step-card,
.journey-card,
.stats-card,
.config-panel,
.action-panel {
    background: var(--bg-primary);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    box-shadow: var(--shadow-sm);
    transition: box-shadow 0.3s ease;
}

.checklist-card:hover,
.next-step-card:hover,
.journey-card:hover,
.stats-card:hover {
    box-shadow: var(--shadow-md);
}

/* Checklist Styles */
.checklist-card h3 {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 12px;
    color: var(--text-primary);
}

.checklist-items {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.checklist-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 12px;
    background: var(--bg-secondary);
    border-radius: 6px;
    transition: all 0.2s ease;
}

.checklist-item:hover {
    background: var(--bg-tertiary);
}

.checklist-item.completed {
    background: #E8F5E9;
}

.check-icon {
    font-size: 18px;
    transition: transform 0.3s ease;
}

.checklist-item.completed .check-icon {
    color: var(--success-green);
    transform: scale(1.2);
}

.check-content {
    flex: 1;
}

.check-title {
    font-size: 13px;
    font-weight: 500;
    color: var(--text-primary);
}

.check-subtitle {
    font-size: 11px;
    color: var(--text-secondary);
}

/* Next Step Card */
.next-step-card {
    background: linear-gradient(135deg, #FFF9E6 0%, #FFF3CD 100%);
    border: 1px solid #FFE082;
    text-align: center;
}

.step-icon {
    font-size: 32px;
    margin-bottom: 8px;
}

.next-step-card h3 {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 4px;
    color: var(--text-primary);
}

.next-step-card p {
    font-size: 13px;
    color: var(--text-secondary);
    margin-bottom: 12px;
}

/* Journey Progress */
.journey-steps {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.journey-step {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 12px;
    background: var(--bg-secondary);
    border-radius: 6px;
    position: relative;
    transition: all 0.2s ease;
}

.journey-step:hover {
    background: var(--bg-tertiary);
}

.journey-step.completed {
    background: #E8F5E9;
}

.journey-step.active {
    background: #E3F2FD;
    border: 1px solid var(--info-blue);
}

.step-number {
    width: 24px;
    height: 24px;
    background: var(--text-tertiary);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    font-weight: 500;
}

.journey-step.completed .step-number {
    background: var(--success-green);
}

.journey-step.active .step-number {
    background: var(--info-blue);
}

.step-info {
    flex: 1;
}

.step-title {
    font-size: 13px;
    font-weight: 500;
    color: var(--text-primary);
}

.step-desc {
    font-size: 11px;
    color: var(--text-secondary);
}

.step-status {
    font-size: 16px;
}

/* Statistics Card */
.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-bottom: 16px;
}

.stat-item {
    padding: 8px;
    background: var(--bg-secondary);
    border-radius: 6px;
}

.stat-label {
    font-size: 11px;
    color: var(--text-secondary);
    margin-bottom: 2px;
}

.stat-value {
    font-size: 18px;
    font-weight: 500;
    color: var(--text-primary);
}

.stat-value.success {
    color: var(--success-green);
}

.stat-value.warning {
    color: var(--warning-yellow);
}

/* Progress Bar */
.progress-container {
    position: relative;
    margin-top: 12px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--border-color);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--ruby-primary), var(--ruby-light));
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 4px;
}

.progress-text {
    position: absolute;
    top: -20px;
    right: 0;
    font-size: 11px;
    color: var(--text-secondary);
    font-weight: 500;
}

/* Buttons */
.action-btn {
    width: 100%;
    padding: 10px 16px;
    margin: 4px 0;
    border: none;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.action-btn.primary {
    background: var(--ruby-primary);
    color: white;
}

.action-btn.primary:hover:not(:disabled) {
    background: var(--ruby-secondary);
    box-shadow: var(--shadow-sm);
}

.action-btn.secondary {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.action-btn.secondary:hover:not(:disabled) {
    background: var(--bg-tertiary);
    border-color: var(--ruby-primary);
    color: var(--ruby-primary);
}

.action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Configuration Panel */
.config-panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 400px;
    z-index: 1000;
    box-shadow: var(--shadow-lg);
}

.config-section {
    margin-bottom: 16px;
}

.config-section label {
    display: block;
    font-size: 12px;
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: 4px;
}

.config-section input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 13px;
    transition: border-color 0.2s ease;
}

.config-section input:focus {
    outline: none;
    border-color: var(--ruby-primary);
}

/* Status Message */
.status-message {
    padding: 12px;
    border-radius: 6px;
    margin-top: 16px;
    font-size: 13px;
    text-align: center;
    display: none;
}

.status-message.show {
    display: block;
}

.status-message.info {
    background: var(--bg-tertiary);
    color: var(--info-blue);
    border: 1px solid var(--info-blue);
}

.status-message.success {
    background: #E8F5E9;
    color: var(--success-green);
    border: 1px solid var(--success-green);
}

.status-message.warning {
    background: #FFF9E6;
    color: var(--warning-yellow);
    border: 1px solid var(--warning-yellow);
}

.status-message.error {
    background: #FFEBEE;
    color: var(--error-red);
    border: 1px solid var(--error-red);
}

/* Footer */
footer {
    background: var(--bg-primary);
    border-top: 1px solid var(--border-color);
    padding: 8px 16px;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

footer span {
    font-size: 11px;
    color: var(--text-tertiary);
}

.refresh-btn {
    padding: 4px 8px;
    background: transparent;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.refresh-btn:hover {
    background: var(--bg-secondary);
    border-color: var(--ruby-primary);
}

/* Debug Panel */
.debug-panel {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 200px;
    background: var(--bg-primary);
    border-top: 2px solid var(--ruby-primary);
    box-shadow: var(--shadow-lg);
    z-index: 2000;
}

.debug-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
}

.debug-header h4 {
    font-size: 12px;
    font-weight: 500;
}

.close-btn {
    background: transparent;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: var(--text-secondary);
}

.debug-log {
    height: calc(100% - 40px);
    overflow-y: auto;
    padding: 8px;
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 11px;
}

.debug-entry {
    margin-bottom: 4px;
    padding: 2px 4px;
}

.debug-entry.info {
    color: var(--text-primary);
}

.debug-entry.error {
    color: var(--error-red);
    background: #FFEBEE;
}

.debug-entry.warning {
    color: var(--warning-yellow);
    background: #FFF9E6;
}

.timestamp {
    color: var(--text-tertiary);
    margin-right: 8px;
}

/* Animations */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.checklist-card,
.next-step-card,
.journey-card,
.stats-card {
    animation: slideIn 0.3s ease-out;
}

/* Responsive adjustments */
@media (max-width: 320px) {
    header h1 {
        font-size: 18px;
    }
    
    .tagline {
        font-size: 11px;
    }
    
    .action-btn {
        font-size: 12px;
        padding: 8px 12px;
    }
}