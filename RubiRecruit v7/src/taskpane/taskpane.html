<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=Edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>RubiRecruit for Excel</title>
    <script src="https://appsforoffice.microsoft.com/lib/1.1/hosted/office.js"></script>
    <link rel="stylesheet" href="taskpane.css">
</head>
<body>
    <div id="container">
        <header>
            <div class="header-content">
                <div class="logo-section">
                    <span class="diamond">💎</span>
                    <h1>RubiRecruit™</h1>
                </div>
                <p class="tagline">AI-Powered Recruitment Excellence</p>
                <div class="version-badge">v7.0.0</div>
            </div>
        </header>
        
        <main>
            <!-- Prerequisites Checklist -->
            <div class="checklist-card">
                <h3>📋 Prerequisites Checklist</h3>
                <div class="checklist-items">
                    <div class="checklist-item" id="check-api">
                        <span class="check-icon">⏺️</span>
                        <div class="check-content">
                            <div class="check-title">Claude API Key</div>
                            <div class="check-subtitle">Get from console.anthropic.com</div>
                        </div>
                    </div>
                    <div class="checklist-item" id="check-position">
                        <span class="check-icon">⏺️</span>
                        <div class="check-content">
                            <div class="check-title">Position Description PDF</div>
                            <div class="check-subtitle">Local PDF file ready to select</div>
                        </div>
                    </div>
                    <div class="checklist-item" id="check-cvs">
                        <span class="check-icon">⏺️</span>
                        <div class="check-content">
                            <div class="check-title">CVs in PDF Format</div>
                            <div class="check-subtitle">Local PDF files ready to select</div>
                        </div>
                    </div>
                    <div class="checklist-item" id="check-covers">
                        <span class="check-icon">⏺️</span>
                        <div class="check-content">
                            <div class="check-title">Cover Letters (Optional)</div>
                            <div class="check-subtitle">Include with CV selections</div>
                        </div>
                    </div>
                    <div class="checklist-item" id="check-proxy">
                        <span class="check-icon">⏺️</span>
                        <div class="check-content">
                            <div class="check-title">Proxy Server Running</div>
                            <div class="check-subtitle">Run "npm run proxy" in terminal</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Next Step Card -->
            <div class="next-step-card" id="next-step-card">
                <div class="step-icon">💡</div>
                <h3>Next Step</h3>
                <p id="next-step-text">Configure API key to begin</p>
                <button id="next-step-btn" class="action-btn primary">
                    Get Started
                </button>
            </div>

            <!-- Journey Progress -->
            <div class="journey-card">
                <h3>📊 Your Journey Progress</h3>
                <div class="journey-steps">
                    <div class="journey-step" id="step-1">
                        <div class="step-number">1</div>
                        <div class="step-info">
                            <div class="step-title">Initial Setup</div>
                            <div class="step-desc">Configure API key and proxy server</div>
                        </div>
                        <span class="step-status">⏳</span>
                    </div>
                    <div class="journey-step" id="step-2">
                        <div class="step-number">2</div>
                        <div class="step-info">
                            <div class="step-title">Create all Sheets</div>
                            <div class="step-desc">Setup evaluation worksheets</div>
                        </div>
                        <span class="step-status">⏳</span>
                    </div>
                    <div class="journey-step" id="step-3">
                        <div class="step-number">3</div>
                        <div class="step-info">
                            <div class="step-title">Load Position</div>
                            <div class="step-desc">Select position description PDF</div>
                        </div>
                        <span class="step-status">⏳</span>
                    </div>
                    <div class="journey-step" id="step-4">
                        <div class="step-number">4</div>
                        <div class="step-info">
                            <div class="step-title">Document Mapping</div>
                            <div class="step-desc">Map CVs and cover letters</div>
                        </div>
                        <span class="step-status">⏳</span>
                    </div>
                    <div class="journey-step" id="step-5">
                        <div class="step-number">5</div>
                        <div class="step-info">
                            <div class="step-title">Generate Rubric</div>
                            <div class="step-desc">AI creates evaluation criteria</div>
                        </div>
                        <span class="step-status">⏳</span>
                    </div>
                    <div class="journey-step" id="step-6">
                        <div class="step-number">6</div>
                        <div class="step-info">
                            <div class="step-title">Process Candidates</div>
                            <div class="step-desc">Evaluate applications</div>
                        </div>
                        <span class="step-status">⏳</span>
                    </div>
                    <div class="journey-step" id="step-7">
                        <div class="step-number">7</div>
                        <div class="step-info">
                            <div class="step-title">Review Results</div>
                            <div class="step-desc">Analyze evaluations</div>
                        </div>
                        <span class="step-status">⏳</span>
                    </div>
                </div>
            </div>

            <!-- Current Statistics -->
            <div class="stats-card" id="stats-card" style="display: none;">
                <h3>📈 Current Statistics</h3>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-label">Position:</div>
                        <div class="stat-value" id="stat-position">-</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">Total Candidates:</div>
                        <div class="stat-value" id="stat-total">0</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">Processed:</div>
                        <div class="stat-value success" id="stat-processed">0</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">Remaining:</div>
                        <div class="stat-value warning" id="stat-remaining">0</div>
                    </div>
                </div>
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill" id="main-progress"></div>
                    </div>
                    <div class="progress-text" id="progress-text">0%</div>
                </div>
            </div>

            <!-- Configuration Panel (Hidden by default) -->
            <div id="config-panel" class="config-panel" style="display: none;">
                <h3>⚙️ Configuration</h3>
                
                <div class="config-section">
                    <label>Claude API Key</label>
                    <input type="password" id="api-key-input" placeholder="sk-ant-api..." />
                    <button id="save-api-btn" class="action-btn secondary">Save API Key</button>
                </div>
                
                <div class="config-section">
                    <label>File Count</label>
                    <div id="file-count" class="file-count-display">Files loaded: 0 position(s), 0 CV/cover letter(s)</div>
                </div>
                
                <div class="config-section">
                    <button id="save-config-btn" class="action-btn primary">Save Configuration</button>
                    <button id="close-config-btn" class="action-btn secondary">Close</button>
                </div>
            </div>

            <!-- Action Buttons Panel (Hidden by default) -->
            <div id="action-panel" class="action-panel" style="display: none;">
                <button id="setup-sheets-btn" class="action-btn primary">
                    📊 Create All Sheets
                </button>
                <button id="load-position-btn" class="action-btn secondary">
                    📄 Load Position PDF
                </button>
                <button id="map-documents-btn" class="action-btn secondary">
                    📁 Map Documents
                </button>
                <button id="generate-rubric-btn" class="action-btn secondary" disabled>
                    🤖 Generate AI Rubric
                </button>
                <button id="process-batch-btn" class="action-btn primary" disabled>
                    ⚡ Process Candidates
                </button>
                <button id="refresh-status-btn" class="action-btn secondary">
                    🔄 Refresh Status
                </button>
            </div>

            <!-- Status Message -->
            <div id="status-message" class="status-message"></div>
        </main>
        
        <footer>
            <div class="footer-content">
                <span>RubiRecruit v7.0.0</span>
                <button id="refresh-btn" class="refresh-btn">🔄 Refresh Status</button>
            </div>
        </footer>
    </div>
    
    <!-- Hidden file inputs for local file selection -->
    <input type="file" id="positionFilePicker" accept=".pdf" style="display:none">
    <input type="file" id="cvFilesPicker" multiple accept=".pdf" style="display:none">
    
    <!-- Debug Panel (Hidden) -->
    <div id="debug-panel" class="debug-panel" style="display: none;">
        <div class="debug-header">
            <h4>Debug Console</h4>
            <button onclick="closeDebugPanel()" class="close-btn">×</button>
        </div>
        <div id="debug-log" class="debug-log"></div>
    </div>
    
    <script>
        // Debug functions
        window.addDebugLog = function(message, level = 'info') {
            const debugLog = document.getElementById('debug-log');
            if (!debugLog) return;
            
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = `debug-entry ${level}`;
            entry.innerHTML = `<span class="timestamp">[${timestamp}]</span> ${escapeHtml(message)}`;
            debugLog.appendChild(entry);
            
            // Keep only last 100 entries
            while (debugLog.children.length > 100) {
                debugLog.removeChild(debugLog.firstChild);
            }
            
            // Auto-scroll to bottom
            debugLog.scrollTop = debugLog.scrollHeight;
        }
        
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        function closeDebugPanel() {
            document.getElementById('debug-panel').style.display = 'none';
        }
        
        // Log initial load
        console.log('RubiRecruit taskpane.html loaded');
        console.log('User Agent:', navigator.userAgent);
        console.log('Office.js loading...');
    </script>
</body>
</html>