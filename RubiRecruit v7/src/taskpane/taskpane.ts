/* global document, Office, Excel */

import authService from '../services/authService';
import localFileService from '../services/localFileService';
import excelService from '../services/excelService';
import claudeService from '../services/claudeService';
import pdfService from '../services/pdfService';
import { Config } from '../config/config';

// Global state
let claudeApiKey: string = '';
let currentRubric: any = null;
let processingQueue: any[] = [];
let isProcessing: boolean = false;

// Debug logging wrapper
function debugLog(message: string, data?: any) {
  console.log(`[RubiRecruit] ${message}`, data || '');
  if (typeof (window as any).addDebugLog === 'function') {
    (window as any).addDebugLog(`${message} ${data ? JSON.stringify(data) : ''}`, 'info');
  }
}

function debugError(message: string, error: any) {
  console.error(`[RubiRecruit ERROR] ${message}`, error);
  if (typeof (window as any).addDebugLog === 'function') {
    (window as any).addDebugLog(`ERROR: ${message} - ${error?.message || error}`, 'error');
  }
}

// Wait for Office to be ready
Office.onReady((info) => {
  debugLog('Office.onReady called', info);
  
  if (info.host === Office.HostType.Excel) {
    debugLog('Host is Excel, initializing...');
    if (document.readyState !== 'loading') {
      initializeTaskPane();
    } else {
      document.addEventListener('DOMContentLoaded', initializeTaskPane);
    }
  } else {
    debugError('Unexpected host type', info.host);
  }
});

async function initializeTaskPane() {
  debugLog('Initializing RubiRecruit task pane (LOCAL FILE MODE)');
  
  // Add diagnostics button
  const setupSection = document.getElementById('setup-section');
  if (setupSection) {
    const diagBtn = document.createElement('button');
    diagBtn.textContent = '🔧 Run Diagnostics';
    diagBtn.onclick = runDiagnostics;
    diagBtn.className = 'secondary-btn';
    diagBtn.style.marginTop = '10px';
    setupSection.appendChild(diagBtn);
    
    const debugToggle = document.createElement('button');
    debugToggle.textContent = '🛠️ Toggle Debug Panel';
    debugToggle.onclick = () => {
      const debugSection = document.getElementById('debug-section');
      if (debugSection) {
        debugSection.style.display = debugSection.style.display === 'none' ? 'block' : 'none';
      }
    };
    debugToggle.className = 'secondary-btn';
    debugToggle.style.marginTop = '5px';
    setupSection.appendChild(debugToggle);
  }
  
  // Load stored API key
  await loadApiKey();
  
  // Update status
  updateStatus('Running in LOCAL FILE MODE - Ready to begin', "success");
  updateFileCount();

  // Set up button click handlers
  const setupBtn = document.getElementById("setup-sheets-btn") as HTMLButtonElement;
  if (setupBtn) {
    setupBtn.onclick = () => {
      debugLog('Setup sheets button clicked');
      setupSheets();
    };
  }
  
  // API key handling
  const saveApiKeyBtn = document.getElementById("save-api-btn") as HTMLButtonElement;
  if (saveApiKeyBtn) {
    saveApiKeyBtn.onclick = () => {
      debugLog('Save API key button clicked');
      saveApiKey();
    };
  }
  
  const apiKeyInput = document.getElementById("api-key-input") as HTMLInputElement;
  if (apiKeyInput) {
    apiKeyInput.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        debugLog('API key entered via Enter key');
        saveApiKey();
      }
    });
  }
  
  const loadPosBtn = document.getElementById("load-position-btn") as HTMLButtonElement;
  if (loadPosBtn) {
    loadPosBtn.onclick = () => {
      debugLog('Load position button clicked');
      loadPosition();
    };
  }
  
  const genRubricBtn = document.getElementById("generate-rubric-btn") as HTMLButtonElement;
  if (genRubricBtn) {
    genRubricBtn.onclick = () => {
      debugLog('Generate rubric button clicked');
      generateRubric();
    };
  }
  
  const mapDocsBtn = document.getElementById("map-documents-btn") as HTMLButtonElement;
  if (mapDocsBtn) {
    mapDocsBtn.onclick = () => {
      debugLog('Map documents button clicked');
      mapDocuments();
    };
  }
  
  const processBatchBtn = document.getElementById("process-batch-btn") as HTMLButtonElement;
  if (processBatchBtn) {
    processBatchBtn.onclick = () => {
      debugLog('Process batch button clicked');
      processCandidates();
    };
  }
  
  // Main UI button handlers
  const getStartedBtn = document.getElementById("next-step-btn") as HTMLButtonElement;
  if (getStartedBtn) {
    getStartedBtn.onclick = () => {
      debugLog('Get Started button clicked');
      showConfigPanel();
    };
  }
  
  const saveConfigBtn = document.getElementById("save-config-btn") as HTMLButtonElement;
  if (saveConfigBtn) {
    saveConfigBtn.onclick = () => {
      debugLog('Save config button clicked');
      saveConfig();
    };
  }
  
  const closeConfigBtn = document.getElementById("close-config-btn") as HTMLButtonElement;
  if (closeConfigBtn) {
    closeConfigBtn.onclick = () => {
      debugLog('Close config button clicked');
      hideConfigPanel();
    };
  }
  
  const refreshStatusBtn = document.getElementById("refresh-status-btn") as HTMLButtonElement;
  if (refreshStatusBtn) {
    refreshStatusBtn.onclick = () => {
      debugLog('Refresh status button clicked');
      updateFileCount();
      updateStatus('Status refreshed', 'success');
    };
  }
  
  const refreshBtn = document.getElementById("refresh-btn") as HTMLButtonElement;
  if (refreshBtn) {
    refreshBtn.onclick = () => {
      debugLog('Footer refresh button clicked');
      updateFileCount();
      updateStatus('Status refreshed', 'success');
    };
  }
  
  // Add hidden file pickers if not present
  if (!document.getElementById('positionFilePicker')) {
    const positionPicker = document.createElement('input');
    positionPicker.type = 'file';
    positionPicker.id = 'positionFilePicker';
    positionPicker.accept = '.pdf';
    positionPicker.style.display = 'none';
    document.body.appendChild(positionPicker);
    debugLog('Created position file picker element');
  }
  
  if (!document.getElementById('cvFilesPicker')) {
    const cvPicker = document.createElement('input');
    cvPicker.type = 'file';
    cvPicker.id = 'cvFilesPicker';
    cvPicker.multiple = true;
    cvPicker.accept = '.pdf';
    cvPicker.style.display = 'none';
    document.body.appendChild(cvPicker);
    debugLog('Created CV files picker element');
  }
}

async function runDiagnostics() {
  debugLog('=== RUNNING FULL DIAGNOSTICS ===');
  
  // Check API key
  debugLog('Checking API Key...');
  const hasApiKey = !!claudeApiKey;
  debugLog(`API Key present: ${hasApiKey}`);
  if (hasApiKey) {
    debugLog(`API Key preview: ${claudeApiKey.substring(0, 10)}...`);
  }
  
  // Check proxy server
  debugLog('Testing proxy server connectivity...');
  try {
    const proxyResponse = await fetch('http://localhost:3001/health');
    if (proxyResponse.ok) {
      const health = await proxyResponse.json();
      debugLog('✓ Proxy server is running:', health);
    } else {
      debugLog('✗ Proxy server not responding properly');
    }
  } catch (error) {
    debugError('✗ Proxy server is NOT running - run "npm run proxy"', error);
  }
  
  // Check stored values
  debugLog('Checking stored values...');
  const keys = ['CLAUDE_API_KEY', 'POSITION_TEXT', 'POSITION_FILE_NAME', 'POSITION_FILE_ID', 'CURRENT_RUBRIC', 'POSITION_PDF_BASE64'];
  
  for (const key of keys) {
    try {
      const value = await excelService.getStoredValue(key);
      if (value) {
        debugLog(`${key}: Present (length: ${value.length})`);
        if (key === 'POSITION_PDF_BASE64') {
          debugLog(`PDF Base64 is stored, can be used for rubric generation`);
        }
      } else {
        debugLog(`${key}: Missing`);
      }
    } catch (error) {
      debugError(`Failed to get ${key}`, error);
    }
  }
  
  // Check if PDF is in sessionStorage
  if (typeof(Storage) !== "undefined") {
    const sessionPDF = sessionStorage.getItem('currentPDFBase64');
    if (sessionPDF) {
      debugLog(`PDF in sessionStorage: ${sessionPDF.length} bytes`);
    } else {
      debugLog('No PDF in sessionStorage');
    }
  }
  
  // Test Excel sheets exist
  debugLog('Checking Excel sheets...');
  try {
    await Excel.run(async (context) => {
      const sheets = context.workbook.worksheets;
      sheets.load('items/name');
      await context.sync();
      
      const sheetNames = sheets.items.map(s => s.name);
      debugLog('Found sheets:', sheetNames);
      
      const requiredSheets = [
        'Processing Log',
        'Processed Candidates',
        'Document Mapping',
        'Position Configuration',
        'PD Raw Text',
        'Dynamic Rubric Configuration',
        'Company Metrics',
        'Candidate Evaluations',
        'Detailed Evaluations',
        'Combined Analysis',
        'Summary Dashboard',
        'API Usage Tracking',
        '_Storage'
      ];
      
      requiredSheets.forEach(name => {
        if (sheetNames.includes(name)) {
          debugLog(`✓ Sheet exists: ${name}`);
        } else {
          debugError(`✗ Sheet missing: ${name}`, '');
        }
      });
    });
  } catch (error) {
    debugError('Failed to check sheets', error);
  }
  
  // Test Claude API connectivity via proxy
  if (hasApiKey) {
    debugLog('Testing Claude API connectivity via proxy...');
    try {
      const testResponse = await fetch('http://localhost:3001/api/claude', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': claudeApiKey
        },
        body: JSON.stringify({
          model: 'claude-3-5-sonnet-20241022',
          max_tokens: 10,
          messages: [{role: 'user', content: 'Test'}]
        })
      });
      
      debugLog(`Claude API test response status: ${testResponse.status}`);
      
      if (testResponse.ok) {
        debugLog('✓ Claude API connection successful via proxy');
      } else {
        const errorText = await testResponse.text();
        debugError('Claude API test failed', errorText);
      }
    } catch (error) {
      debugError('Claude API connectivity test failed', error);
    }
  } else {
    debugLog('Skipping API test - no API key configured');
  }
  
  debugLog('=== DIAGNOSTICS COMPLETE ===');
}

async function loadApiKey() {
  try {
    debugLog('Loading API key from storage...');
    const storedKey = await excelService.getStoredValue('CLAUDE_API_KEY');
    
    if (storedKey) {
      claudeApiKey = storedKey;
      claudeService.setApiKey(storedKey);
      debugLog('API key loaded successfully');
      
      // Update UI
      const apiStatus = document.getElementById("api-status");
      if (apiStatus) {
        apiStatus.textContent = "✅ API key loaded";
        apiStatus.style.color = "#4CAF50";
      }
      
      const apiInput = document.getElementById("api-key-input") as HTMLInputElement;
      if (apiInput) {
        apiInput.value = storedKey.substring(0, 10) + '...';
      }
      
      updateStatus("Claude API key loaded", "success");
      
      // Check if we can enable rubric generation
      const positionId = await excelService.getStoredValue('POSITION_FILE_ID');
      const positionPDF = await excelService.getStoredValue('POSITION_PDF_BASE64');
      if (positionId || positionPDF) {
        enableButtons(['generate-rubric-btn']);
      }
      
      // Check if we have a stored rubric
      const storedRubric = await excelService.getStoredValue('CURRENT_RUBRIC');
      if (storedRubric) {
        try {
          currentRubric = JSON.parse(storedRubric);
          debugLog('Rubric loaded from previous session', Object.keys(currentRubric));
          updateStatus("Rubric loaded from previous session", "success");
          enableButtons(['process-batch-btn']);
        } catch (e) {
          debugError('Failed to parse stored rubric', e);
        }
      }
    } else {
      debugLog('No API key found in storage');
      const apiStatus = document.getElementById("api-status");
      if (apiStatus) {
        apiStatus.textContent = "⚠️ No API key configured";
        apiStatus.style.color = "#FF9800";
      }
      updateStatus("Please configure Claude API key", "warning");
    }
  } catch (error) {
    debugError('Failed to load API key', error);
  }
}

async function saveApiKey() {
  const apiKeyInput = document.getElementById("api-key-input") as HTMLInputElement;
  const apiStatus = document.getElementById("api-status");
  
  if (!apiKeyInput || !apiKeyInput.value.trim()) {
    if (apiStatus) {
      apiStatus.textContent = "❌ Please enter an API key";
      apiStatus.style.color = "#F44336";
    }
    return;
  }
  
  const apiKey = apiKeyInput.value.trim();
  debugLog(`Saving API key: ${apiKey.substring(0, 10)}...`);
  
  // Basic validation
  if (apiKey.length < 20 || !apiKey.startsWith('sk-')) {
    if (apiStatus) {
      apiStatus.textContent = "❌ Invalid API key format";
      apiStatus.style.color = "#F44336";
    }
    updateStatus("Invalid API key format. Should start with 'sk-'", "error");
    return;
  }
  
  try {
    // Save the API key
    claudeApiKey = apiKey;
    claudeService.setApiKey(claudeApiKey);
    await excelService.setStoredValue('CLAUDE_API_KEY', claudeApiKey);
    
    debugLog('API key saved successfully');
    
    if (apiStatus) {
      apiStatus.textContent = "✅ API key saved successfully";
      apiStatus.style.color = "#4CAF50";
    }
    
    // Mask the input
    apiKeyInput.value = apiKey.substring(0, 10) + '...';
    
    updateStatus("Claude API key configured successfully", "success");
    
    // Enable rubric generation if position is loaded
    const positionId = await excelService.getStoredValue('POSITION_FILE_ID');
    const positionPDF = await excelService.getStoredValue('POSITION_PDF_BASE64');
    if (positionId || positionPDF) {
      enableButtons(['generate-rubric-btn']);
    }
    
  } catch (error: any) {
    debugError('Failed to save API key', error);
    if (apiStatus) {
      apiStatus.textContent = "❌ Failed to save API key";
      apiStatus.style.color = "#F44336";
    }
    updateStatus(`Failed to save API key: ${error.message}`, "error");
  }
}

async function setupSheets() {
  try {
    debugLog('Setting up all Excel sheets...');
    updateStatus("Creating evaluation sheets...", "info");
    
    await excelService.createAllSheets();
    
    updateStatus("All sheets created successfully", "success");
    debugLog('Sheets setup complete');
    
  } catch (error: any) {
    debugError('Failed to setup sheets', error);
    updateStatus(`Failed to setup sheets: ${error.message}`, "error");
  }
}

async function loadPosition() {
  try {
    debugLog('Opening file picker for position description...');
    updateStatus("Select a position description PDF file...", "info");
    
    const positionFile = await localFileService.selectPositionFile();
    debugLog('Selected file:', positionFile.name);
    debugLog('File ID:', positionFile.id);
    debugLog('File content length:', positionFile.content?.length);
    
    updateStatus(`Processing ${positionFile.name}...`, "info");
    
    // Process PDF with the service - this will store it
    const base64Content = await pdfService.extractTextFromBase64(positionFile.content);
    debugLog('PDF processed and stored, base64 length:', base64Content.length);
    
    // Store position metadata
    await excelService.setStoredValue('POSITION_FILE_ID', positionFile.id);
    await excelService.setStoredValue('POSITION_FILE_NAME', positionFile.name);
    await excelService.setStoredValue('POSITION_TEXT', base64Content);
    
    // Add position description to PD Raw Text sheet
    await excelService.addPDRawText(positionFile.name, base64Content, true);
    debugLog('Added position description to PD Raw Text sheet');
    
    // Update Excel Position Configuration
    await Excel.run(async (context) => {
      const sheet = context.workbook.worksheets.getItem('Position Configuration');
      
      // Clear existing data
      const usedRange = sheet.getUsedRange();
      if (usedRange) {
        usedRange.load('rowCount');
        await context.sync();
        if (usedRange.rowCount > 1) {
          sheet.getRangeByIndexes(1, 0, usedRange.rowCount - 1, 3).clear();
        }
      }
      
      // Add position info
      const range = sheet.getRange('A2:C5');
      range.values = [
        ['Position Title', positionFile.name, 'Loaded'],
        ['File ID', positionFile.id, 'Active'],
        ['Load Date', new Date().toLocaleDateString(), 'Current'],
        ['File Size', Math.round(base64Content.length / 1024) + ' KB (PDF)', 'Ready']
      ];
      
      await context.sync();
    });
    
    updateStatus(`Position loaded: ${positionFile.name}`, "success");
    
    if (claudeApiKey) {
      enableButtons(['generate-rubric-btn']);
    }
    
    updateFileCount();
    
  } catch (error: any) {
    debugError('Load position error', error);
    updateStatus(`Failed to load position: ${error.message}`, "error");
  }
}

async function generateRubric() {
  try {
    debugLog('=== STARTING RUBRIC GENERATION ===');
    
    if (!claudeApiKey) {
      debugError('No API key found', '');
      updateStatus("Please configure Claude API key first", "error");
      return;
    }
    
    debugLog('API key present:', claudeApiKey.substring(0, 10) + '...');
    
    updateStatus("Generating AI rubric (this may take 30-60 seconds)...", "info");
    updateProgress(10);
    
    // Try to get PDF content first
    const positionPDF = await pdfService.getStoredPDFContent();
    const positionText = positionPDF || await excelService.getStoredValue('POSITION_TEXT');
    
    debugLog('Position content loaded, length:', positionText?.length);
    debugLog('Content is PDF base64:', positionPDF ? 'YES' : 'NO');
    
    const positionName = await excelService.getStoredValue('POSITION_FILE_NAME');
    debugLog('Position name:', positionName);
    
    if (!positionText) {
      debugError('No position text found', '');
      updateStatus("Please load a position first", "warning");
      return;
    }
    
    updateProgress(20);
    
    debugLog('Calling Claude API with position content...');
    
    const rubric = await claudeService.generateRubric(positionText, positionName || 'Unknown Position');
    
    debugLog('Rubric received successfully');
    debugLog('Rubric categories:', Object.keys(rubric));
    
    updateProgress(60);
    
    // Store the rubric
    currentRubric = rubric;
    await excelService.setStoredValue('CURRENT_RUBRIC', JSON.stringify(rubric));
    debugLog('Rubric stored in Excel');
    
    updateProgress(70);
    
    // Write rubric to Excel sheets
    await excelService.writeDynamicRubric(rubric);
    debugLog('Rubric written to Excel sheet');
    
    updateProgress(90);
    
    // Update position configuration
    await Excel.run(async (context) => {
      const sheet = context.workbook.worksheets.getItem('Position Configuration');
      
      const usedRange = sheet.getUsedRange();
      usedRange.load('rowCount');
      await context.sync();
      
      const nextRow = usedRange ? usedRange.rowCount + 1 : 6;
      const range = sheet.getRange(`A${nextRow}:C${nextRow}`);
      range.values = [['Rubric Status', 'Generated', 'Ready']];
      
      await context.sync();
      debugLog('Updated Position Configuration sheet');
    });
    
    // Track API usage for rubric generation
    await excelService.updateAPIUsage(
      'RUBRIC GENERATION',
      'Generate Dynamic Rubric',
      8000, // Estimated input tokens
      4000  // Estimated output tokens
    );
    
    updateProgress(100);
    updateStatus("AI Rubric generated successfully!", "success");
    enableButtons(['process-batch-btn']);
    
    debugLog('=== RUBRIC GENERATION COMPLETE ===');
    
  } catch (error: any) {
    debugError('=== RUBRIC GENERATION ERROR ===', error);
    updateStatus(`Failed to generate rubric: ${error.message}`, "error");
    updateProgress(0);
  }
}

async function mapDocuments() {
  try {
    debugLog('Opening file picker for CV/cover letter files...');
    updateStatus("Select CV and cover letter PDF files...", "info");
    
    const files = await localFileService.selectCVFiles();
    debugLog(`Selected ${files.length} files`);
    
    updateStatus(`Processing ${files.length} files...`, "info");
    
    // Document mapping is done automatically in localFileService
    const mapping = localFileService.getDocumentMapping();
    
    debugLog(`Document mapping complete: ${mapping.size} candidates found`);
    
    // Write mapping to Excel
    await Excel.run(async (context) => {
      const sheet = context.workbook.worksheets.getItem('Document Mapping');
      
      // Clear existing data
      const usedRange = sheet.getUsedRange();
      if (usedRange) {
        usedRange.load('rowCount');
        await context.sync();
        if (usedRange.rowCount > 1) {
          const clearRange = sheet.getRangeByIndexes(1, 0, usedRange.rowCount - 1, 6);
          clearRange.clear();
        }
      }
      
      // Add mapping data
      const rows: any[][] = [];
      mapping.forEach((files, candidateName) => {
        rows.push([
          candidateName,
          files.cv?.name || '',
          files.coverLetter?.name || '',
          files.coverLetter ? 'Both' : 'CV Only',
          'Automatic',
          'High'
        ]);
      });
      
      if (rows.length > 0) {
        const dataRange = sheet.getRangeByIndexes(1, 0, rows.length, 6);
        dataRange.values = rows;
      }
      
      sheet.getUsedRange()?.format.autofitColumns();
      await context.sync();
    });
    
    updateStatus(`Mapped ${mapping.size} candidates successfully`, "success");
    
    if (currentRubric) {
      enableButtons(['process-batch-btn']);
    }
    
    updateFileCount();
    
  } catch (error: any) {
    debugError('Document mapping error', error);
    updateStatus(`Failed to map documents: ${error.message}`, "error");
  }
}

async function processCandidates() {
  try {
    debugLog('=== STARTING CANDIDATE PROCESSING ===');
    
    if (!currentRubric) {
      updateStatus("Please generate a rubric first", "error");
      return;
    }
    
    // Log the rubric to ensure it's complete
    debugLog('Current rubric categories:', Object.keys(currentRubric));
    
    const mapping = localFileService.getDocumentMapping();
    if (mapping.size === 0) {
      updateStatus("Please map documents first", "error");
      return;
    }
    
    updateStatus("Starting candidate evaluation...", "info");
    updateProgress(0);
    
    const candidates = Array.from(mapping.entries());
    const totalCandidates = candidates.length;
    let successCount = 0;
    let failCount = 0;
    let skippedCount = 0;
    
    // Process candidates with complete rubric
    for (let i = 0; i < candidates.length; i++) {
      const [candidateName, files] = candidates[i];
      const progressPercent = Math.round(((i + 1) / totalCandidates) * 100);
      
      updateStatus(`Processing ${candidateName} (${i + 1}/${totalCandidates})...`, "info");
      updateProgress(progressPercent);
      
      try {
        // Check for duplicates FIRST
        const isDuplicate = await excelService.checkForDuplicates(candidateName);
        if (isDuplicate) {
          debugLog(`Skipping duplicate candidate: ${candidateName}`);
          updateStatus(`Skipping ${candidateName} - already processed`, "warning");
          skippedCount++;
          await excelService.updateProcessingLog(
            `Skipped duplicate candidate: ${candidateName}`,
            'Warning'
          );
          continue; // Skip to next candidate
        }
        
        let cvBase64: string | null = null;
        let coverBase64: string | null = null;
        
        // Get CV file content
        if (files.cv) {
          const cvFileData = await localFileService.getFileByName(files.cv.name);
          if (cvFileData) {
            cvBase64 = cvFileData.content;
            debugLog(`CV loaded for ${candidateName}, size: ${cvBase64.length}`);
          }
        }
        
        // Get cover letter content if exists
        if (files.coverLetter) {
          const coverData = await localFileService.getFileByName(files.coverLetter.name);
          if (coverData) {
            coverBase64 = coverData.content;
            debugLog(`Cover letter loaded for ${candidateName}, size: ${coverBase64.length}`);
          }
        }
        
        if (!cvBase64) {
          throw new Error('No CV found for candidate');
        }
        
        debugLog(`Evaluating ${candidateName} with rubric containing ${Object.keys(currentRubric).length} categories`);
        
        let evaluation: any = null;
        let retryCount = 0;
        const maxRetries = 3;
        let lastError: any = null;
        
        while (retryCount < maxRetries && !evaluation) {
          try {
            debugLog(`Evaluation attempt ${retryCount + 1} for ${candidateName}`);
            
            evaluation = await claudeService.evaluateCandidateWithPDF(
              cvBase64,
              coverBase64,
              candidateName,
              currentRubric
            );
            
            // Verify we got a valid evaluation
            if (!evaluation || !evaluation.categories || Object.keys(evaluation.categories).length === 0) {
              throw new Error('Received invalid evaluation structure');
            }
            
            debugLog(`Evaluation complete for ${candidateName}:`);
            debugLog(`  - Total Score: ${evaluation.total_score}`);
            debugLog(`  - Categories evaluated: ${Object.keys(evaluation.categories).length}`);
            debugLog(`  - Has metrics: ${!!evaluation.metrics}`);
            debugLog(`  - Interview questions: ${evaluation.interview_questions?.length || 0}`);
            
            // Log a sample interview question to check personalization
            if (evaluation.interview_questions && evaluation.interview_questions.length > 0) {
              debugLog(`  - Sample question: ${evaluation.interview_questions[0].substring(0, 100)}...`);
            }
            
            // Log warning if not all categories evaluated
            if (Object.keys(evaluation.categories).length < 8) {
              console.warn(`WARNING: Only ${Object.keys(evaluation.categories).length} categories evaluated instead of 8`);
            }
            
            // Track API usage with actual tokens
            const tokensUsed = evaluation.actualTokensUsed || { input: 5000, output: 3000 };
            await excelService.updateAPIUsage(
              candidateName,
              'Candidate Evaluation',
              tokensUsed.input,
              tokensUsed.output
            );
            
          } catch (evalError: any) {
            lastError = evalError;
            retryCount++;
            debugError(`Evaluation attempt ${retryCount} failed for ${candidateName}`, evalError);
            
            if (evalError.message && evalError.message.includes('Rate limit')) {
              if (retryCount < maxRetries) {
                // Exponential backoff for rate limits
                const waitTime = Math.min(70000, 15000 * Math.pow(2, retryCount - 1));
                debugLog(`Rate limit hit. Waiting ${waitTime/1000}s before retry ${retryCount + 1}...`);
                updateStatus(`Rate limit reached. Waiting ${Math.round(waitTime/1000)}s...`, "warning");
                await delay(waitTime);
                continue;
              }
            } else if (evalError.message && evalError.message.includes('incomplete or errored')) {
              // Claude response was cut off - retry with longer max_tokens
              if (retryCount < maxRetries) {
                debugLog(`Response was incomplete. Retrying with adjusted parameters...`);
                await delay(5000);
                continue;
              }
            } else if (retryCount < maxRetries) {
              // For other errors, shorter wait
              const waitTime = 5000;
              debugLog(`Error occurred. Waiting ${waitTime/1000}s before retry ${retryCount + 1}...`);
              await delay(waitTime);
              continue;
            }
            
            // If all retries failed, create default evaluation
            if (retryCount >= maxRetries) {
              debugError(`All retries failed for ${candidateName}`, lastError);
              
              // Create proper default evaluation structure
              evaluation = {
                candidate_name: candidateName,
                total_score: 0,
                recommendation: 'Unable to evaluate',
                categories: Object.fromEntries(
                  Object.entries(currentRubric).map(([catName, catData]: [string, any]) => [
                    catName,
                    {
                      category_score: 0,
                      weight: catData.weight,
                      attributes: catData.attributes.map((attr: any) => ({
                        name: attr.name,
                        score: 0,
                        weight: attr.weight,
                        evidence: 'Evaluation failed after multiple attempts'
                      }))
                    }
                  ])
                ),
                strengths: [`Evaluation failed: ${lastError?.message || 'Unknown error'}`],
                development_areas: ['Unable to complete evaluation'],
                overall_assessment: `Evaluation could not be completed after ${maxRetries} attempts`,
                interview_questions: [
                  'Unable to generate personalized questions due to evaluation failure',
                  'Please review the candidate manually'
                ],
                metrics: {
                  current_company: "Unknown",
                  time_at_current_company_years: 0,
                  advancement_current_company: "Unknown",
                  total_career_experience_years: 0,
                  years_industry_experience: 0,
                  relevant_industry: "Unknown",
                  avg_time_per_role_years: 0,
                  years_managerial_experience: 0,
                  max_team_size_managed: 0,
                  budget_managed: "N/A",
                  short_stints_count: 0,
                  job_hopping_flag: false,
                  notable_companies: [],
                  industries_worked: [],
                  universities: [],
                  degrees: [],
                  certifications: [],
                  functional_expertise: [],
                  technical_skills: [],
                  x_factor: [],
                  language_quality: "Unknown"
                }
              };
            }
          }
        }
        
        // Process the evaluation (whether successful or default)
        if (evaluation) {
          evaluation.documentsStatus = files.coverLetter ? 'Both' : 'CV Only';
          evaluation.matchConfidence = evaluation.total_score > 0 ? 'High' : 'Failed';
          evaluation.matchMethod = evaluation.total_score > 0 ? 'Direct' : 'Error';
          
          // Write to Excel
          await excelService.addCandidateEvaluation(evaluation);
          await excelService.addCompanyMetrics(evaluation);
          await excelService.addDetailedEvaluations(evaluation);
          await excelService.addCombinedAnalysis(evaluation);
          
          // Record processed candidate with score
          await excelService.recordProcessedCandidate(
            candidateName,
            files.cv?.name || '',
            files.coverLetter?.name || '',
            evaluation.total_score
          );
          debugLog(`Recorded ${candidateName} in Processed Candidates sheet`);
          
          const logMessage = evaluation.total_score > 0 
            ? `Successfully evaluated ${candidateName} - Score: ${evaluation.total_score.toFixed(1)}`
            : `Failed to evaluate ${candidateName} - Default values used`;
          
          await excelService.updateProcessingLog(logMessage, evaluation.total_score > 0 ? 'Success' : 'Error');
          
          if (evaluation.total_score > 0) {
            successCount++;
          } else {
            failCount++;
          }
        }
        
        // Delay between candidates to avoid rate limits
        if (i < candidates.length - 1) {
          const delayTime = 4000; // Increased delay
          debugLog(`Waiting ${delayTime/1000}s before next candidate...`);
          await delay(delayTime);
        }
        
      } catch (error: any) {
        failCount++;
        debugError(`Critical error processing ${candidateName}`, error);
        await excelService.updateProcessingLog(
          `Critical error processing ${candidateName}: ${error.message}`,
          'Error'
        );
        
        // Still record as processed (failed)
        await excelService.recordProcessedCandidate(
          candidateName,
          files.cv?.name || '',
          files.coverLetter?.name || '',
          0
        );
      }
    }
    
    // Update summary
    await excelService.updateSummaryDashboard();
    
    updateProgress(100);
    const statusMessage = successCount === totalCandidates 
      ? `All ${totalCandidates} candidates evaluated successfully!`
      : skippedCount > 0 
        ? `Processing complete: ${successCount} successful, ${failCount} failed, ${skippedCount} skipped (duplicates)`
        : `Processing complete: ${successCount} successful, ${failCount} failed`;
    
    updateStatus(statusMessage, successCount > 0 ? "success" : "error");
    
    debugLog('=== CANDIDATE PROCESSING COMPLETE ===');
    debugLog(`Final results: ${successCount} successful, ${failCount} failed, ${skippedCount} skipped`);
    
  } catch (error: any) {
    debugError('Critical error in processCandidates', error);
    updateStatus(`Critical error: ${error.message}`, "error");
    updateProgress(0);
  }
}

// Helper functions
function updateStatus(message: string, type: string = "info") {
  const statusDiv = document.getElementById("status-message");
  if (statusDiv) {
    statusDiv.textContent = message;
    statusDiv.className = `status-${type}`;
  }
  debugLog(`Status: [${type}] ${message}`);
}

function updateProgress(percent: number) {
  const progressFill = document.querySelector(".progress-fill") as HTMLElement;
  if (progressFill) {
    progressFill.style.width = `${percent}%`;
  }
  debugLog(`Progress: ${percent}%`);
}

function updateFileCount() {
  const counts = localFileService.getStoredFilesCount();
  const fileCountDiv = document.getElementById("file-count");
  if (fileCountDiv) {
    fileCountDiv.textContent = `Files loaded: ${counts.positions} position(s), ${counts.cvs} CV/cover letter(s)`;
  }
  debugLog(`File count updated: ${counts.positions} positions, ${counts.cvs} CVs`);
}

function enableButtons(buttonIds: string[]) {
  buttonIds.forEach(id => {
    const button = document.getElementById(id) as HTMLButtonElement;
    if (button) {
      button.disabled = false;
      debugLog(`Enabled button: ${id}`);
    }
  });
}

function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// UI helper functions
function showConfigPanel() {
  const configPanel = document.getElementById('config-panel');
  const actionPanel = document.getElementById('action-panel');
  if (configPanel) {
    configPanel.style.display = 'block';
  }
  if (actionPanel) {
    actionPanel.style.display = 'block';
  }
  debugLog('Config and action panels shown');
}

function hideConfigPanel() {
  const configPanel = document.getElementById('config-panel');
  if (configPanel) {
    configPanel.style.display = 'none';
  }
  debugLog('Config panel hidden');
}

async function saveConfig() {
  // In local file mode, we just need to save the API key
  await saveApiKey();
  updateStatus('Configuration saved', 'success');
}

// Extend window interface for debug functions
declare global {
  interface Window {
    addDebugLog?: (message: string, level: string) => void;
  }
}