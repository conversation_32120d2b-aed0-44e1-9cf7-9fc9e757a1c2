 export const Config = {
  // Azure AD App Registration (disabled in local mode)
  AZURE_CLIENT_ID: 'disabled-in-local-mode',
  AZURE_TENANT_ID: 'disabled-in-local-mode',
  AZURE_REDIRECT_URI: window.location.origin + '/taskpane.html',
  
  // File Storage Mode
  USE_LOCAL_FILES: true,  
  USE_ONEDRIVE: false,    
  USE_SHAREPOINT: false,  
  
  // Add this line:
  SHAREPOINT_SITE_URL: 'disabled-in-local-mode',
  
  // Local folders (for reference only)
  LOCAL_POSITION_PATH: 'C:\\Users\\<USER>\\OneDrive\\Personal + old stuff\\Desktop\\RubiRecruit Testing\\1. Position Descriptions',
  LOCAL_CV_PATH: 'C:\\Users\\<USER>\\OneDrive\\Personal + old stuff\\Desktop\\RubiRecruit Testing\\2. CVs and Cover Letters',
  
  // Claude API Configuration
  CLAUDE_API_URL: 'https://api.anthropic.com/v1/messages',
  CLAUDE_MODEL: 'claude-3-5-sonnet-20241022',
  
  // App Information
  VERSION: '1.0.0-LOCAL',
  APP_NAME: 'RubiRecruit for Excel (Local File Mode)',
  
  // Graph API Configuration (disabled)
  GRAPH_API_VERSION: 'v1.0',
  GRAPH_API_BASE_URL: 'disabled-in-local-mode'
};