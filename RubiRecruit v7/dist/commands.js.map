{"version": 3, "file": "commands.js", "mappings": ";;;;AAAA;AACA;AACA;AACA;;AAEA;;AAEAA,MAAM,CAACC,OAAO,CAAC,YAAM;EACnB;AAAA,CACD,CAAC;;AAEF;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAACC,KAAiC,EAAE;EACjD,IAAMC,OAA0C,GAAG;IACjDC,IAAI,EAAEL,MAAM,CAACM,YAAY,CAACC,2BAA2B,CAACC,oBAAoB;IAC1EJ,OAAO,EAAE,mBAAmB;IAC5BK,IAAI,EAAE,YAAY;IAClBC,UAAU,EAAE;EACd,CAAC;;EAED;EACAV,MAAM,CAACW,OAAO,CAACC,OAAO,CAACC,IAAI,CAACC,oBAAoB,CAACC,YAAY,CAC3D,+BAA+B,EAC/BX,OACF,CAAC;;EAED;EACAD,KAAK,CAACa,SAAS,CAAC,CAAC;AACnB;;AAEA;AACAhB,MAAM,CAACiB,OAAO,CAACC,SAAS,CAAC,QAAQ,EAAEhB,MAAM,CAAC,C", "sources": ["webpack://office-addin-taskpane/./src/commands/commands.ts"], "sourcesContent": ["/*\n * Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n * See LICENSE in the project root for license information.\n */\n\n/* global Office */\n\nOffice.onReady(() => {\n  // If needed, Office.js is ready to be called.\n});\n\n/**\n * Shows a notification when the add-in command is executed.\n * @param event\n */\nfunction action(event: Office.AddinCommands.Event) {\n  const message: Office.NotificationMessageDetails = {\n    type: Office.MailboxEnums.ItemNotificationMessageType.InformationalMessage,\n    message: \"Performed action.\",\n    icon: \"Icon.80x80\",\n    persistent: true,\n  };\n\n  // Show a notification message.\n  Office.context.mailbox.item.notificationMessages.replaceAsync(\n    \"ActionPerformanceNotification\",\n    message\n  );\n\n  // Be sure to indicate when the add-in command function is complete.\n  event.completed();\n}\n\n// Register the function with Office.\nOffice.actions.associate(\"action\", action);\n"], "names": ["Office", "onReady", "action", "event", "message", "type", "MailboxEnums", "ItemNotificationMessageType", "InformationalMessage", "icon", "persistent", "context", "mailbox", "item", "notificationMessages", "replaceAsync", "completed", "actions", "associate"], "sourceRoot": ""}