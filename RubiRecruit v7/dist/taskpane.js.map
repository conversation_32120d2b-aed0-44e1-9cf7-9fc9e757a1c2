{"version": 3, "file": "taskpane.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;UAAA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;UAEA;UACA;;;;;WCzBA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA,E;;;;;WCPA;WACA;WACA;WACA;WACA,GAAG;WACH;WACA;WACA,CAAC,I;;;;;WCPD,8CAA8C,yD;;;;;WCA9C;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D,E;;;;;WCNA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA,kC;;;;;WClBA;;WAEA;WACA;WACA;WACA;WACA;WACA;;WAEA;;WAEA;;WAEA;;WAEA;;WAEA;;WAEA;;WAEA,oB;;;;;;;;;;;;;;;0BCpBA,uKAAAA,CAAA,EAAAC,CAAA,EAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,WAAA,8BAAAC,EAAAN,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAE,CAAA,QAAAC,CAAA,GAAAL,CAAA,IAAAA,CAAA,CAAAM,SAAA,YAAAC,SAAA,GAAAP,CAAA,GAAAO,SAAA,EAAAC,CAAA,GAAAC,MAAA,CAAAC,MAAA,CAAAL,CAAA,CAAAC,SAAA,UAAAK,mBAAA,CAAAH,CAAA,uBAAAV,CAAA,EAAAE,CAAA,EAAAE,CAAA,QAAAE,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAI,CAAA,MAAAC,CAAA,GAAAX,CAAA,QAAAY,CAAA,OAAAC,CAAA,KAAAF,CAAA,KAAAb,CAAA,KAAAgB,CAAA,EAAApB,CAAA,EAAAqB,CAAA,EAAAC,CAAA,EAAAN,CAAA,EAAAM,CAAA,CAAAC,IAAA,CAAAvB,CAAA,MAAAsB,CAAA,WAAAA,EAAArB,CAAA,EAAAC,CAAA,WAAAM,CAAA,GAAAP,CAAA,EAAAQ,CAAA,MAAAG,CAAA,GAAAZ,CAAA,EAAAmB,CAAA,CAAAf,CAAA,GAAAF,CAAA,EAAAmB,CAAA,gBAAAC,EAAApB,CAAA,EAAAE,CAAA,SAAAK,CAAA,GAAAP,CAAA,EAAAU,CAAA,GAAAR,CAAA,EAAAH,CAAA,OAAAiB,CAAA,IAAAF,CAAA,KAAAV,CAAA,IAAAL,CAAA,GAAAgB,CAAA,CAAAO,MAAA,EAAAvB,CAAA,UAAAK,CAAA,EAAAE,CAAA,GAAAS,CAAA,CAAAhB,CAAA,GAAAqB,CAAA,GAAAH,CAAA,CAAAF,CAAA,EAAAQ,CAAA,GAAAjB,CAAA,KAAAN,CAAA,QAAAI,CAAA,GAAAmB,CAAA,KAAArB,CAAA,MAAAQ,CAAA,GAAAJ,CAAA,EAAAC,CAAA,GAAAD,CAAA,YAAAC,CAAA,WAAAD,CAAA,MAAAA,CAAA,MAAAR,CAAA,IAAAQ,CAAA,OAAAc,CAAA,MAAAhB,CAAA,GAAAJ,CAAA,QAAAoB,CAAA,GAAAd,CAAA,QAAAC,CAAA,MAAAU,CAAA,CAAAC,CAAA,GAAAhB,CAAA,EAAAe,CAAA,CAAAf,CAAA,GAAAI,CAAA,OAAAc,CAAA,GAAAG,CAAA,KAAAnB,CAAA,GAAAJ,CAAA,QAAAM,CAAA,MAAAJ,CAAA,IAAAA,CAAA,GAAAqB,CAAA,MAAAjB,CAAA,MAAAN,CAAA,EAAAM,CAAA,MAAAJ,CAAA,EAAAe,CAAA,CAAAf,CAAA,GAAAqB,CAAA,EAAAhB,CAAA,cAAAH,CAAA,IAAAJ,CAAA,aAAAmB,CAAA,QAAAH,CAAA,OAAAd,CAAA,qBAAAE,CAAA,EAAAW,CAAA,EAAAQ,CAAA,QAAAT,CAAA,YAAAU,SAAA,uCAAAR,CAAA,UAAAD,CAAA,IAAAK,CAAA,CAAAL,CAAA,EAAAQ,CAAA,GAAAhB,CAAA,GAAAQ,CAAA,EAAAL,CAAA,GAAAa,CAAA,GAAAxB,CAAA,GAAAQ,CAAA,OAAAT,CAAA,GAAAY,CAAA,MAAAM,CAAA,KAAAV,CAAA,KAAAC,CAAA,GAAAA,CAAA,QAAAA,CAAA,SAAAU,CAAA,CAAAf,CAAA,QAAAkB,CAAA,CAAAb,CAAA,EAAAG,CAAA,KAAAO,CAAA,CAAAf,CAAA,GAAAQ,CAAA,GAAAO,CAAA,CAAAC,CAAA,GAAAR,CAAA,aAAAI,CAAA,MAAAR,CAAA,QAAAC,CAAA,KAAAH,CAAA,YAAAL,CAAA,GAAAO,CAAA,CAAAF,CAAA,WAAAL,CAAA,GAAAA,CAAA,CAAA0B,IAAA,CAAAnB,CAAA,EAAAI,CAAA,UAAAc,SAAA,2CAAAzB,CAAA,CAAA2B,IAAA,SAAA3B,CAAA,EAAAW,CAAA,GAAAX,CAAA,CAAA4B,KAAA,EAAApB,CAAA,SAAAA,CAAA,oBAAAA,CAAA,KAAAR,CAAA,GAAAO,CAAA,CAAAsB,MAAA,KAAA7B,CAAA,CAAA0B,IAAA,CAAAnB,CAAA,GAAAC,CAAA,SAAAG,CAAA,GAAAc,SAAA,uCAAApB,CAAA,gBAAAG,CAAA,OAAAD,CAAA,GAAAR,CAAA,cAAAC,CAAA,IAAAiB,CAAA,GAAAC,CAAA,CAAAf,CAAA,QAAAQ,CAAA,GAAAV,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,EAAAe,CAAA,OAAAE,CAAA,kBAAApB,CAAA,IAAAO,CAAA,GAAAR,CAAA,EAAAS,CAAA,MAAAG,CAAA,GAAAX,CAAA,cAAAe,CAAA,mBAAAa,KAAA,EAAA5B,CAAA,EAAA2B,IAAA,EAAAV,CAAA,SAAAhB,CAAA,EAAAI,CAAA,EAAAE,CAAA,QAAAI,CAAA,QAAAS,CAAA,gBAAAV,UAAA,cAAAoB,kBAAA,cAAAC,2BAAA,KAAA/B,CAAA,GAAAY,MAAA,CAAAoB,cAAA,MAAAxB,CAAA,MAAAL,CAAA,IAAAH,CAAA,CAAAA,CAAA,IAAAG,CAAA,SAAAW,mBAAA,CAAAd,CAAA,OAAAG,CAAA,iCAAAH,CAAA,GAAAW,CAAA,GAAAoB,0BAAA,CAAAtB,SAAA,GAAAC,SAAA,CAAAD,SAAA,GAAAG,MAAA,CAAAC,MAAA,CAAAL,CAAA,YAAAO,EAAAhB,CAAA,WAAAa,MAAA,CAAAqB,cAAA,GAAArB,MAAA,CAAAqB,cAAA,CAAAlC,CAAA,EAAAgC,0BAAA,KAAAhC,CAAA,CAAAmC,SAAA,GAAAH,0BAAA,EAAAjB,mBAAA,CAAAf,CAAA,EAAAM,CAAA,yBAAAN,CAAA,CAAAU,SAAA,GAAAG,MAAA,CAAAC,MAAA,CAAAF,CAAA,GAAAZ,CAAA,WAAA+B,iBAAA,CAAArB,SAAA,GAAAsB,0BAAA,EAAAjB,mBAAA,CAAAH,CAAA,iBAAAoB,0BAAA,GAAAjB,mBAAA,CAAAiB,0BAAA,iBAAAD,iBAAA,GAAAA,iBAAA,CAAAK,WAAA,wBAAArB,mBAAA,CAAAiB,0BAAA,EAAA1B,CAAA,wBAAAS,mBAAA,CAAAH,CAAA,GAAAG,mBAAA,CAAAH,CAAA,EAAAN,CAAA,gBAAAS,mBAAA,CAAAH,CAAA,EAAAR,CAAA,iCAAAW,mBAAA,CAAAH,CAAA,8DAAAyB,YAAA,YAAAA,aAAA,aAAAC,CAAA,EAAA9B,CAAA,EAAA+B,CAAA,EAAAvB,CAAA;AAAA,SAAAD,oBAAAf,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAH,CAAA,QAAAO,CAAA,GAAAK,MAAA,CAAA2B,cAAA,QAAAhC,CAAA,uBAAAR,CAAA,IAAAQ,CAAA,QAAAO,mBAAA,YAAA0B,mBAAAzC,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAH,CAAA,aAAAK,EAAAJ,CAAA,EAAAE,CAAA,IAAAW,mBAAA,CAAAf,CAAA,EAAAE,CAAA,YAAAF,CAAA,gBAAA0C,OAAA,CAAAxC,CAAA,EAAAE,CAAA,EAAAJ,CAAA,SAAAE,CAAA,GAAAM,CAAA,GAAAA,CAAA,CAAAR,CAAA,EAAAE,CAAA,IAAA2B,KAAA,EAAAzB,CAAA,EAAAuC,UAAA,GAAA1C,CAAA,EAAA2C,YAAA,GAAA3C,CAAA,EAAA4C,QAAA,GAAA5C,CAAA,MAAAD,CAAA,CAAAE,CAAA,IAAAE,CAAA,IAAAE,CAAA,aAAAA,CAAA,cAAAA,CAAA,mBAAAS,mBAAA,CAAAf,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAH,CAAA;AAAA,SAAA6C,mBAAA1C,CAAA,EAAAH,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAI,CAAA,EAAAe,CAAA,EAAAZ,CAAA,cAAAD,CAAA,GAAAJ,CAAA,CAAAiB,CAAA,EAAAZ,CAAA,GAAAG,CAAA,GAAAJ,CAAA,CAAAqB,KAAA,WAAAzB,CAAA,gBAAAJ,CAAA,CAAAI,CAAA,KAAAI,CAAA,CAAAoB,IAAA,GAAA3B,CAAA,CAAAW,CAAA,IAAAmC,OAAA,CAAAC,OAAA,CAAApC,CAAA,EAAAqC,IAAA,CAAA/C,CAAA,EAAAI,CAAA;AAAA,SAAA4C,kBAAA9C,CAAA,6BAAAH,CAAA,SAAAD,CAAA,GAAAmD,SAAA,aAAAJ,OAAA,WAAA7C,CAAA,EAAAI,CAAA,QAAAe,CAAA,GAAAjB,CAAA,CAAAgD,KAAA,CAAAnD,CAAA,EAAAD,CAAA,YAAAqD,MAAAjD,CAAA,IAAA0C,kBAAA,CAAAzB,CAAA,EAAAnB,CAAA,EAAAI,CAAA,EAAA+C,KAAA,EAAAC,MAAA,UAAAlD,CAAA,cAAAkD,OAAAlD,CAAA,IAAA0C,kBAAA,CAAAzB,CAAA,EAAAnB,CAAA,EAAAI,CAAA,EAAA+C,KAAA,EAAAC,MAAA,WAAAlD,CAAA,KAAAiD,KAAA;AADA;AACA;AACA;AACA;;AAEA;;AAEAE,MAAM,CAACC,OAAO,CAAC,UAACC,IAAI,EAAK;EACvB,IAAIA,IAAI,CAACC,IAAI,KAAKH,MAAM,CAACI,QAAQ,CAACC,KAAK,EAAE;IACvCC,QAAQ,CAACC,cAAc,CAAC,cAAc,CAAC,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;IAC9DH,QAAQ,CAACC,cAAc,CAAC,UAAU,CAAC,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;IAC1DH,QAAQ,CAACC,cAAc,CAAC,KAAK,CAAC,CAACG,OAAO,GAAGC,GAAG;EAC9C;AACF,CAAC,CAAC;AAEK,SAAeA,GAAGA,CAAA;EAAA,OAAAC,IAAA,CAAAf,KAAA,OAAAD,SAAA;AAAA;AAoBxB,SAAAgB,KAAA;EAAAA,IAAA,GAAAjB,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CApBM,SAAA6B,SAAA;IAAA,IAAAC,EAAA;IAAA,OAAAhC,YAAA,GAAAC,CAAA,WAAAgC,SAAA;MAAA,kBAAAA,SAAA,CAAArD,CAAA,GAAAqD,SAAA,CAAAlE,CAAA;QAAA;UAAAkE,SAAA,CAAArD,CAAA;UAAAqD,SAAA,CAAAlE,CAAA;UAAA,OAEGwD,KAAK,CAACM,GAAG;YAAA,IAAAK,IAAA,GAAArB,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAAC,SAAAiC,QAAOC,OAAO;cAAA,IAAAC,KAAA;cAAA,OAAArC,YAAA,GAAAC,CAAA,WAAAqC,QAAA;gBAAA,kBAAAA,QAAA,CAAAvE,CAAA;kBAAA;oBAC5B;AACN;AACA;oBACYsE,KAAK,GAAGD,OAAO,CAACG,QAAQ,CAACC,gBAAgB,CAAC,CAAC,EAEjD;oBACAH,KAAK,CAACI,IAAI,CAAC,SAAS,CAAC;;oBAErB;oBACAJ,KAAK,CAACK,MAAM,CAACC,IAAI,CAACC,KAAK,GAAG,QAAQ;oBAACN,QAAA,CAAAvE,CAAA;oBAAA,OAE7BqE,OAAO,CAACS,IAAI,CAAC,CAAC;kBAAA;oBACpBC,OAAO,CAACC,GAAG,0BAAAC,MAAA,CAA0BX,KAAK,CAACY,OAAO,MAAG,CAAC;kBAAC;oBAAA,OAAAX,QAAA,CAAAtD,CAAA;gBAAA;cAAA,GAAAmD,OAAA;YAAA,CACxD;YAAA,iBAAAe,EAAA;cAAA,OAAAhB,IAAA,CAAAnB,KAAA,OAAAD,SAAA;YAAA;UAAA,IAAC;QAAA;UAAAmB,SAAA,CAAAlE,CAAA;UAAA;QAAA;UAAAkE,SAAA,CAAArD,CAAA;UAAAoD,EAAA,GAAAC,SAAA,CAAAlD,CAAA;UAEF+D,OAAO,CAACK,KAAK,CAAAnB,EAAM,CAAC;QAAC;UAAA,OAAAC,SAAA,CAAAjD,CAAA;MAAA;IAAA,GAAA+C,QAAA;EAAA,CAExB;EAAA,OAAAD,IAAA,CAAAf,KAAA,OAAAD,SAAA;AAAA,C;;;;;;;;ACnCD;AACA,yCAAyC,kHAAiC;AAC1E,yCAAyC,6HAA+C;AACxF;AACA,qtDAAqtD;AACrtD;AACA,+DAAe,IAAI,E", "sources": ["webpack://office-addin-taskpane/webpack/bootstrap", "webpack://office-addin-taskpane/webpack/runtime/define property getters", "webpack://office-addin-taskpane/webpack/runtime/global", "webpack://office-addin-taskpane/webpack/runtime/hasOwnProperty shorthand", "webpack://office-addin-taskpane/webpack/runtime/make namespace object", "webpack://office-addin-taskpane/webpack/runtime/publicPath", "webpack://office-addin-taskpane/webpack/runtime/jsonp chunk loading", "webpack://office-addin-taskpane/./src/taskpane/taskpane.ts", "webpack://office-addin-taskpane/./src/taskpane/taskpane.html"], "sourcesContent": ["// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "// define __esModule on exports\n__webpack_require__.r = function(exports) {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "var scriptUrl;\nif (__webpack_require__.g.importScripts) scriptUrl = __webpack_require__.g.location + \"\";\nvar document = __webpack_require__.g.document;\nif (!scriptUrl && document) {\n\tif (document.currentScript && document.currentScript.tagName.toUpperCase() === 'SCRIPT')\n\t\tscriptUrl = document.currentScript.src;\n\tif (!scriptUrl) {\n\t\tvar scripts = document.getElementsByTagName(\"script\");\n\t\tif(scripts.length) {\n\t\t\tvar i = scripts.length - 1;\n\t\t\twhile (i > -1 && (!scriptUrl || !/^http(s?):/.test(scriptUrl))) scriptUrl = scripts[i--].src;\n\t\t}\n\t}\n}\n// When supporting browsers where an automatic publicPath is not supported you must specify an output.publicPath manually via configuration\n// or pass an empty string (\"\") and set the __webpack_public_path__ variable from your code to use your own logic.\nif (!scriptUrl) throw new Error(\"Automatic publicPath is not supported in this browser\");\nscriptUrl = scriptUrl.replace(/^blob:/, \"\").replace(/#.*$/, \"\").replace(/\\?.*$/, \"\").replace(/\\/[^\\/]+$/, \"/\");\n__webpack_require__.p = scriptUrl;", "__webpack_require__.b = document.baseURI || self.location.href;\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t\"taskpane\": 0\n};\n\n// no chunk on demand loading\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n// no on chunks loaded\n\n// no jsonp function", "/*\n * Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n * See LICENSE in the project root for license information.\n */\n\n/* global console, document, Excel, Office */\n\nOffice.onReady((info) => {\n  if (info.host === Office.HostType.Excel) {\n    document.getElementById(\"sideload-msg\").style.display = \"none\";\n    document.getElementById(\"app-body\").style.display = \"flex\";\n    document.getElementById(\"run\").onclick = run;\n  }\n});\n\nexport async function run() {\n  try {\n    await Excel.run(async (context) => {\n      /**\n       * Insert your Excel code here\n       */\n      const range = context.workbook.getSelectedRange();\n\n      // Read the range address\n      range.load(\"address\");\n\n      // Update the fill color\n      range.format.fill.color = \"yellow\";\n\n      await context.sync();\n      console.log(`The range address was ${range.address}.`);\n    });\n  } catch (error) {\n    console.error(error);\n  }\n}\n", "// Imports\nvar ___HTML_LOADER_IMPORT_0___ = new URL(\"./taskpane.css\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_1___ = new URL(\"../../assets/logo-filled.png\", import.meta.url);\n// Module\nvar code = \"<!-- Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT License. -->\\n<!-- This file shows how to design a first-run page that provides a welcome screen to the user about the features of the add-in. -->\\n\\n<!DOCTYPE html>\\n<html>\\n\\n<head>\\n    <meta charset=\\\"UTF-8\\\" />\\n    <meta http-equiv=\\\"X-UA-Compatible\\\" content=\\\"IE=Edge\\\" />\\n    <meta name=\\\"viewport\\\" content=\\\"width=device-width, initial-scale=1\\\">\\n    <title>Contoso Task Pane Add-in</title>\\n\\n    <!-- Office JavaScript API -->\\n    <\" + \"script type=\\\"text/javascript\\\" src=\\\"https://appsforoffice.microsoft.com/lib/1/hosted/office.js\\\"><\" + \"/script>\\n\\n    <!-- For more information on Fluent UI, visit https://developer.microsoft.com/fluentui#/. -->\\n    <link rel=\\\"stylesheet\\\" href=\\\"https://res-1.cdn.office.net/files/fabric-cdn-prod_20230815.002/office-ui-fabric-core/11.1.0/css/fabric.min.css\\\"/>\\n\\n    <!-- Template styles -->\\n    <link href=\\\"\" + ___HTML_LOADER_IMPORT_0___ + \"\\\" rel=\\\"stylesheet\\\" type=\\\"text/css\\\" />\\n</head>\\n\\n<body class=\\\"ms-font-m ms-welcome ms-Fabric\\\">\\n    <header class=\\\"ms-welcome__header ms-bgColor-neutralLighter\\\">\\n        <img width=\\\"90\\\" height=\\\"90\\\" src=\\\"\" + ___HTML_LOADER_IMPORT_1___ + \"\\\" alt=\\\"Contoso\\\" title=\\\"Contoso\\\" />\\n        <h1 class=\\\"ms-font-su\\\">Welcome</h1>\\n    </header>\\n    <section id=\\\"sideload-msg\\\" class=\\\"ms-welcome__main\\\">\\n        <h2 class=\\\"ms-font-xl\\\">Please <a target=\\\"_blank\\\" href=\\\"https://learn.microsoft.com/office/dev/add-ins/testing/test-debug-office-add-ins#sideload-an-office-add-in-for-testing\\\">sideload</a> your add-in to see app body.</h2>\\n    </section>\\n    <main id=\\\"app-body\\\" class=\\\"ms-welcome__main\\\" style=\\\"display: none;\\\">\\n        <h2 class=\\\"ms-font-xl\\\"> Discover what Office Add-ins can do for you today! </h2>\\n        <ul class=\\\"ms-List ms-welcome__features\\\">\\n            <li class=\\\"ms-ListItem\\\">\\n                <i class=\\\"ms-Icon ms-Icon--Ribbon ms-font-xl\\\"></i>\\n                <span class=\\\"ms-font-m\\\">Achieve more with Office integration</span>\\n            </li>\\n            <li class=\\\"ms-ListItem\\\">\\n                <i class=\\\"ms-Icon ms-Icon--Unlock ms-font-xl\\\"></i>\\n                <span class=\\\"ms-font-m\\\">Unlock features and functionality</span>\\n            </li>\\n            <li class=\\\"ms-ListItem\\\">\\n                <i class=\\\"ms-Icon ms-Icon--Design ms-font-xl\\\"></i>\\n                <span class=\\\"ms-font-m\\\">Create and visualize like a pro</span>\\n            </li>\\n        </ul>\\n        <p class=\\\"ms-font-l\\\">Modify the source files, then click <b>Run</b>.</p>\\n        <div role=\\\"button\\\" id=\\\"run\\\" class=\\\"ms-welcome__action ms-Button ms-Button--hero ms-font-xl\\\">\\n            <span class=\\\"ms-Button-label\\\">Run</span>\\n        </div>\\n        <p><label id=\\\"item-subject\\\"></label></p>\\n    </main>\\n</body>\\n\\n</html>\\n\";\n// Exports\nexport default code;"], "names": ["e", "t", "r", "Symbol", "n", "iterator", "o", "toStringTag", "i", "c", "prototype", "Generator", "u", "Object", "create", "_regeneratorDefine2", "f", "p", "y", "G", "v", "a", "d", "bind", "length", "l", "TypeError", "call", "done", "value", "return", "GeneratorFunction", "GeneratorFunctionPrototype", "getPrototypeOf", "setPrototypeOf", "__proto__", "displayName", "_regenerator", "w", "m", "defineProperty", "_regeneratorDefine", "_invoke", "enumerable", "configurable", "writable", "asyncGeneratorStep", "Promise", "resolve", "then", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "Office", "onReady", "info", "host", "HostType", "Excel", "document", "getElementById", "style", "display", "onclick", "run", "_run", "_callee2", "_t", "_context2", "_ref", "_callee", "context", "range", "_context", "workbook", "getSelectedRange", "load", "format", "fill", "color", "sync", "console", "log", "concat", "address", "_x", "error"], "sourceRoot": ""}