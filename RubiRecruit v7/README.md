# RubiRecruit v7 - Advanced Local File Processing Version

## Overview

RubiRecruit v7 is the most feature-complete version of the AI-powered recruitment Excel add-in. It provides comprehensive local file processing capabilities, advanced analytics, and sophisticated AI integration without requiring Microsoft 365 SSO authentication.

## 🚀 Key Features

### Advanced AI Integration
- **Claude 3.5 Sonnet Integration**: Latest Anthropic AI model for superior candidate evaluation
- **Dynamic Rubric Generation**: AI creates customized evaluation criteria from job descriptions
- **Comprehensive Candidate Analysis**: Detailed scoring across 8+ evaluation categories
- **Personalized Interview Questions**: AI-generated questions tailored to each candidate

### Local File Processing
- **PDF Document Support**: Direct upload and processing of CVs, cover letters, and job descriptions
- **Automatic Document Mapping**: Intelligent matching of documents to candidates
- **Batch Processing**: Efficient handling of multiple candidates simultaneously
- **No Cloud Dependencies**: All processing happens locally for maximum privacy

### Advanced Excel Integration
- **13 Specialized Worksheets**: Comprehensive data organization and analytics
- **Real-time Progress Tracking**: Live updates during processing
- **Detailed Analytics**: Company metrics, career progression analysis
- **Export-Ready Results**: Professional formatting for reporting

### Developer-Friendly Features
- **Built-in Diagnostics**: Comprehensive system health checks
- **Debug Panel**: Real-time logging and troubleshooting
- **Proxy Server**: CORS handling for API integration
- **Hot Reload**: Development server with automatic updates

## 📋 Prerequisites

- **Microsoft Excel**: Desktop or web version
- **Node.js**: Version 14 or higher
- **Claude API Key**: From Anthropic (https://console.anthropic.com/)
- **Modern Browser**: Chrome, Firefox, or Edge for development

## 🔧 Installation

### Quick Start
```bash
# Navigate to RubiRecruit v7 directory
cd "RubiRecruit v7"

# Install dependencies
npm install

# Start the proxy server (required for Claude API)
npm run proxy

# In another terminal, start the development server
npm run dev-server

# Or run both simultaneously
npm run dev
```

### Development Setup
```bash
# Install Office Add-in development tools
npm install -g office-addin-cli

# Generate development certificates (if needed)
npx office-addin-dev-certs install

# Start Excel with the add-in
npm start
```

## 🏗️ Architecture

### Service Layer
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Task Pane     │    │  Local File     │    │   PDF Service   │
│   Interface     │◄──►│   Service       │◄──►│   (PDF.js)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Excel Service  │    │  Claude Service │    │  Proxy Server   │
│  (Data Layer)   │    │   (AI Layer)    │    │  (CORS Handler) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Data Flow
```
PDF Upload → Text Extraction → AI Analysis → Excel Storage → Analytics
     │              │              │             │            │
File Service → PDF Service → Claude API → Excel Service → Dashboard
```

## 📊 Worksheet Structure

### Core Data Sheets
1. **Position Configuration**: Job description metadata and settings
2. **Document Mapping**: Candidate-to-document relationships
3. **Candidate Evaluations**: Detailed AI assessment results
4. **Company Metrics**: Career progression and experience data
5. **Processing Log**: Operation history and debugging information

### Analytics Sheets
6. **Dynamic Rubric Configuration**: AI-generated evaluation criteria
7. **Detailed Evaluations**: Category-wise scoring breakdown
8. **Combined Analysis**: Comprehensive candidate profiles
9. **Summary Dashboard**: High-level insights and rankings
10. **API Usage Tracking**: Cost monitoring and optimization

### Support Sheets
11. **PD Raw Text**: Extracted position description content
12. **Processed Candidates**: Batch processing status
13. **_Storage**: Internal configuration and state management

## 🔑 Configuration

### API Key Setup
1. Obtain Claude API key from Anthropic Console
2. Enter key in the add-in interface
3. Key is securely stored within Excel workbook
4. Validate connection with built-in diagnostics

### Proxy Server Configuration
```javascript
// proxy-server.js runs on localhost:3001
const PORT = process.env.PROXY_PORT || 3001;

// Handles CORS for Claude API requests
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'x-api-key']
}));
```

## 🎯 Usage Workflow

### 1. Initial Setup
```
Open Excel → Load Add-in → Configure API Key → Initialize Worksheets
```

### 2. Position Loading
```
Load Position PDF → Extract Text → Store in Excel → Ready for Rubric
```

### 3. Rubric Generation
```
Analyze Position → Generate Criteria → Create Weighted Rubric → Store Configuration
```

### 4. Document Processing
```
Upload CVs/Cover Letters → Map to Candidates → Validate Documents → Ready for Evaluation
```

### 5. Candidate Evaluation
```
Process Each Candidate → AI Analysis → Score Generation → Store Results → Update Analytics
```

## 🛠️ Development Features

### Built-in Diagnostics
```typescript
// Comprehensive system health check
async function runDiagnostics() {
  await checkApiKey();           // Validate Claude API access
  await testProxyServer();       // Verify CORS proxy
  await validateExcelSheets();   // Check worksheet structure
  await testClaudeConnectivity(); // Test AI service
}
```

### Debug Panel
- **Real-time Logging**: Live operation tracking
- **Error Monitoring**: Detailed error information
- **Performance Metrics**: Processing time analysis
- **API Usage**: Token consumption tracking

### Hot Reload Development
```bash
# Webpack dev server with live reload
npm run dev-server

# Watch mode for continuous building
npm run watch
```

## 🔍 Advanced Features

### Intelligent Document Mapping
```typescript
// Automatic candidate name extraction
private extractCandidateName(filename: string): string {
  // Remove common suffixes and clean formatting
  let name = filename.replace(/\.[^/.]+$/, '');
  const suffixes = ['_CV', '_Resume', '_CoverLetter'];
  // ... intelligent parsing logic
  return cleanedName;
}
```

### Retry Logic and Error Handling
```typescript
// Robust API call handling with exponential backoff
async function evaluateWithRetry(candidate: any, maxRetries: number = 3) {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await claudeService.evaluateCandidate(candidate);
    } catch (error) {
      if (error.includes('Rate limit')) {
        await delay(15000 * Math.pow(2, attempt - 1));
      }
    }
  }
}
```

### Performance Optimization
- **Batch Processing**: Sequential candidate evaluation with rate limiting
- **Memory Management**: Efficient PDF content storage and retrieval
- **Progress Tracking**: Real-time status updates and completion estimates
- **Error Recovery**: Automatic retry with intelligent backoff strategies

## 📈 Analytics and Reporting

### Candidate Metrics
- **Total Score**: Weighted evaluation across all categories
- **Category Breakdown**: Detailed scoring by competency area
- **Career Progression**: Experience timeline and advancement analysis
- **Skills Assessment**: Technical and soft skills evaluation

### Company Intelligence
- **Industry Experience**: Relevant sector background
- **Company Progression**: Career advancement patterns
- **Team Management**: Leadership experience and team size
- **Budget Responsibility**: Financial management experience

### Interview Preparation
- **Personalized Questions**: AI-generated based on candidate profile
- **Competency Focus**: Questions targeting specific skill gaps
- **Behavioral Scenarios**: Situation-based interview prompts
- **Technical Assessments**: Role-specific technical questions

## 🔒 Security and Privacy

### Data Protection
- **Local Processing**: All data remains within user's Excel file
- **No Cloud Storage**: Documents never leave user's environment
- **API Key Security**: Encrypted storage within Excel workbook
- **Audit Trail**: Complete operation logging for compliance

### Privacy Features
- **No Data Retention**: AI service doesn't store candidate information
- **Anonymization Options**: Remove identifying information if needed
- **Secure Transmission**: HTTPS for all API communications
- **Access Control**: Excel-level security and permissions

## 🚀 Performance Specifications

### Processing Capabilities
- **Document Size**: Up to 10MB per PDF file
- **Batch Size**: 50+ candidates per session
- **Processing Speed**: 2-3 minutes per candidate (including AI analysis)
- **Memory Usage**: Optimized for large document sets

### API Efficiency
- **Token Optimization**: Efficient prompt engineering
- **Rate Limiting**: Automatic throttling to respect API limits
- **Cost Tracking**: Real-time token usage monitoring
- **Error Recovery**: Minimal retry overhead

## 🔧 Troubleshooting

### Common Issues
1. **Proxy Server Not Running**: Ensure `npm run proxy` is active
2. **API Key Invalid**: Verify key format and permissions
3. **PDF Processing Fails**: Check file size and format
4. **Excel Sheets Missing**: Run setup diagnostics

### Debug Commands
```bash
# Check proxy server health
curl http://localhost:3001/health

# Validate Excel add-in manifest
npm run validate

# Run comprehensive diagnostics
# Use built-in diagnostic button in add-in
```

## 🔄 Updates and Maintenance

### Version Management
- **Semantic Versioning**: Clear version tracking
- **Backward Compatibility**: Maintains existing data structures
- **Migration Scripts**: Automatic data format updates
- **Feature Flags**: Gradual rollout of new capabilities

### Monitoring
- **Performance Metrics**: Processing time and success rates
- **Error Tracking**: Detailed failure analysis
- **Usage Analytics**: Feature adoption and optimization opportunities
- **API Monitoring**: Cost and rate limit tracking

## 🤝 Contributing

### Development Setup
1. Fork the repository
2. Create feature branch
3. Follow TypeScript and ESLint standards
4. Add comprehensive tests
5. Update documentation

### Code Standards
- **TypeScript**: Strict type checking enabled
- **ESLint**: Office Add-in recommended rules
- **Prettier**: Consistent code formatting
- **Documentation**: JSDoc comments for all public methods

## 📄 License

MIT License - See LICENSE file for details.

## 🆘 Support

For technical support:
1. Check built-in diagnostics panel
2. Review processing logs in Excel
3. Consult troubleshooting section
4. Submit issues with diagnostic output
