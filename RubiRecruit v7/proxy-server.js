const express = require('express');
const cors = require('cors');
const app = express();

// Configure CORS and JSON parsing
app.use(cors({
  origin: ['https://localhost:3000', 'http://localhost:3000'],
  credentials: true,
  methods: ['GET', 'POST', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'x-api-key', 'anthropic-version']
}));

app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Claude API proxy endpoint
app.post('/api/claude', async (req, res) => {
  console.log('[Proxy] Received request to Claude API');
  console.log('[Proxy] Request size:', JSON.stringify(req.body).length, 'bytes');
  
  const apiKey = req.headers['x-api-key'];
  
  if (!apiKey) {
    console.error('[Proxy] No API key provided');
    return res.status(400).json({ error: 'API key required' });
  }
  
  try {
    console.log('[Proxy] Forwarding to Claude API...');
    
    // Use dynamic import for node-fetch
    const fetch = (await import('node-fetch')).default;
    
    const response = await fetch('https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': apiKey,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(req.body)
    });
    
    console.log('[Proxy] Claude API response status:', response.status);
    
    const data = await response.json();
    
    if (!response.ok) {
      console.error('[Proxy] Claude API error:', data);
      return res.status(response.status).json(data);
    }
    
    console.log('[Proxy] Success, sending response');
    res.json(data);
    
  } catch (error) {
    console.error('[Proxy] Error calling Claude API:', error);
    res.status(500).json({ 
      error: 'Proxy error', 
      message: error.message,
      stack: error.stack 
    });
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

const PORT = 3003;
app.listen(PORT, () => {
  console.log(`🚀 Proxy server running on http://localhost:${PORT}`);
  console.log(`📝 Claude API endpoint: http://localhost:${PORT}/api/claude`);
  console.log(`✅ Health check: http://localhost:${PORT}/health`);
});