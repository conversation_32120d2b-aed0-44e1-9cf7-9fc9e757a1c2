# AI Services Integration

## Overview

The AI Services component integrates Anthropic's <PERSON> to provide intelligent candidate evaluation, dynamic rubric generation, and personalized interview question creation. This system transforms traditional recruitment processes through advanced natural language processing.

## Architecture

### Core Components (RubiRecruit v7)

1. **Claude Service** (`RubiRecruit v7/src/services/claudeService.ts`)
   - Primary AI integration layer
   - API communication and error handling
   - Response parsing and validation

2. **PDF Service** (`RubiRecruit v7/src/services/pdfService.ts`)
   - Document text extraction
   - PDF processing and storage
   - Content preparation for AI analysis

3. **Proxy Server** (`RubiRecruit v7/proxy-server.js`)
   - CORS handling for Claude API
   - Request forwarding and response processing
   - Development environment support

## Claude AI Integration

### Service Configuration
```typescript
class ClaudeService {
  private apiKey: string = '';
  private baseUrl: string = 'http://localhost:3001/api/claude'; // Proxy endpoint
  
  setApiKey(key: string) {
    this.apiKey = key;
  }
  
  private async makeRequest(payload: any): Promise<any> {
    const response = await fetch(this.baseUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': this.apiKey
      },
      body: JSON.stringify(payload)
    });
    
    return this.handleResponse(response);
  }
}
```

### Rubric Generation
```typescript
async generateRubric(positionText: string, positionName: string): Promise<any> {
  const prompt = `
    Analyze this position description and create a comprehensive evaluation rubric.
    
    Position: ${positionName}
    Description: ${positionText}
    
    Generate a structured rubric with:
    - 8 key evaluation categories
    - Weighted scoring criteria
    - Specific attributes for each category
    - Clear evaluation guidelines
  `;
  
  const payload = {
    model: 'claude-3-5-sonnet-20241022',
    max_tokens: 4000,
    messages: [{ role: 'user', content: prompt }]
  };
  
  const response = await this.makeRequest(payload);
  return this.parseRubricResponse(response);
}
```

### Candidate Evaluation
```typescript
async evaluateCandidateWithPDF(
  cvBase64: string,
  coverBase64: string | null,
  candidateName: string,
  rubric: any
): Promise<any> {
  
  const prompt = this.buildEvaluationPrompt(
    cvBase64, 
    coverBase64, 
    candidateName, 
    rubric
  );
  
  const payload = {
    model: 'claude-3-5-sonnet-20241022',
    max_tokens: 8000,
    messages: [{ role: 'user', content: prompt }]
  };
  
  const response = await this.makeRequest(payload);
  return this.parseEvaluationResponse(response, candidateName);
}
```

## PDF Processing Service

### Text Extraction
```typescript
class PDFService {
  async extractTextFromBase64(base64Content: string): Promise<string> {
    try {
      // Convert base64 to Uint8Array
      const pdfData = this.base64ToUint8Array(base64Content);
      
      // Load PDF document
      const pdf = await pdfjsLib.getDocument({ data: pdfData }).promise;
      
      let fullText = '';
      
      // Extract text from each page
      for (let i = 1; i <= pdf.numPages; i++) {
        const page = await pdf.getPage(i);
        const textContent = await page.getTextContent();
        const pageText = textContent.items
          .map((item: any) => item.str)
          .join(' ');
        fullText += pageText + '\n';
      }
      
      return this.cleanExtractedText(fullText);
    } catch (error) {
      throw new Error(`PDF processing failed: ${error.message}`);
    }
  }
}
```

### Content Storage
```typescript
async storePositionPDF(base64Content: string): Promise<void> {
  // Store in Excel for persistence
  await excelService.setStoredValue('POSITION_PDF_BASE64', base64Content);
  
  // Also store in session for quick access
  if (typeof(Storage) !== "undefined") {
    sessionStorage.setItem('currentPDFBase64', base64Content);
  }
}

async getStoredPDFContent(): Promise<string | null> {
  // Try session storage first
  if (typeof(Storage) !== "undefined") {
    const sessionPDF = sessionStorage.getItem('currentPDFBase64');
    if (sessionPDF) return sessionPDF;
  }
  
  // Fallback to Excel storage
  return await excelService.getStoredValue('POSITION_PDF_BASE64');
}
```

## Proxy Server Configuration

### CORS Handling
```javascript
// proxy-server.js
const express = require('express');
const cors = require('cors');
const fetch = require('node-fetch');

const app = express();
const PORT = 3001;

// Enable CORS for all origins in development
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'x-api-key']
}));

app.use(express.json({ limit: '50mb' }));

// Claude API proxy endpoint
app.post('/api/claude', async (req, res) => {
  try {
    const apiKey = req.headers['x-api-key'];
    
    const response = await fetch('https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': apiKey,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(req.body)
    });
    
    const data = await response.json();
    res.json(data);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});
```

## AI Response Processing

### Rubric Response Parsing
```typescript
private parseRubricResponse(response: any): any {
  try {
    const content = response.content[0].text;
    
    // Extract JSON from Claude's response
    const jsonMatch = content.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('No valid JSON found in rubric response');
    }
    
    const rubric = JSON.parse(jsonMatch[0]);
    
    // Validate rubric structure
    this.validateRubricStructure(rubric);
    
    return rubric;
  } catch (error) {
    throw new Error(`Failed to parse rubric: ${error.message}`);
  }
}
```

### Evaluation Response Parsing
```typescript
private parseEvaluationResponse(response: any, candidateName: string): any {
  try {
    const content = response.content[0].text;
    
    // Extract JSON evaluation
    const jsonMatch = content.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('No valid JSON found in evaluation response');
    }
    
    const evaluation = JSON.parse(jsonMatch[0]);
    
    // Add metadata
    evaluation.candidate_name = candidateName;
    evaluation.evaluation_date = new Date().toISOString();
    evaluation.actualTokensUsed = {
      input: response.usage?.input_tokens || 0,
      output: response.usage?.output_tokens || 0
    };
    
    // Validate evaluation structure
    this.validateEvaluationStructure(evaluation);
    
    return evaluation;
  } catch (error) {
    throw new Error(`Failed to parse evaluation: ${error.message}`);
  }
}
```

## Error Handling and Retry Logic

### Rate Limiting
```typescript
private async handleRateLimit(error: any, retryCount: number): Promise<void> {
  if (error.message?.includes('Rate limit')) {
    const waitTime = Math.min(70000, 15000 * Math.pow(2, retryCount));
    console.log(`Rate limit hit. Waiting ${waitTime/1000}s...`);
    await this.delay(waitTime);
  }
}
```

### Response Validation
```typescript
private validateEvaluationStructure(evaluation: any): void {
  const requiredFields = [
    'total_score',
    'categories',
    'recommendation',
    'strengths',
    'development_areas',
    'interview_questions',
    'metrics'
  ];
  
  for (const field of requiredFields) {
    if (!(field in evaluation)) {
      throw new Error(`Missing required field: ${field}`);
    }
  }
  
  // Validate categories structure
  if (!evaluation.categories || Object.keys(evaluation.categories).length === 0) {
    throw new Error('Invalid categories structure');
  }
}
```

## Prompt Engineering

### Rubric Generation Prompt
```typescript
private buildRubricPrompt(positionText: string, positionName: string): string {
  return `
    Create a comprehensive evaluation rubric for: ${positionName}
    
    Position Description:
    ${positionText}
    
    Requirements:
    1. Generate exactly 8 evaluation categories
    2. Each category should have 3-5 specific attributes
    3. Include weights for categories and attributes
    4. Provide clear scoring guidelines (1-5 scale)
    5. Focus on role-specific competencies
    
    Return as valid JSON with this structure:
    {
      "category_name": {
        "weight": 0.15,
        "description": "Category description",
        "attributes": [
          {
            "name": "Attribute name",
            "weight": 0.3,
            "description": "What to evaluate"
          }
        ]
      }
    }
  `;
}
```

### Evaluation Prompt
```typescript
private buildEvaluationPrompt(
  cvBase64: string,
  coverBase64: string | null,
  candidateName: string,
  rubric: any
): string {
  return `
    Evaluate candidate: ${candidateName}
    
    Documents provided:
    - CV: ${cvBase64 ? 'Yes' : 'No'}
    - Cover Letter: ${coverBase64 ? 'Yes' : 'No'}
    
    Evaluation Rubric:
    ${JSON.stringify(rubric, null, 2)}
    
    Instructions:
    1. Analyze all provided documents
    2. Score each attribute (1-5 scale)
    3. Calculate weighted category scores
    4. Provide evidence for each score
    5. Generate 5 personalized interview questions
    6. Extract career metrics and achievements
    
    Return comprehensive evaluation as JSON...
  `;
}
```

## Performance Optimization

### Token Management
- **Input optimization**: Efficient prompt construction
- **Output control**: Appropriate max_tokens settings
- **Usage tracking**: Monitor API consumption

### Caching Strategy
- **PDF content**: Store processed text to avoid re-extraction
- **Rubrics**: Cache generated rubrics for reuse
- **Responses**: Store successful evaluations

### Batch Processing
- **Queue management**: Process candidates sequentially
- **Rate limiting**: Respect API limits with delays
- **Error recovery**: Retry failed evaluations with backoff

## Integration with Excel Services

### Data Flow
```
PDF Upload → Text Extraction → AI Processing → Excel Storage
     ↓              ↓              ↓            ↓
File Service → PDF Service → Claude Service → Excel Service
```

### Result Storage
- **Evaluations**: Detailed candidate assessments
- **Metrics**: Career progression data
- **Questions**: Personalized interview content
- **Analytics**: Summary statistics and insights

## Future Enhancements

- **Multi-model support**: Integration with other AI providers
- **Custom prompts**: User-configurable evaluation criteria
- **Batch optimization**: Parallel processing capabilities
- **Advanced analytics**: Trend analysis and benchmarking
