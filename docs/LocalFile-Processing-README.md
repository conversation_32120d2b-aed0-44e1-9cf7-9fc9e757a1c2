# Local File Processing System

## Overview

The Local File Processing System enables RubiRecruit to handle document uploads, PDF processing, and candidate-document mapping without requiring cloud storage integration. This component is essential for the standalone version (RubiRecruit v7) and provides comprehensive file management capabilities.

## Architecture

### Core Components (RubiRecruit v7)

1. **Local File Service** (`RubiRecruit v7/src/services/localFileService.ts`)
   - File selection and upload handling
   - Document-to-candidate mapping
   - In-memory file storage and retrieval
   - File type validation and processing

2. **PDF Service** (`RubiRecruit v7/src/services/pdfService.ts`)
   - PDF text extraction using PDF.js
   - Base64 encoding and decoding
   - Content cleaning and formatting
   - Storage integration

3. **File Management**
   - Browser-based file selection
   - Memory-efficient storage
   - Automatic document mapping
   - Duplicate detection and handling

## Local File Service Implementation

### File Selection Interface
```typescript
class LocalFileService {
  private storedFiles: Map<string, FileData> = new Map();
  private documentMapping: Map<string, DocumentPair> = new Map();
  
  async selectPositionFile(): Promise<FileData> {
    return new Promise((resolve, reject) => {
      const input = document.getElementById('positionFilePicker') as HTMLInputElement;
      
      input.onchange = async (event) => {
        const file = (event.target as HTMLInputElement).files?.[0];
        if (!file) {
          reject(new Error('No file selected'));
          return;
        }
        
        try {
          const fileData = await this.processFile(file, 'position');
          this.storedFiles.set(fileData.id, fileData);
          resolve(fileData);
        } catch (error) {
          reject(error);
        }
      };
      
      input.click();
    });
  }
  
  async selectCVFiles(): Promise<FileData[]> {
    return new Promise((resolve, reject) => {
      const input = document.getElementById('cvFilesPicker') as HTMLInputElement;
      
      input.onchange = async (event) => {
        const files = Array.from((event.target as HTMLInputElement).files || []);
        if (files.length === 0) {
          reject(new Error('No files selected'));
          return;
        }
        
        try {
          const processedFiles: FileData[] = [];
          
          for (const file of files) {
            const fileData = await this.processFile(file, 'cv');
            this.storedFiles.set(fileData.id, fileData);
            processedFiles.push(fileData);
          }
          
          // Perform automatic document mapping
          this.performDocumentMapping(processedFiles);
          
          resolve(processedFiles);
        } catch (error) {
          reject(error);
        }
      };
      
      input.click();
    });
  }
}
```

### File Processing
```typescript
private async processFile(file: File, type: 'position' | 'cv'): Promise<FileData> {
  // Validate file type
  if (file.type !== 'application/pdf') {
    throw new Error('Only PDF files are supported');
  }
  
  // Validate file size (max 10MB)
  const maxSize = 10 * 1024 * 1024;
  if (file.size > maxSize) {
    throw new Error('File size exceeds 10MB limit');
  }
  
  // Convert to base64
  const base64Content = await this.fileToBase64(file);
  
  // Generate unique ID
  const fileId = this.generateFileId(file.name, file.size);
  
  const fileData: FileData = {
    id: fileId,
    name: file.name,
    type: type,
    size: file.size,
    content: base64Content,
    uploadDate: new Date(),
    processed: false
  };
  
  console.log(`Processed ${type} file: ${file.name} (${file.size} bytes)`);
  
  return fileData;
}

private fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = () => {
      const result = reader.result as string;
      // Remove data URL prefix to get pure base64
      const base64 = result.split(',')[1];
      resolve(base64);
    };
    
    reader.onerror = () => {
      reject(new Error('Failed to read file'));
    };
    
    reader.readAsDataURL(file);
  });
}
```

### Document Mapping Algorithm
```typescript
private performDocumentMapping(files: FileData[]): void {
  console.log(`Starting document mapping for ${files.length} files`);
  
  // Clear existing mapping
  this.documentMapping.clear();
  
  // Group files by candidate name
  const candidateGroups = new Map<string, FileData[]>();
  
  for (const file of files) {
    const candidateName = this.extractCandidateName(file.name);
    
    if (!candidateGroups.has(candidateName)) {
      candidateGroups.set(candidateName, []);
    }
    candidateGroups.get(candidateName)!.push(file);
  }
  
  // Create document pairs
  for (const [candidateName, candidateFiles] of candidateGroups) {
    const documentPair = this.createDocumentPair(candidateFiles);
    this.documentMapping.set(candidateName, documentPair);
    
    console.log(`Mapped candidate: ${candidateName}`, {
      cv: documentPair.cv?.name,
      coverLetter: documentPair.coverLetter?.name
    });
  }
  
  console.log(`Document mapping complete: ${this.documentMapping.size} candidates mapped`);
}

private extractCandidateName(filename: string): string {
  // Remove file extension
  let name = filename.replace(/\.[^/.]+$/, '');
  
  // Remove common suffixes
  const suffixes = [
    '_CV', '_cv', '_Resume', '_resume',
    '_CoverLetter', '_cover_letter', '_Cover_Letter',
    ' CV', ' cv', ' Resume', ' resume',
    ' Cover Letter', ' cover letter'
  ];
  
  for (const suffix of suffixes) {
    if (name.endsWith(suffix)) {
      name = name.slice(0, -suffix.length);
      break;
    }
  }
  
  // Clean up common separators
  name = name.replace(/[-_]/g, ' ').trim();
  
  return name;
}

private createDocumentPair(files: FileData[]): DocumentPair {
  let cv: FileData | null = null;
  let coverLetter: FileData | null = null;
  
  for (const file of files) {
    const filename = file.name.toLowerCase();
    
    if (this.isCoverLetter(filename)) {
      coverLetter = file;
    } else {
      // Default to CV if not identified as cover letter
      cv = file;
    }
  }
  
  return { cv, coverLetter };
}

private isCoverLetter(filename: string): boolean {
  const coverLetterKeywords = [
    'cover', 'letter', 'coverletter', 'cover_letter',
    'motivation', 'application', 'intro'
  ];
  
  return coverLetterKeywords.some(keyword => 
    filename.includes(keyword)
  );
}
```

## PDF Processing Integration

### Text Extraction Service
```typescript
class PDFService {
  async extractTextFromBase64(base64Content: string): Promise<string> {
    try {
      console.log('Starting PDF text extraction...');
      
      // Convert base64 to Uint8Array
      const pdfData = this.base64ToUint8Array(base64Content);
      
      // Configure PDF.js worker
      pdfjsLib.GlobalWorkerOptions.workerSrc = 
        'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
      
      // Load PDF document
      const loadingTask = pdfjsLib.getDocument({ data: pdfData });
      const pdf = await loadingTask.promise;
      
      console.log(`PDF loaded: ${pdf.numPages} pages`);
      
      let fullText = '';
      
      // Extract text from each page
      for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
        const page = await pdf.getPage(pageNum);
        const textContent = await page.getTextContent();
        
        // Combine text items with proper spacing
        const pageText = textContent.items
          .map((item: any) => item.str)
          .join(' ');
        
        fullText += pageText + '\n\n';
        
        console.log(`Extracted text from page ${pageNum}: ${pageText.length} characters`);
      }
      
      // Clean and format extracted text
      const cleanedText = this.cleanExtractedText(fullText);
      
      console.log(`PDF extraction complete: ${cleanedText.length} characters total`);
      
      return cleanedText;
    } catch (error) {
      console.error('PDF extraction failed:', error);
      throw new Error(`PDF processing failed: ${error.message}`);
    }
  }
  
  private base64ToUint8Array(base64: string): Uint8Array {
    const binaryString = atob(base64);
    const bytes = new Uint8Array(binaryString.length);
    
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    
    return bytes;
  }
  
  private cleanExtractedText(text: string): string {
    return text
      // Remove excessive whitespace
      .replace(/\s+/g, ' ')
      // Remove page breaks and form feeds
      .replace(/[\f\r]/g, '')
      // Normalize line breaks
      .replace(/\n\s*\n/g, '\n\n')
      // Trim whitespace
      .trim();
  }
}
```

### Content Storage and Retrieval
```typescript
async storePositionPDF(base64Content: string): Promise<void> {
  try {
    // Store in Excel for persistence
    await excelService.setStoredValue('POSITION_PDF_BASE64', base64Content);
    
    // Also store in session storage for quick access
    if (typeof(Storage) !== "undefined") {
      sessionStorage.setItem('currentPDFBase64', base64Content);
    }
    
    console.log('Position PDF stored successfully');
  } catch (error) {
    console.error('Failed to store position PDF:', error);
    throw error;
  }
}

async getStoredPDFContent(): Promise<string | null> {
  try {
    // Try session storage first (faster)
    if (typeof(Storage) !== "undefined") {
      const sessionPDF = sessionStorage.getItem('currentPDFBase64');
      if (sessionPDF) {
        console.log('Retrieved PDF from session storage');
        return sessionPDF;
      }
    }
    
    // Fallback to Excel storage
    const excelPDF = await excelService.getStoredValue('POSITION_PDF_BASE64');
    if (excelPDF) {
      console.log('Retrieved PDF from Excel storage');
      return excelPDF;
    }
    
    return null;
  } catch (error) {
    console.error('Failed to retrieve PDF content:', error);
    return null;
  }
}
```

## File Management Operations

### Storage and Retrieval
```typescript
getFileByName(filename: string): FileData | null {
  for (const [id, file] of this.storedFiles) {
    if (file.name === filename) {
      return file;
    }
  }
  return null;
}

getFileById(id: string): FileData | null {
  return this.storedFiles.get(id) || null;
}

getStoredFilesCount(): { positions: number; cvs: number } {
  let positions = 0;
  let cvs = 0;
  
  for (const file of this.storedFiles.values()) {
    if (file.type === 'position') {
      positions++;
    } else if (file.type === 'cv') {
      cvs++;
    }
  }
  
  return { positions, cvs };
}

getDocumentMapping(): Map<string, DocumentPair> {
  return new Map(this.documentMapping);
}
```

### File Validation
```typescript
private validateFile(file: File): void {
  // Check file type
  if (file.type !== 'application/pdf') {
    throw new Error(`Unsupported file type: ${file.type}. Only PDF files are allowed.`);
  }
  
  // Check file size
  const maxSize = 10 * 1024 * 1024; // 10MB
  if (file.size > maxSize) {
    throw new Error(`File too large: ${Math.round(file.size / 1024 / 1024)}MB. Maximum size is 10MB.`);
  }
  
  // Check filename
  if (file.name.length > 255) {
    throw new Error('Filename too long. Maximum 255 characters allowed.');
  }
  
  // Check for valid characters
  const invalidChars = /[<>:"/\\|?*]/;
  if (invalidChars.test(file.name)) {
    throw new Error('Filename contains invalid characters.');
  }
}
```

## Memory Management

### Efficient Storage
```typescript
private generateFileId(filename: string, size: number): string {
  // Create unique ID based on filename and size
  const timestamp = Date.now();
  const hash = this.simpleHash(filename + size + timestamp);
  return `file_${hash}`;
}

private simpleHash(str: string): string {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash).toString(36);
}

clearStoredFiles(): void {
  this.storedFiles.clear();
  this.documentMapping.clear();
  
  // Clear session storage
  if (typeof(Storage) !== "undefined") {
    sessionStorage.removeItem('currentPDFBase64');
  }
  
  console.log('All stored files cleared');
}
```

### Performance Optimization
```typescript
async processFilesInBatches(files: File[], batchSize: number = 5): Promise<FileData[]> {
  const results: FileData[] = [];
  
  for (let i = 0; i < files.length; i += batchSize) {
    const batch = files.slice(i, i + batchSize);
    
    const batchPromises = batch.map(file => this.processFile(file, 'cv'));
    const batchResults = await Promise.all(batchPromises);
    
    results.push(...batchResults);
    
    // Small delay between batches to prevent UI blocking
    if (i + batchSize < files.length) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }
  
  return results;
}
```

## Error Handling

### File Processing Errors
```typescript
private handleFileError(error: Error, filename: string): Error {
  console.error(`File processing error for ${filename}:`, error);
  
  if (error.message.includes('PDF')) {
    return new Error(`PDF processing failed for ${filename}: ${error.message}`);
  } else if (error.message.includes('size')) {
    return new Error(`File size error for ${filename}: ${error.message}`);
  } else {
    return new Error(`Failed to process ${filename}: ${error.message}`);
  }
}
```

### Recovery Strategies
```typescript
async retryFileProcessing(file: File, maxRetries: number = 3): Promise<FileData> {
  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`Processing attempt ${attempt} for ${file.name}`);
      return await this.processFile(file, 'cv');
    } catch (error) {
      lastError = error as Error;
      console.warn(`Attempt ${attempt} failed:`, error.message);
      
      if (attempt < maxRetries) {
        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
      }
    }
  }
  
  throw new Error(`Failed to process ${file.name} after ${maxRetries} attempts: ${lastError.message}`);
}
```

## Integration Points

### Service Layer Communication
- **Task Pane**: File selection UI integration
- **PDF Service**: Text extraction coordination
- **Excel Service**: Metadata and mapping storage
- **AI Services**: Document content provision

### Data Flow
```
File Selection → Validation → Processing → Storage → Mapping
      ↓              ↓           ↓          ↓         ↓
   Browser API → File API → PDF.js → Memory → Excel Storage
```

## Future Enhancements

- **Drag-and-drop**: Enhanced file upload experience
- **Progress tracking**: Real-time processing feedback
- **File compression**: Optimize storage efficiency
- **Batch operations**: Improved performance for large datasets
- **File preview**: Quick document content viewing
