# SSO Authentication System

## Overview

The SSO (Single Sign-On) authentication system enables seamless integration with Microsoft 365 services, allowing users to authenticate once and access both the Excel add-in and Microsoft Graph APIs without additional login prompts.

## Architecture

### Components

1. **SSO Helper** (`src/helpers/sso-helper.ts`)
   - Primary authentication orchestrator
   - Handles Office runtime authentication
   - Manages token acquisition and validation
   - Implements fallback authentication strategies

2. **Middle Tier Authentication** (`src/middle-tier/ssoauth-helper.ts`)
   - Server-side JWT validation
   - On-behalf-of token exchange
   - Microsoft Graph token management
   - Development mode bypasses

3. **Fallback Authentication Dialog** (`src/helpers/fallbackauthdialog.ts`)
   - MSAL.js browser-based authentication
   - Popup dialog for interactive login
   - Handles authentication failures and retries
   - Manages token storage and retrieval

## Authentication Flow

### Primary SSO Flow
```
1. Office.js → OfficeRuntime.auth.getAccessToken()
2. Token validation → Middle tier JWT verification
3. On-behalf-of exchange → Microsoft Graph token
4. API calls → Authenticated Graph requests
```

### Fallback Flow (When SSO Fails)
```
1. SSO failure detected → Error handling
2. Dialog popup → MSAL.js authentication
3. Interactive login → User credentials
4. Token acquisition → Access token retrieval
5. API calls → Authenticated requests
```

## Key Files and Functions

### `src/helpers/sso-helper.ts`
- **`getUserData(callback)`**: Main entry point for authentication
- **`handleAADErrors()`**: Processes Azure AD authentication errors
- **Token retry logic**: Handles expired tokens and re-authentication

### `src/middle-tier/ssoauth-helper.ts`
- **`getAccessToken(authorization)`**: Exchanges Office token for Graph token
- **`validateJwt(req, res, next)`**: Middleware for JWT validation
- **Development mode**: Bypasses authentication for local development

### `src/helpers/fallbackauthdialog.ts`
- **`dialogFallback(callback)`**: Initiates fallback authentication
- **MSAL configuration**: Browser-based authentication setup
- **Token management**: Silent token acquisition and storage

## Configuration

### Manifest Configuration (`manifest.xml`)
```xml
<WebApplicationInfo>
  <Id>ce890f2a-55f4-4799-bf74-d93502dd2034</Id>
  <Resource>api://localhost:3000/ce890f2a-55f4-4799-bf74-d93502dd2034</Resource>
  <Scopes>
    <Scope>Files.Read.All</Scope>
    <Scope>profile</Scope>
    <Scope>openid</Scope>
  </Scopes>
</WebApplicationInfo>
```

### Environment Variables
- **`CLIENT_ID`**: Azure AD application ID
- **`CLIENT_SECRET`**: Azure AD application secret
- **`SCOPE`**: Microsoft Graph API scopes
- **`NODE_ENV`**: Environment mode (development/production)

## Development Mode

For development and testing, the system includes bypasses:

```typescript
// Development mode bypass in ssoauth-helper.ts
if (process.env.NODE_ENV === "development") {
  console.log("🔧 Development mode: Bypassing authentication");
  return {
    access_token: "dev-access-token",
    token_type: "Bearer",
    expires_in: 3600,
    scope: "User.Read"
  };
}
```

## Error Handling

### Common Authentication Errors
1. **AADSTS500133**: Token expired during exchange
2. **Missing access_as_user scope**: Insufficient permissions
3. **Network connectivity**: API endpoint unavailable
4. **Invalid client credentials**: Misconfigured Azure AD app

### Error Recovery Strategies
- **Automatic retry**: For transient token expiration
- **Fallback authentication**: When SSO is unavailable
- **User notification**: Clear error messages and guidance
- **Graceful degradation**: Limited functionality without authentication

## Security Considerations

### Token Management
- **Short-lived tokens**: Automatic refresh and renewal
- **Secure storage**: Browser localStorage with encryption
- **Scope limitation**: Minimal required permissions
- **Token validation**: Server-side JWT verification

### Best Practices
- **HTTPS enforcement**: All authentication endpoints use SSL
- **CORS configuration**: Restricted cross-origin requests
- **Client secret protection**: Server-side only storage
- **Audit logging**: Authentication events tracking

## Troubleshooting

### Common Issues
1. **SSO not working**: Check Azure AD app registration
2. **Token validation fails**: Verify client ID and secret
3. **Scope errors**: Ensure proper Graph API permissions
4. **Popup blocked**: Browser popup blocker interference

### Debug Steps
1. Check browser console for authentication errors
2. Verify manifest.xml configuration
3. Test with development mode bypasses
4. Review Azure AD app registration settings
5. Validate environment variables

## Integration with Microsoft Graph

The authentication system enables access to:
- **User profile data**: Basic user information
- **SharePoint files**: Document storage and retrieval
- **OneDrive access**: Personal file management
- **Teams integration**: Collaboration features

## Future Enhancements

- **Multi-tenant support**: Cross-organization authentication
- **Certificate-based authentication**: Enhanced security
- **Conditional access**: Policy-based authentication
- **Token caching optimization**: Improved performance
