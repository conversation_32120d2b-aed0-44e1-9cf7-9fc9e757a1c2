# Middle Tier Services

## Overview

The Middle Tier serves as the backend infrastructure for RubiRecruit, providing secure API endpoints, authentication middleware, and integration with Microsoft Graph services. It acts as a bridge between the Excel add-in frontend and external services.

## Architecture

### Core Components

1. **Express Server** (`src/middle-tier/app.ts`)
   - Main application server
   - Route handling and middleware
   - Static file serving
   - Error handling and logging

2. **Authentication Helper** (`src/middle-tier/ssoauth-helper.ts`)
   - JWT token validation
   - On-behalf-of token exchange
   - Development mode bypasses

3. **Microsoft Graph Helper** (`src/middle-tier/msgraph-helper.ts`)
   - Graph API integration
   - User data retrieval
   - SharePoint connectivity

## Server Configuration

### Express Application Setup
```typescript
const app = express();
const port: number | string = process.env.API_PORT || "3000";

// Middleware configuration
app.use(logger("dev"));
app.use(express.json());
app.use(express.urlencoded({ extended: false }));
app.use(cookieParser());
```

### Development vs Production Mode

#### Development Mode
```typescript
if (process.env.NODE_ENV !== "production") {
  // Disable caching for development
  app.use(express.static(path.join(process.cwd(), "dist"), { etag: false }));
  
  // Add cache-control headers
  app.use(function (req, res, next) {
    res.header("Cache-Control", "private, no-cache, no-store, must-revalidate");
    res.header("Expires", "-1");
    res.header("Pragma", "no-cache");
    next();
  });
}
```

#### Production Mode
```typescript
else {
  // Enable static file caching in production
  app.use(express.static(path.join(process.cwd(), "dist")));
}
```

## API Endpoints

### Health Check Endpoints
```typescript
// Root endpoint
app.get("/", function (req, res) {
  res.send("RubiRecruit Development Server Running! 🚀");
});

// Health check
app.get("/ping", function (req: any, res: any) {
  res.send({ 
    status: "OK", 
    message: "Server is running", 
    timestamp: new Date().toISOString() 
  });
});
```

### Authenticated Endpoints
```typescript
// User data endpoint with JWT validation
app.get("/getuserdata", validateJwt, getUserData);
```

### Static File Serving
```typescript
// Task pane HTML
app.get("/taskpane.html", async (req: any, res: any) => {
  return res.sendFile(path.join(process.cwd(), "dist", "taskpane.html"));
});

// Fallback authentication dialog
app.get("/fallbackauthdialog.html", async (req: any, res: any) => {
  return res.sendFile(path.join(process.cwd(), "dist", "fallbackauthdialog.html"));
});
```

## Authentication Middleware

### JWT Validation
```typescript
export function validateJwt(req, res, next): void {
  // Development mode bypass
  if (process.env.NODE_ENV === "development") {
    console.log("🔧 Development mode: Bypassing JWT validation");
    return next();
  }

  const authHeader = req.headers.authorization;
  if (authHeader) {
    const token = authHeader.split(" ")[1];
    
    const validationOptions = {
      audience: process.env.CLIENT_ID,
    };

    jwt.verify(token, getSigningKeys, validationOptions, (err) => {
      if (err) {
        console.log(err);
        return res.sendStatus(403);
      }
      next();
    });
  }
}
```

### Token Exchange
```typescript
export async function getAccessToken(authorization: string): Promise<any> {
  // Development mode mock
  if (process.env.NODE_ENV === "development") {
    return {
      access_token: "dev-access-token",
      token_type: "Bearer",
      expires_in: 3600,
      scope: "User.Read"
    };
  }

  // Production token exchange logic
  const formParams = {
    client_id: process.env.CLIENT_ID,
    client_secret: process.env.CLIENT_SECRET,
    grant_type: "urn:ietf:params:oauth:grant-type:jwt-bearer",
    assertion: assertion,
    requested_token_use: "on_behalf_of",
    scope: [scopeName].join(" "),
  };

  // Exchange token with Microsoft Graph
  const tokenResponse = await fetch(`${stsDomain}/${tenant}/${tokenURLSegment}`, {
    method: "POST",
    body: form(formParams),
    headers: {
      Accept: "application/json",
      "Content-Type": "application/x-www-form-urlencoded",
    },
  });
}
```

## Microsoft Graph Integration

### User Data Retrieval
```typescript
export async function getUserData(req: any, res: any, next: any) {
  // Development mode mock data
  if (process.env.NODE_ENV === "development") {
    const mockUserData = {
      "@odata.context": "https://graph.microsoft.com/v1.0/$metadata#users/$entity",
      "id": "dev-user-id-12345",
      "displayName": "Development User",
      "mail": "<EMAIL>",
      // ... additional mock properties
    };
    return res.send(mockUserData);
  }

  // Production Graph API call
  const authorization: string = req.get("Authorization");
  await getAccessToken(authorization)
    .then(async (graphTokenResponse) => {
      const graphToken: string = graphTokenResponse.access_token;
      const graphData = await getGraphData(graphToken, "/me", "");
      res.send(graphData);
    });
}
```

### Graph API Communication
```typescript
export async function getGraphData(accessToken: string, apiUrl: string, queryParams?: string): Promise<any> {
  return new Promise<any>((resolve, reject) => {
    const options: https.RequestOptions = {
      host: "graph.microsoft.com",
      path: "/v1.0" + apiUrl + queryParams,
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
        Authorization: "Bearer " + accessToken,
      },
    };

    https.get(options, (response) => {
      let body = "";
      response.on("data", (d) => { body += d; });
      response.on("end", () => {
        if (response.statusCode === 200) {
          resolve(JSON.parse(body));
        } else {
          // Handle error responses
          const error = new Error();
          error.code = response.statusCode;
          error.message = response.statusMessage;
          resolve(error);
        }
      });
    }).on("error", reject);
  });
}
```

## Server Startup

### Development Server
```typescript
if (process.env.NODE_ENV === "development") {
  console.log("🔧 Development mode: Starting HTTP server");
  http.createServer(app).listen(port, () => {
    console.log(`🚀 HTTP Server running on http://localhost:${port}`);
    console.log(`📝 No certificates required for development`);
    console.log(`🌐 Test endpoints:`);
    console.log(`   - http://localhost:${port}/ (root)`);
    console.log(`   - http://localhost:${port}/ping (health check)`);
  });
}
```

### Production Server
```typescript
else {
  // Production mode: Use HTTPS with certificates
  getHttpsServerOptions().then((options) => {
    https
      .createServer(options, app)
      .listen(port, () => 
        console.log(`Server running on ${port} in ${process.env.NODE_ENV} mode`)
      );
  });
}
```

## Error Handling

### Global Error Handler
```typescript
app.use(function (err: any, req: any, res: any) {
  // Set locals, only providing error in development
  res.locals.message = err.message;
  res.locals.error = req.app.get("env") === "development" ? err : {};

  // Send error response
  res.status(err.status || 500);
  res.send({ error: err.message, status: err.status });
});
```

### 404 Handler
```typescript
app.use(function (req: any, res: any, next: any) {
  next(createError(404));
});
```

## Security Features

### CORS Configuration
- **Development**: Permissive CORS for local testing
- **Production**: Restricted CORS for security

### Headers Security
```typescript
// Cache control headers
res.header("Cache-Control", "private, no-cache, no-store, must-revalidate");
res.header("Expires", "-1");
res.header("Pragma", "no-cache");
```

### Environment Variables
- **`CLIENT_ID`**: Azure AD application identifier
- **`CLIENT_SECRET`**: Azure AD application secret
- **`API_PORT`**: Server port configuration
- **`NODE_ENV`**: Environment mode setting

## Logging and Monitoring

### Request Logging
```typescript
app.use(logger("dev")); // Morgan logging middleware
```

### Debug Output
- **Development**: Verbose console logging
- **Production**: Structured logging for monitoring
- **Error tracking**: Detailed error information

## Integration Points

### Frontend Communication
- **Task Pane**: Serves HTML and JavaScript files
- **Authentication**: Handles SSO token validation
- **API Calls**: Processes authenticated requests

### External Services
- **Microsoft Graph**: User and file data access
- **Azure AD**: Authentication and authorization
- **SharePoint**: Document storage integration

## Deployment Considerations

### Development Deployment
- **HTTP server**: No SSL certificates required
- **Mock data**: Simulated authentication and Graph responses
- **Hot reload**: Automatic restart on file changes

### Production Deployment
- **HTTPS server**: SSL certificates from office-addin-dev-certs
- **Real authentication**: Full Azure AD integration
- **Static file optimization**: Compressed and cached assets

## Future Enhancements

- **Rate limiting**: API call throttling
- **Caching layer**: Redis integration for performance
- **Health monitoring**: Advanced diagnostics and metrics
- **Load balancing**: Multi-instance deployment support
