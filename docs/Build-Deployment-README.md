# Build and Deployment Guide

## Overview

This guide covers the build process, deployment strategies, and configuration management for all versions of RubiRecruit. The project uses modern web development tools including Webpack, TypeScript, and Node.js to create production-ready Office Add-ins.

## Build System Architecture

### Webpack Configuration

The project uses a dual-configuration Webpack setup to handle both client-side and server-side code:

#### Client-Side Configuration
```javascript
// webpack.config.js - Client configuration
{
  devtool: "source-map",
  entry: {
    polyfill: ["core-js/stable", "regenerator-runtime/runtime"],
    taskpane: ["./src/taskpane/taskpane.ts", "./src/taskpane/taskpane.html"],
    commands: "./src/commands/commands.ts",
    fallbackauthdialog: "./src/helpers/fallbackauthdialog.ts",
  },
  resolve: {
    extensions: [".ts", ".html", ".js"],
    fallback: {
      buffer: require.resolve("buffer/"),
      http: require.resolve("stream-http"),
      https: require.resolve("https-browserify"),
      url: require.resolve("url/"),
    },
  },
  // ... module rules and plugins
}
```

#### Server-Side Configuration
```javascript
// webpack.config.js - Server configuration
{
  devtool: "source-map",
  target: "node",
  entry: {
    middletier: "./src/middle-tier/app.ts",
  },
  externals: [nodeExternals()],
  // ... additional server-specific configuration
}
```

### TypeScript Configuration

#### Main TypeScript Config (`tsconfig.json`)
```json
{
  "compilerOptions": {
    "allowJs": true,
    "baseUrl": ".",
    "esModuleInterop": true,
    "experimentalDecorators": true,
    "jsx": "react",
    "noEmitOnError": true,
    "outDir": "lib",
    "sourceMap": true,
    "target": "es5",
    "lib": ["es2015", "dom"]
  },
  "exclude": ["node_modules", "dist", "lib", "lib-amd"]
}
```

### Babel Configuration

#### Babel Config (`babel.config.json`)
```json
{
  "presets": [
    [
      "@babel/preset-env",
      {
        "targets": {
          "ie": "11"
        }
      }
    ],
    "@babel/preset-typescript"
  ]
}
```

## Build Scripts

### Package.json Scripts

#### Main Version (SSO)
```json
{
  "scripts": {
    "build": "webpack --mode production",
    "build:dev": "webpack --mode development",
    "configure-sso": "office-addin-sso configure manifest.xml",
    "dev-server": "node --inspect=localhost:8080 dist/middletier.js",
    "prestart": "npm run build:dev",
    "start": "office-addin-debugging start manifest.xml",
    "stop": "office-addin-debugging stop manifest.xml",
    "validate": "office-addin-manifest validate manifest.xml"
  }
}
```

#### RubiRecruit v7 (Local File Version)
```json
{
  "scripts": {
    "build": "webpack --mode production",
    "build:dev": "webpack --mode development",
    "dev-server": "webpack serve --mode development",
    "watch": "webpack --mode development --watch",
    "proxy": "node proxy-server.js --port 3003",
    "dev": "npm-run-all --parallel proxy start"
  }
}
```

## Development Environment Setup

### Prerequisites Installation
```bash
# Install Node.js (version 14 or higher)
# Install npm or yarn package manager

# Clone repository
git clone [repository-url]
cd RubiRecruit

# Install dependencies
npm install

# For v7 development
cd "RubiRecruit v7"
npm install
```

### Development Server Configuration

#### Main Version Development
```bash
# Build development version
npm run build:dev

# Start development server with debugging
npm run dev-server

# In another terminal, start Office debugging
npm start
```

#### RubiRecruit v7 Development
```bash
# Start proxy server for Claude API
npm run proxy

# Start webpack dev server
npm run dev-server

# Or run both simultaneously
npm run dev
```

### Environment Variables

#### Main Version (.env)
```env
# Azure AD Configuration
CLIENT_ID=your-azure-ad-client-id
CLIENT_SECRET=your-azure-ad-client-secret
SCOPE=User.Read

# Server Configuration
API_PORT=3000
NODE_ENV=development

# Microsoft Graph
GRAPH_URL_SEGMENT=/me
QUERY_PARAM_SEGMENT=
```

#### RubiRecruit v7 (.env)
```env
# Development Configuration
NODE_ENV=development
DEV_SERVER_PORT=3000
PROXY_PORT=3001

# API Configuration
CLAUDE_API_URL=https://api.anthropic.com/v1/messages
```

## Production Build Process

### Build Commands
```bash
# Production build
npm run build

# Validate manifest
npm run validate

# Lint code
npm run lint

# Run prettier
npm run prettier
```

### Build Output Structure
```
dist/
├── assets/
│   ├── icon-16.png
│   ├── icon-32.png
│   └── icon-80.png
├── taskpane.html
├── taskpane.js
├── taskpane.js.map
├── commands.html
├── commands.js
├── fallbackauthdialog.html
├── fallbackauthdialog.js
├── middletier.js (SSO version only)
├── polyfill.js
└── manifest.xml
```

### Asset Processing

#### HTML Template Processing
```javascript
// webpack.config.js - HTML plugin configuration
new HtmlWebpackPlugin({
  filename: "taskpane.html",
  template: "./src/taskpane/taskpane.html",
  chunks: ["polyfill", "taskpane"],
}),
```

#### Asset Copying
```javascript
// webpack.config.js - Copy plugin configuration
new CopyWebpackPlugin({
  patterns: [
    {
      from: "assets/*",
      to: "assets/[name][ext][query]",
    },
    {
      from: "manifest*.xml",
      to: "[name][ext]",
      transform(content) {
        if (dev) {
          return content;
        } else {
          // Replace development URLs with production URLs
          return content.toString().replace(new RegExp(urlDev, "g"), urlProd);
        }
      },
    },
  ],
}),
```

## Deployment Strategies

### Local Development Deployment
```bash
# Start local development
npm run build:dev
npm start

# Excel will open with the add-in loaded
# Changes will require rebuild and refresh
```

### Staging Deployment
```bash
# Build for staging
NODE_ENV=staging npm run build

# Deploy to staging server
# Update manifest URLs to staging endpoints
# Test with staging Office 365 tenant
```

### Production Deployment

#### Azure App Service Deployment
```bash
# Build production version
npm run build

# Configure Azure App Service
# Set environment variables
# Deploy dist folder contents
# Configure SSL certificates
```

#### SharePoint App Catalog Deployment
```bash
# Package add-in for SharePoint
# Upload to tenant app catalog
# Configure permissions and scopes
# Deploy to target sites
```

### Manifest Configuration

#### Development Manifest
```xml
<!-- Development URLs -->
<SourceLocation DefaultValue="https://localhost:3000/taskpane.html"/>
<bt:Url id="Taskpane.Url" DefaultValue="https://localhost:3000/taskpane.html"/>
```

#### Production Manifest
```xml
<!-- Production URLs -->
<SourceLocation DefaultValue="https://your-domain.com/taskpane.html"/>
<bt:Url id="Taskpane.Url" DefaultValue="https://your-domain.com/taskpane.html"/>
```

## SSL Certificate Management

### Development Certificates
```bash
# Generate development certificates
npx office-addin-dev-certs install

# Verify certificate installation
npx office-addin-dev-certs verify
```

### Production Certificates
- Use valid SSL certificates from trusted CA
- Configure HTTPS endpoints
- Ensure certificate chain is complete
- Set up automatic renewal

## Performance Optimization

### Bundle Optimization
```javascript
// webpack.config.js - Optimization
optimization: {
  splitChunks: {
    chunks: 'all',
    cacheGroups: {
      vendor: {
        test: /[\\/]node_modules[\\/]/,
        name: 'vendors',
        chunks: 'all',
      },
    },
  },
},
```

### Code Splitting
```typescript
// Dynamic imports for large dependencies
const pdfjs = await import('pdfjs-dist');
const msal = await import('@azure/msal-browser');
```

### Asset Optimization
- Image compression for icons
- CSS minification
- JavaScript minification and tree shaking
- Source map generation for debugging

## Testing and Quality Assurance

### Linting Configuration
```json
// .eslintrc.json
{
  "extends": ["plugin:office-addins/recommended"],
  "rules": {
    "no-console": "warn",
    "@typescript-eslint/no-unused-vars": "error"
  }
}
```

### Testing Commands
```bash
# Lint code
npm run lint

# Fix linting issues
npm run lint:fix

# Validate manifest
npm run validate

# Format code
npm run prettier
```

## Troubleshooting

### Common Build Issues

#### TypeScript Compilation Errors
```bash
# Clear TypeScript cache
rm -rf lib/
rm -rf dist/

# Rebuild
npm run build:dev
```

#### Webpack Bundle Issues
```bash
# Clear webpack cache
rm -rf node_modules/.cache/

# Reinstall dependencies
rm -rf node_modules/
npm install
```

#### SSL Certificate Issues
```bash
# Reinstall development certificates
npx office-addin-dev-certs uninstall
npx office-addin-dev-certs install
```

### Debugging Production Issues

#### Enable Source Maps
```javascript
// webpack.config.js
devtool: process.env.NODE_ENV === 'production' ? 'source-map' : 'eval-source-map'
```

#### Logging Configuration
```typescript
// Enable detailed logging in production
if (process.env.NODE_ENV === 'production') {
  console.log = () => {}; // Disable console.log
} else {
  // Keep debug logging in development
}
```

## Continuous Integration

### GitHub Actions Example
```yaml
# .github/workflows/build.yml
name: Build and Test
on: [push, pull_request]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '16'
      - run: npm install
      - run: npm run lint
      - run: npm run build
      - run: npm run validate
```

### Automated Deployment
```yaml
# Deploy to staging on main branch
deploy:
  if: github.ref == 'refs/heads/main'
  runs-on: ubuntu-latest
  steps:
    - run: npm run build
    - run: az webapp deployment source config-zip
```

## Security Considerations

### Build Security
- Validate all dependencies for vulnerabilities
- Use npm audit to check for security issues
- Keep dependencies updated
- Exclude sensitive files from build output

### Deployment Security
- Use HTTPS for all endpoints
- Implement proper CORS policies
- Validate SSL certificates
- Secure environment variables

## Future Enhancements

- **Docker containerization**: Simplified deployment
- **Automated testing**: Unit and integration tests
- **Performance monitoring**: Build time optimization
- **Multi-environment**: Enhanced configuration management
