# Excel Integration

## Overview

The Excel Integration component provides comprehensive data management within Microsoft Excel, creating specialized worksheets for recruitment data, candidate evaluations, and analytics. It serves as the primary data persistence layer for RubiRecruit.

## Architecture

### Core Components (RubiRecruit v7)

1. **Excel Service** (`RubiRecruit v7/src/services/excelService.ts`)
   - Worksheet creation and management
   - Data storage and retrieval
   - Formatting and styling
   - Range operations and calculations

2. **Data Persistence**
   - Configuration storage within Excel
   - Candidate evaluation results
   - Processing logs and audit trails
   - Analytics and summary data

## Worksheet Structure

### Core Worksheets Created

1. **Position Configuration**
   - Job description metadata
   - Position requirements
   - Loading status and timestamps

2. **Document Mapping**
   - Candidate-to-document relationships
   - File names and processing status
   - Mapping confidence levels

3. **Candidate Evaluations**
   - Detailed scoring results
   - Category-wise assessments
   - Recommendations and feedback

4. **Company Metrics**
   - Career progression data
   - Experience analysis
   - Industry and skill tracking

5. **Processing Log**
   - Operation timestamps
   - Success/failure tracking
   - Error messages and debugging

6. **Dynamic Rubric Configuration**
   - AI-generated evaluation criteria
   - Weighted scoring structure
   - Category and attribute definitions

7. **Summary Dashboard**
   - High-level analytics
   - Candidate rankings
   - Processing statistics

## Excel Service Implementation

### Worksheet Creation
```typescript
class ExcelService {
  async createAllSheets(): Promise<void> {
    await Excel.run(async (context) => {
      const sheets = context.workbook.worksheets;
      
      const sheetConfigs = [
        { name: 'Processing Log', headers: ['Timestamp', 'Message', 'Level'] },
        { name: 'Processed Candidates', headers: ['Name', 'CV File', 'Cover Letter', 'Score'] },
        { name: 'Document Mapping', headers: ['Candidate', 'CV File', 'Cover Letter', 'Status'] },
        { name: 'Position Configuration', headers: ['Setting', 'Value', 'Status'] },
        { name: 'Candidate Evaluations', headers: ['Name', 'Total Score', 'Recommendation'] },
        // ... additional sheets
      ];
      
      for (const config of sheetConfigs) {
        await this.createSheetWithHeaders(sheets, config.name, config.headers);
      }
      
      await context.sync();
    });
  }
  
  private async createSheetWithHeaders(
    sheets: Excel.WorksheetCollection, 
    name: string, 
    headers: string[]
  ): Promise<void> {
    try {
      const sheet = sheets.add(name);
      const headerRange = sheet.getRange(`A1:${this.getColumnLetter(headers.length)}1`);
      headerRange.values = [headers];
      headerRange.format.font.bold = true;
      headerRange.format.fill.color = "#4472C4";
      headerRange.format.font.color = "white";
    } catch (error) {
      // Sheet might already exist
      console.log(`Sheet ${name} already exists or creation failed`);
    }
  }
}
```

### Data Storage Operations

#### Configuration Storage
```typescript
async setStoredValue(key: string, value: string): Promise<void> {
  await Excel.run(async (context) => {
    const sheet = this.getOrCreateStorageSheet(context);
    
    // Find existing key or create new row
    const dataRange = sheet.getUsedRange();
    if (dataRange) {
      dataRange.load('values');
      await context.sync();
      
      // Search for existing key
      const values = dataRange.values;
      let rowIndex = -1;
      
      for (let i = 0; i < values.length; i++) {
        if (values[i][0] === key) {
          rowIndex = i + 1; // Excel is 1-indexed
          break;
        }
      }
      
      if (rowIndex > 0) {
        // Update existing
        sheet.getCell(rowIndex - 1, 1).values = [[value]];
      } else {
        // Add new row
        const newRow = values.length + 1;
        sheet.getRange(`A${newRow}:B${newRow}`).values = [[key, value]];
      }
    } else {
      // First entry
      sheet.getRange('A1:B1').values = [[key, value]];
    }
    
    await context.sync();
  });
}

async getStoredValue(key: string): Promise<string | null> {
  return await Excel.run(async (context) => {
    const sheet = this.getStorageSheet(context);
    if (!sheet) return null;
    
    const dataRange = sheet.getUsedRange();
    if (!dataRange) return null;
    
    dataRange.load('values');
    await context.sync();
    
    const values = dataRange.values;
    for (const row of values) {
      if (row[0] === key) {
        return row[1] as string;
      }
    }
    
    return null;
  });
}
```

#### Candidate Evaluation Storage
```typescript
async addCandidateEvaluation(evaluation: any): Promise<void> {
  await Excel.run(async (context) => {
    const sheet = context.workbook.worksheets.getItem('Candidate Evaluations');
    
    // Get next available row
    const usedRange = sheet.getUsedRange();
    let nextRow = 2; // Start after headers
    
    if (usedRange) {
      usedRange.load('rowCount');
      await context.sync();
      nextRow = usedRange.rowCount + 1;
    }
    
    // Prepare evaluation data
    const rowData = [
      evaluation.candidate_name,
      evaluation.total_score,
      evaluation.recommendation,
      evaluation.strengths.join('; '),
      evaluation.development_areas.join('; '),
      new Date().toLocaleDateString(),
      evaluation.documentsStatus || 'Unknown'
    ];
    
    // Write to Excel
    const range = sheet.getRangeByIndexes(nextRow - 1, 0, 1, rowData.length);
    range.values = [rowData];
    
    // Apply conditional formatting for scores
    this.applyScoreFormatting(sheet, nextRow, 2); // Column B (Total Score)
    
    await context.sync();
  });
}
```

### Dynamic Rubric Management
```typescript
async writeDynamicRubric(rubric: any): Promise<void> {
  await Excel.run(async (context) => {
    const sheet = context.workbook.worksheets.getItem('Dynamic Rubric Configuration');
    
    // Clear existing content
    const usedRange = sheet.getUsedRange();
    if (usedRange) {
      usedRange.clear();
    }
    
    // Write headers
    const headers = ['Category', 'Weight', 'Attribute', 'Attribute Weight', 'Description'];
    sheet.getRange('A1:E1').values = [headers];
    
    let currentRow = 2;
    
    // Write rubric data
    for (const [categoryName, categoryData] of Object.entries(rubric)) {
      const category = categoryData as any;
      
      for (let i = 0; i < category.attributes.length; i++) {
        const attribute = category.attributes[i];
        
        const rowData = [
          i === 0 ? categoryName : '', // Category name only on first row
          i === 0 ? category.weight : '', // Weight only on first row
          attribute.name,
          attribute.weight,
          attribute.description
        ];
        
        sheet.getRangeByIndexes(currentRow - 1, 0, 1, 5).values = [rowData];
        currentRow++;
      }
    }
    
    // Format the sheet
    this.formatRubricSheet(sheet);
    
    await context.sync();
  });
}
```

## Advanced Excel Operations

### Conditional Formatting
```typescript
private applyScoreFormatting(sheet: Excel.Worksheet, row: number, column: number): void {
  const cell = sheet.getCell(row - 1, column - 1);
  
  // Color coding based on score ranges
  const conditionalFormat = cell.conditionalFormats.add(Excel.ConditionalFormatType.cellValue);
  conditionalFormat.cellValue.format.fill.color = "#4CAF50"; // Green for high scores
  conditionalFormat.cellValue.rule = {
    formula1: "80",
    operator: Excel.ConditionalCellValueOperator.greaterThanOrEqual
  };
  
  // Add more conditional formats for different score ranges
  // ... additional formatting rules
}
```

### Data Validation
```typescript
async addDataValidation(sheetName: string, range: string, validationRule: any): Promise<void> {
  await Excel.run(async (context) => {
    const sheet = context.workbook.worksheets.getItem(sheetName);
    const targetRange = sheet.getRange(range);
    
    targetRange.dataValidation.rule = validationRule;
    targetRange.dataValidation.prompt = {
      message: "Please select a valid option",
      showPrompt: true,
      title: "Data Validation"
    };
    
    await context.sync();
  });
}
```

### Formula and Calculation Support
```typescript
async addCalculatedColumns(sheetName: string): Promise<void> {
  await Excel.run(async (context) => {
    const sheet = context.workbook.worksheets.getItem(sheetName);
    
    // Add calculated score column
    const scoreColumn = sheet.getRange('H:H');
    scoreColumn.formulas = [['=IF(B2<>"", B2*100, "")']]; // Convert to percentage
    
    // Add ranking formula
    const rankColumn = sheet.getRange('I:I');
    rankColumn.formulas = [['=IF(B2<>"", RANK(B2, B:B, 0), "")']];
    
    await context.sync();
  });
}
```

## Data Analysis and Reporting

### Summary Dashboard Creation
```typescript
async updateSummaryDashboard(): Promise<void> {
  await Excel.run(async (context) => {
    const sheet = context.workbook.worksheets.getItem('Summary Dashboard');
    
    // Clear existing content
    sheet.getUsedRange()?.clear();
    
    // Create summary statistics
    const summaryData = await this.calculateSummaryStats(context);
    
    // Write dashboard headers and data
    const dashboardLayout = [
      ['RubiRecruit Summary Dashboard', '', '', ''],
      ['Generated:', new Date().toLocaleDateString(), '', ''],
      ['', '', '', ''],
      ['Metric', 'Value', 'Details', 'Status'],
      ['Total Candidates', summaryData.totalCandidates, '', 'Complete'],
      ['Average Score', summaryData.averageScore, summaryData.scoreRange, 'Calculated'],
      ['Top Performer', summaryData.topCandidate, summaryData.topScore, 'Identified'],
      ['Processing Time', summaryData.processingTime, 'Minutes', 'Tracked']
    ];
    
    const range = sheet.getRangeByIndexes(0, 0, dashboardLayout.length, 4);
    range.values = dashboardLayout;
    
    // Apply formatting
    this.formatDashboard(sheet);
    
    await context.sync();
  });
}
```

### Analytics Calculations
```typescript
private async calculateSummaryStats(context: Excel.RequestContext): Promise<any> {
  const evaluationSheet = context.workbook.worksheets.getItem('Candidate Evaluations');
  const dataRange = evaluationSheet.getUsedRange();
  
  if (!dataRange) {
    return {
      totalCandidates: 0,
      averageScore: 0,
      topCandidate: 'None',
      topScore: 0,
      processingTime: 0
    };
  }
  
  dataRange.load('values');
  await context.sync();
  
  const values = dataRange.values;
  const scores = values.slice(1).map(row => parseFloat(row[1])).filter(score => !isNaN(score));
  
  const totalCandidates = scores.length;
  const averageScore = scores.reduce((sum, score) => sum + score, 0) / totalCandidates;
  const maxScore = Math.max(...scores);
  const topCandidateIndex = values.findIndex(row => parseFloat(row[1]) === maxScore);
  const topCandidate = topCandidateIndex > 0 ? values[topCandidateIndex][0] : 'None';
  
  return {
    totalCandidates,
    averageScore: Math.round(averageScore * 100) / 100,
    topCandidate,
    topScore: maxScore,
    scoreRange: `${Math.min(...scores)} - ${Math.max(...scores)}`,
    processingTime: this.calculateProcessingTime()
  };
}
```

## Error Handling and Data Integrity

### Duplicate Detection
```typescript
async checkForDuplicates(candidateName: string): Promise<boolean> {
  return await Excel.run(async (context) => {
    const sheet = context.workbook.worksheets.getItem('Processed Candidates');
    const dataRange = sheet.getUsedRange();
    
    if (!dataRange) return false;
    
    dataRange.load('values');
    await context.sync();
    
    const values = dataRange.values;
    return values.some(row => row[0] === candidateName);
  });
}
```

### Data Validation and Cleanup
```typescript
async validateDataIntegrity(): Promise<string[]> {
  const issues: string[] = [];
  
  await Excel.run(async (context) => {
    // Check for required sheets
    const requiredSheets = [
      'Position Configuration',
      'Candidate Evaluations',
      'Processing Log'
    ];
    
    const sheets = context.workbook.worksheets;
    sheets.load('items/name');
    await context.sync();
    
    const existingSheets = sheets.items.map(s => s.name);
    
    for (const required of requiredSheets) {
      if (!existingSheets.includes(required)) {
        issues.push(`Missing required sheet: ${required}`);
      }
    }
    
    // Validate data consistency
    await this.validateEvaluationData(context, issues);
  });
  
  return issues;
}
```

## Performance Optimization

### Batch Operations
```typescript
async batchUpdateCandidates(evaluations: any[]): Promise<void> {
  await Excel.run(async (context) => {
    const sheet = context.workbook.worksheets.getItem('Candidate Evaluations');
    
    // Prepare all data at once
    const batchData = evaluations.map(eval => [
      eval.candidate_name,
      eval.total_score,
      eval.recommendation,
      eval.strengths.join('; '),
      eval.development_areas.join('; '),
      new Date().toLocaleDateString()
    ]);
    
    // Single range operation
    const startRow = await this.getNextAvailableRow(sheet);
    const range = sheet.getRangeByIndexes(
      startRow - 1, 
      0, 
      batchData.length, 
      batchData[0].length
    );
    
    range.values = batchData;
    await context.sync();
  });
}
```

### Memory Management
```typescript
async optimizeWorkbook(): Promise<void> {
  await Excel.run(async (context) => {
    // Remove unused ranges and optimize formulas
    const sheets = context.workbook.worksheets;
    sheets.load('items');
    await context.sync();
    
    for (const sheet of sheets.items) {
      // Auto-fit columns
      sheet.getUsedRange()?.format.autofitColumns();
      
      // Clear empty rows
      await this.removeEmptyRows(sheet);
    }
    
    await context.sync();
  });
}
```

## Integration Points

### Service Layer Communication
- **Task Pane**: User interface data binding
- **AI Services**: Evaluation result storage
- **File Services**: Document metadata tracking
- **Authentication**: Configuration persistence

### Data Flow
```
User Input → Task Pane → Excel Service → Worksheet Storage
     ↓              ↓           ↓              ↓
AI Results → Processing → Data Validation → Formatted Output
```

## Future Enhancements

- **Chart integration**: Visual analytics and dashboards
- **Pivot table automation**: Dynamic data analysis
- **Export capabilities**: PDF and CSV generation
- **Template management**: Customizable worksheet layouts
- **Data synchronization**: Real-time updates across sheets
