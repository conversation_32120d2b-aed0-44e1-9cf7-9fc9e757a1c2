{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Excel Desktop (Edge Chromium)",
      "type": "msedge",
      "request": "attach",
      "port": 9229,
      "timeout": 600000,
      "webRoot": "${workspaceRoot}",
      "preLaunchTask": "Debug: Excel Desktop",
      "postDebugTask": "Stop Debug"
    },
    {
      "name": "Excel Desktop (Edge Legacy)",
      "type": "office-addin",
      "request": "attach",
      "url": "https://localhost:3000/taskpane.html?_host_Info=Excel$Win32$16.01$en-US$$$$0",
      "port": 9222,
      "timeout": 600000,
      "webRoot": "${workspaceRoot}",
      "preLaunchTask": "Debug: Excel Desktop",
      "postDebugTask": "Stop Debug"
    },
    {
      "name": "Middle Tier (launch and attach)",
      "type": "node",
      "request": "attach",
      "port": 8080,
      "timeout": 600000,
      "preLaunchTask": "Debug: Desktop",
      "postDebugTask": "Stop Debug"
    },
    {
      "name": "Middle Tier (attach to existing)",
      "type": "node",
      "request": "attach",
      "port": 8080,
      "timeout": 600000
    },
    {
      "type": "pwa-msedge",
      "name": "Launch Microsoft Edge",
      "request": "launch",
      "runtimeArgs": [
        "--remote-debugging-port=9222"
      ],
      "url": "/home/<USER>/.vscode/extensions/ms-edgedevtools.vscode-edge-devtools-2.1.9/out/startpage/index.html", // Provide your project's url to finish configuring
      "presentation": {
        "hidden": true
      }
    },
    {
      "type": "pwa-msedge",
      "name": "Launch Microsoft Edge in headless mode",
      "request": "launch",
      "runtimeArgs": [
        "--headless",
        "--remote-debugging-port=9222"
      ],
      "url": "/home/<USER>/.vscode/extensions/ms-edgedevtools.vscode-edge-devtools-2.1.9/out/startpage/index.html", // Provide your project's url to finish configuring
      "presentation": {
        "hidden": true
      }
    },
    {
      "type": "vscode-edge-devtools.debug",
      "name": "Open Edge DevTools",
      "request": "attach",
      "url": "/home/<USER>/.vscode/extensions/ms-edgedevtools.vscode-edge-devtools-2.1.9/out/startpage/index.html", // Provide your project's url to finish configuring
      "presentation": {
        "hidden": true
      }
    }
  ],
  "compounds": [
    {
      "name": "Launch Edge Headless and attach DevTools",
      "configurations": [
        "Launch Microsoft Edge in headless mode",
        "Open Edge DevTools"
      ]
    },
    {
      "name": "Launch Edge and attach DevTools",
      "configurations": [
        "Launch Microsoft Edge",
        "Open Edge DevTools"
      ]
    }
  ]
}