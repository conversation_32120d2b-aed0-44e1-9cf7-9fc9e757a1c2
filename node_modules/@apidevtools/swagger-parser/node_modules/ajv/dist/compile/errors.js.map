{"version": 3, "file": "errors.js", "sourceRoot": "", "sources": ["../../lib/compile/errors.ts"], "names": [], "mappings": ";;;AAEA,uCAAgE;AAEhE,iCAAyC;AACzC,mCAAuB;AAEV,QAAA,YAAY,GAA2B;IAClD,OAAO,EAAE,CAAC,EAAC,OAAO,EAAC,EAAE,EAAE,CAAC,IAAA,aAAG,EAAA,cAAc,OAAO,sBAAsB;CACvE,CAAA;AAEY,QAAA,iBAAiB,GAA2B;IACvD,OAAO,EAAE,CAAC,EAAC,OAAO,EAAE,UAAU,EAAC,EAAE,EAAE,CACjC,UAAU;QACR,CAAC,CAAC,IAAA,aAAG,EAAA,IAAI,OAAO,qBAAqB,UAAU,UAAU;QACzD,CAAC,CAAC,IAAA,aAAG,EAAA,IAAI,OAAO,8BAA8B;CACnD,CAAA;AAQD,SAAgB,WAAW,CACzB,GAAoB,EACpB,QAAgC,oBAAY,EAC5C,UAAuB,EACvB,iBAA2B;IAE3B,MAAM,EAAC,EAAE,EAAC,GAAG,GAAG,CAAA;IAChB,MAAM,EAAC,GAAG,EAAE,aAAa,EAAE,SAAS,EAAC,GAAG,EAAE,CAAA;IAC1C,MAAM,MAAM,GAAG,eAAe,CAAC,GAAG,EAAE,KAAK,EAAE,UAAU,CAAC,CAAA;IACtD,IAAI,iBAAiB,aAAjB,iBAAiB,cAAjB,iBAAiB,GAAI,CAAC,aAAa,IAAI,SAAS,CAAC,EAAE,CAAC;QACtD,QAAQ,CAAC,GAAG,EAAE,MAAM,CAAC,CAAA;IACvB,CAAC;SAAM,CAAC;QACN,YAAY,CAAC,EAAE,EAAE,IAAA,WAAC,EAAA,IAAI,MAAM,GAAG,CAAC,CAAA;IAClC,CAAC;AACH,CAAC;AAdD,kCAcC;AAED,SAAgB,gBAAgB,CAC9B,GAAoB,EACpB,QAAgC,oBAAY,EAC5C,UAAuB;IAEvB,MAAM,EAAC,EAAE,EAAC,GAAG,GAAG,CAAA;IAChB,MAAM,EAAC,GAAG,EAAE,aAAa,EAAE,SAAS,EAAC,GAAG,EAAE,CAAA;IAC1C,MAAM,MAAM,GAAG,eAAe,CAAC,GAAG,EAAE,KAAK,EAAE,UAAU,CAAC,CAAA;IACtD,QAAQ,CAAC,GAAG,EAAE,MAAM,CAAC,CAAA;IACrB,IAAI,CAAC,CAAC,aAAa,IAAI,SAAS,CAAC,EAAE,CAAC;QAClC,YAAY,CAAC,EAAE,EAAE,eAAC,CAAC,OAAO,CAAC,CAAA;IAC7B,CAAC;AACH,CAAC;AAZD,4CAYC;AAED,SAAgB,gBAAgB,CAAC,GAAY,EAAE,SAAe;IAC5D,GAAG,CAAC,MAAM,CAAC,eAAC,CAAC,MAAM,EAAE,SAAS,CAAC,CAAA;IAC/B,GAAG,CAAC,EAAE,CAAC,IAAA,WAAC,EAAA,GAAG,eAAC,CAAC,OAAO,WAAW,EAAE,GAAG,EAAE,CACpC,GAAG,CAAC,EAAE,CACJ,SAAS,EACT,GAAG,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,IAAA,WAAC,EAAA,GAAG,eAAC,CAAC,OAAO,SAAS,EAAE,SAAS,CAAC,EACnD,GAAG,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,eAAC,CAAC,OAAO,EAAE,IAAI,CAAC,CAClC,CACF,CAAA;AACH,CAAC;AATD,4CASC;AAED,SAAgB,YAAY,CAAC,EAC3B,GAAG,EACH,OAAO,EACP,WAAW,EACX,IAAI,EACJ,SAAS,EACT,EAAE,GACc;IAChB,wBAAwB;IACxB,IAAI,SAAS,KAAK,SAAS;QAAE,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAA;IACxE,MAAM,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;IAC3B,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,SAAS,EAAE,eAAC,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE;QAC3C,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,IAAA,WAAC,EAAA,GAAG,eAAC,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA;QACrC,GAAG,CAAC,EAAE,CAAC,IAAA,WAAC,EAAA,GAAG,GAAG,6BAA6B,EAAE,GAAG,EAAE,CAChD,GAAG,CAAC,MAAM,CAAC,IAAA,WAAC,EAAA,GAAG,GAAG,eAAe,EAAE,IAAA,mBAAS,EAAC,eAAC,CAAC,YAAY,EAAE,EAAE,CAAC,SAAS,CAAC,CAAC,CAC5E,CAAA;QACD,GAAG,CAAC,MAAM,CAAC,IAAA,WAAC,EAAA,GAAG,GAAG,aAAa,EAAE,IAAA,aAAG,EAAA,GAAG,EAAE,CAAC,aAAa,IAAI,OAAO,EAAE,CAAC,CAAA;QACrE,IAAI,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YACpB,GAAG,CAAC,MAAM,CAAC,IAAA,WAAC,EAAA,GAAG,GAAG,SAAS,EAAE,WAAW,CAAC,CAAA;YACzC,GAAG,CAAC,MAAM,CAAC,IAAA,WAAC,EAAA,GAAG,GAAG,OAAO,EAAE,IAAI,CAAC,CAAA;QAClC,CAAC;IACH,CAAC,CAAC,CAAA;AACJ,CAAC;AAtBD,oCAsBC;AAED,SAAS,QAAQ,CAAC,GAAY,EAAE,MAAY;IAC1C,MAAM,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;IACpC,GAAG,CAAC,EAAE,CACJ,IAAA,WAAC,EAAA,GAAG,eAAC,CAAC,OAAO,WAAW,EACxB,GAAG,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,eAAC,CAAC,OAAO,EAAE,IAAA,WAAC,EAAA,IAAI,GAAG,GAAG,CAAC,EACxC,IAAA,WAAC,EAAA,GAAG,eAAC,CAAC,OAAO,SAAS,GAAG,GAAG,CAC7B,CAAA;IACD,GAAG,CAAC,IAAI,CAAC,IAAA,WAAC,EAAA,GAAG,eAAC,CAAC,MAAM,IAAI,CAAC,CAAA;AAC5B,CAAC;AAED,SAAS,YAAY,CAAC,EAAa,EAAE,IAAU;IAC7C,MAAM,EAAC,GAAG,EAAE,YAAY,EAAE,SAAS,EAAC,GAAG,EAAE,CAAA;IACzC,IAAI,SAAS,CAAC,MAAM,EAAE,CAAC;QACrB,GAAG,CAAC,KAAK,CAAC,IAAA,WAAC,EAAA,OAAO,EAAE,CAAC,eAAuB,IAAI,IAAI,GAAG,CAAC,CAAA;IAC1D,CAAC;SAAM,CAAC;QACN,GAAG,CAAC,MAAM,CAAC,IAAA,WAAC,EAAA,GAAG,YAAY,SAAS,EAAE,IAAI,CAAC,CAAA;QAC3C,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;IACnB,CAAC;AACH,CAAC;AAED,MAAM,CAAC,GAAG;IACR,OAAO,EAAE,IAAI,cAAI,CAAC,SAAS,CAAC;IAC5B,UAAU,EAAE,IAAI,cAAI,CAAC,YAAY,CAAC,EAAE,0BAA0B;IAC9D,MAAM,EAAE,IAAI,cAAI,CAAC,QAAQ,CAAC;IAC1B,YAAY,EAAE,IAAI,cAAI,CAAC,cAAc,CAAC;IACtC,OAAO,EAAE,IAAI,cAAI,CAAC,SAAS,CAAC;IAC5B,MAAM,EAAE,IAAI,cAAI,CAAC,QAAQ,CAAC;IAC1B,YAAY,EAAE,IAAI,cAAI,CAAC,cAAc,CAAC;CACvC,CAAA;AAED,SAAS,eAAe,CACtB,GAAoB,EACpB,KAA6B,EAC7B,UAAuB;IAEvB,MAAM,EAAC,YAAY,EAAC,GAAG,GAAG,CAAC,EAAE,CAAA;IAC7B,IAAI,YAAY,KAAK,KAAK;QAAE,OAAO,IAAA,WAAC,EAAA,IAAI,CAAA;IACxC,OAAO,WAAW,CAAC,GAAG,EAAE,KAAK,EAAE,UAAU,CAAC,CAAA;AAC5C,CAAC;AAED,SAAS,WAAW,CAClB,GAAoB,EACpB,KAA6B,EAC7B,aAAyB,EAAE;IAE3B,MAAM,EAAC,GAAG,EAAE,EAAE,EAAC,GAAG,GAAG,CAAA;IACrB,MAAM,SAAS,GAAgC;QAC7C,iBAAiB,CAAC,EAAE,EAAE,UAAU,CAAC;QACjC,eAAe,CAAC,GAAG,EAAE,UAAU,CAAC;KACjC,CAAA;IACD,eAAe,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,CAAC,CAAA;IACtC,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,SAAS,CAAC,CAAA;AACjC,CAAC;AAED,SAAS,iBAAiB,CAAC,EAAC,SAAS,EAAY,EAAE,EAAC,YAAY,EAAa;IAC3E,MAAM,QAAQ,GAAG,YAAY;QAC3B,CAAC,CAAC,IAAA,aAAG,EAAA,GAAG,SAAS,GAAG,IAAA,mBAAY,EAAC,YAAY,EAAE,WAAI,CAAC,GAAG,CAAC,EAAE;QAC1D,CAAC,CAAC,SAAS,CAAA;IACb,OAAO,CAAC,eAAC,CAAC,YAAY,EAAE,IAAA,mBAAS,EAAC,eAAC,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC,CAAA;AAC9D,CAAC;AAED,SAAS,eAAe,CACtB,EAAC,OAAO,EAAE,EAAE,EAAE,EAAC,aAAa,EAAC,EAAkB,EAC/C,EAAC,UAAU,EAAE,YAAY,EAAa;IAEtC,IAAI,OAAO,GAAG,YAAY,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,IAAA,aAAG,EAAA,GAAG,aAAa,IAAI,OAAO,EAAE,CAAA;IAC7E,IAAI,UAAU,EAAE,CAAC;QACf,OAAO,GAAG,IAAA,aAAG,EAAA,GAAG,OAAO,GAAG,IAAA,mBAAY,EAAC,UAAU,EAAE,WAAI,CAAC,GAAG,CAAC,EAAE,CAAA;IAChE,CAAC;IACD,OAAO,CAAC,CAAC,CAAC,UAAU,EAAE,OAAO,CAAC,CAAA;AAChC,CAAC;AAED,SAAS,eAAe,CACtB,GAAoB,EACpB,EAAC,MAAM,EAAE,OAAO,EAAyB,EACzC,SAAsC;IAEtC,MAAM,EAAC,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,EAAE,EAAC,GAAG,GAAG,CAAA;IAC5C,MAAM,EAAC,IAAI,EAAE,YAAY,EAAE,YAAY,EAAE,UAAU,EAAC,GAAG,EAAE,CAAA;IACzD,SAAS,CAAC,IAAI,CACZ,CAAC,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,EACpB,CAAC,CAAC,CAAC,MAAM,EAAE,OAAO,MAAM,IAAI,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,IAAA,WAAC,EAAA,IAAI,CAAC,CACxE,CAAA;IACD,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;QAClB,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,OAAO,OAAO,IAAI,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAA;IACpF,CAAC;IACD,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;QACjB,SAAS,CAAC,IAAI,CACZ,CAAC,CAAC,CAAC,MAAM,EAAE,WAAW,CAAC,EACvB,CAAC,CAAC,CAAC,YAAY,EAAE,IAAA,WAAC,EAAA,GAAG,YAAY,GAAG,UAAU,EAAE,CAAC,EACjD,CAAC,eAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CACf,CAAA;IACH,CAAC;IACD,IAAI,YAAY;QAAE,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC,CAAA;AAClE,CAAC"}