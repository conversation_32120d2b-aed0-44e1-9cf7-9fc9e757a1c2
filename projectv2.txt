RubiRecruit v2.0 - Functional Requirements 

Last Updated: 1st September 

1. Core Evaluation Workflow 

1.1 Position-Based Rubric Generation 

Functionality 

Accepts PDF position descriptions from user's cloud storage 

Analyzes position using native LLM (Gemini/GPT-4o) – TBC LETS DISCUSS 

Generates exactly 8 weighted categories (5-20% each, totaling 100%) 

Creates 5 specific attributes per category 

Builds 6-level scoring rubrics (0-5) with behavioral descriptions 

Adapts to industry, seniority, and role type 

Technical Requirements 

Text extraction without OCR dependency (v1.0) - phase 2  

Maximum 10,000 characters processed from position description 

Generation time: <30 seconds  

Rubric stored in user's sheet/workbook 

User Experience – currently exist  

One-click generation from selected PDF 

Preview before locking 

Edit capability before locking 

Version tracking with timestamps   

1.2 Document Processing & Matching 

Smart Document Matching  - currently build  

Primary Method: File ID tracking (prevents reprocessing) 

Secondary Methods:  

Name extraction from headers/filename 

Email/phone matching across documents 

Upload timestamp proximity 

Content similarity scoring 

Duplicate Detection (Enhanced) - currently built  

File ID as primary key 

Fuzzy name matching (handles variations) 

Resets per position (key requirement) 

Confidence scoring: 0-100% 

Name Extraction Hierarchy 

Explicit headers ("Name:", "Full Name:") 

Email parsing (<EMAIL> → <PERSON>) 

Filename analysis with cleanup 

First prominent text (bold/large) 

1.3 Evaluation Engine 

Processing Capabilities 

Batch Sizes: 5, 10, or all remaining 

Per-Candidate Time: <30 seconds 

Concurrent Processing: Up to 20 per user 

Text Limit: 14,000 characters per candidate 

Auto-Retry: 3 attempts with exponential backoff 

Scoring Mathematics 

For each attribute (0-5 score): 

→ Convert to percentage (score/5 × 100) 

→ Apply weight within category 

→ Sum for category score (0-100%) 

 

For overall score: 

→ Multiply each category score by category weight 

→ Sum all weighted categories (0-100) 

 

Recommendations: 

- 80-100: Strong Candidate 

- 70-79: Good Candidate   

- 60-69: Developing Candidate 

- 0-59: Poor Fit 

2. Enhanced Metrics Extraction – (Would like to explore what making this dynamic and adjustable looks like) 

2.1 Employment History Analysis 

Total career experience (years) 

Industry-specific experience 

Average tenure per role 

Management experience years 

Maximum team size managed 

Budget responsibility 

Short stint detection (<12 months) 

Job hopping risk assessment 

2.2 Qualification Profiling 

Universities attended 

Degrees earned 

Professional certifications 

Technical skills inventory 

Functional expertise areas 

Notable company experience 

Language quality (Very High to Poor) 

2.3 X-Factor Identification 

Top 3 unique achievements 

Maximum 15 words each 

Focus on quantifiable results 

Prestigious recognitions 

Exceptional performance indicators 

3. Licensing & Access Control 

3.1 Trial Enforcement 

Limit: 3 evaluations lifetime 

Tracking: Server-side counter 

Block Mechanism: API rejection after limit 

Visibility: Counter shown in UI 

Reset: Not available (lifetime limit) 

3.2 Tier Management 

Feature 

Trial 

One-off 

Growth 

Full 

Rubric Generation 

✅ 

✅ 

✅ 

✅ 

Evaluations 

3 lifetime 

500 total 

100/day 

300/day 

Positions 

1 

1 

3/month 

Unlimited 

Processing Priority 

Low 

Normal 

Normal 

High 

Support 

Community 

Email 

Email 

Priority 

3.3 License Validation 

Real-time check with marketplace 

Grace period: 7 days for payment issues 

Read-only mode when expired 

Data retention: 30 days post-expiry 

4. Platform-Specific Features 

4.1 Google Workspace Implementation 

User Interface 

Sheets sidebar (300px width) 

8-step journey tracker 

Real-time progress indicators 

Native Material Design 

Data Storage 

Position descriptions: Google Drive folder 

CVs/Covers: Separate Google Drive folder 

Results: User's spreadsheet 

Rubric: Stored in sheet 

Integration Points 

OAuth 2.0 authentication 

Drive API for file access 

Sheets API for data writing 

Workspace Marketplace billing 

4.2 Microsoft AppSource Implementation 

User Interface 

Excel task pane (320px width) 

Identical 8-step journey 

Office UI Fabric design 

Desktop + Web support 

Data Storage 

Position descriptions: SharePoint/OneDrive folder 

CVs/Covers: SharePoint/OneDrive folder 

Results: User's workbook 

Rubric:  worksheet 

Integration Points 

Microsoft Graph authentication 

SharePoint/OneDrive APIs 

Excel JavaScript API 

Azure Marketplace billing 

5. Processing Workflow 

5.1 Eight-Step Journey 

Initial Setup 

Configure folders 

Accept terms & conditions 

Validate subscription 

Create Sheets/Worksheets 

Generate 13-15 required tabs 

Set up formatting 

Initialize tracking 

Load Position 

Select PDF from folder 

Extract text 

Store in configuration 

Document Mapping 

Scan CV/cover letter folder 

Match CVs with covers 

Create candidate list 

Generate Rubric 

AI analyzes position 

Creates evaluation framework 

Allows editing 

Lock Rubric 

Finalize criteria 

Prevent modifications 

Enable evaluations 

Process Candidates 

Batch or individual processing 

Progress tracking 

Error handling 

Review Results 

Dashboard summary 

Detailed evaluations 

5.2 Batch Processing Options 

Process Next 5: Manual control, good for testing 

Process Next 10: Balanced automation 

Process Specific: Select by name 

Process All: Automatic with pauses 

Retry Failed: Reprocess errors only 

6. Security & Compliance 

6.1 Data Protection 

Encryption: TLS 1.3 transit, platform-native at rest 

Isolation: Complete separation between customers 

Access: User data only in their cloud storage 

Retention: User-controlled via file deletion 

Backup: Platform-native (Drive/SharePoint) 

6.2 Legal Compliance 

Disclaimers: AI evaluations are advisory only 

Terms: Required acceptance before use 

Privacy: No central storage of candidate data 

GDPR: User controls all data deletion 

Audit: Full activity logging available 

6.3 Access Control 

Authentication: Platform SSO only 

Authorization: Folder-level permissions 

Session: Platform-managed timeouts 

MFA: Inherited from platform 

7. Performance Requirements 

7.1 Response Times 

UI Actions: <3 seconds 

Rubric Generation: <30 seconds 

Per Candidate: <30 seconds 

Batch of 5: <3 minutes 

Dashboard Update: <5 seconds 

7.2 Scalability – TBC lets chat 

Concurrent Users: 1,000 system-wide 

Per-User Concurrency: 20 evaluations 

Daily Volume: 10,000 evaluations 

Peak Hour: 1,000 evaluations 

7.3 Reliability 

Uptime SLO: 99.5% 

Error Rate: <0.5% 

Retry Success: >95% 

Data Loss: 0% 

8. Error Handling 

8.1 Graceful Degradation 

LLM timeout: Queue for retry 

API limit: User-specific queue 

Platform outage: Read-only mode 

Billing failure: 7-day grace period 

8.2 User Notifications 

Processing status: Real-time updates 

Errors: Clear explanations 

Limits reached: Upgrade prompts 

System issues: Status page 

9. Quality Assurance Requirements 

9.1 Testing Coverage 

Unit Tests: >80% code coverage 

Integration Tests: All API endpoints 

E2E Tests: Complete user journeys 

Load Tests: 100 concurrent users 

Security: OWASP top 10 – needs to be certified separately  

9.2 Acceptance Metrics 

Duplicate Detection: >99.5% accuracy 

CV-Cover Matching: >90% accuracy 

Rubric Generation: 100% valid structure 

Score Calculation: 100% deterministic 

Billing Integration: 100% accurate 

10. Features Explicitly Deferred 

To v1.1 

OCR for image-based PDFs – TBC lets discuss how hard this is… 

Email notifications 

Comparative analytics 

Rubric templates library 

To v2.0 

Enterprise admin panels 

BYOK option 

API access 

ATS integrations 

Multi-language support 

11. Support Features 

11.1 In-Product Help 

Contextual tooltips 

Step-by-step guides 

Video tutorials (links) 

FAQ section 

11.2 Diagnostic Tools – use same ones ive built – Test API keys – verify  

Connection tester 

Permission validator 

Usage dashboard 

Error history 

12. Operational Features 

12.1 Monitoring 

Health checks every 60 seconds 

Usage metrics per customer 

Error tracking with context 

Cost monitoring with alerts 

12.2 Administrative 

Kill switch for service 

Rate limit adjustments 

Cost threshold management 

Audit log access 

 



 Key Points to be updated in the Proposal - Meeting (5th September) 

Only Google  

Payments: Google payments – Not stripe  

Which Model to use - TBC 

API Key Integration 

One of the main scope changes is that we’ll be using your dedicated API key instead of requiring users to input their own. 

This means we’ll update the App Script so that it no longer adds a user’s API key. Instead, it will call your internal API securely, locked down for internal use only. 

App Script Review & Updates 

In addition to the API key change, we will review the entire App Script to bring it up to current coding standards. 

This includes improving reliability and ensuring the logic is hardened for long-term use. 

Smart Document Matching 

The document matching feature (CV ↔ Cover Letter) is mostly working but can be inconsistent at times. 

The logic currently uses a tiered approach, but occasionally mismatches occur, e.g., the cover letter not appearing alongside the CV in the output sheet. 

We’ll refine and stabilise this so that matching works as expected. 

Enhanced Metrics Extraction 

There’s an existing “Company Metrics” tab that captures additional details (e.g., current employer, years of experience at the company, and ~15–20 other attributes). 

The idea is to explore whether we can enhance this by introducing a more user-friendly setup process—for example, a pop-up or menu early in the sheet setup where the user can select which metrics they’d like to include for scoring. 

Scoring Logic 

The core scoring mathematics is already in place. 

Our focus will be on reviewing the logic to ensure accuracy (no calculation errors) and making the process easier to configure. 

Free Trial – Upload PD, Create the Rubric and Process 3 CVs 

File Processing (CV upload) - word document, Text file and PDF 

Finalise the flow - To install the extension on other browsers  

Add dynamic FAQs 

Update the current RubiRecruit website to add pricing  

 

FLOW – Client Portal  

Step 1 – Get Started 

 

Step 2 – Only Google Sheets for RubiRecruit 

 

Step 3 – Sign in with Google account 

 

Step 4 -  

 

Step 5 – Install  

 

Step 6 -  

 

Step 7 – On Google Sheets – Extensions 

 

Step 8 – Launch  

 

Step – 8: we will not need all the menus displayed, but only accounts and billing, My workspace and any other menu as required.  

 

-> When sign in to our RubiRecruit – The display will be as such. 
Note: We will not require all the menu, we will need spreedsheet, plans and billing, settings and log out  

 

 